# SPDX-License-Identifier: 0BSD
#
# xz 软件包的简体中文翻译。
# Chinese translations for xz package
# This file is published under the BSD Zero Clause License.
# <PERSON><PERSON> <<EMAIL>>, 2019, 2022, 2023, 2024.
# <PERSON><PERSON> <<EMAIL>>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: xz-5.6.0-pre2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2024-08-27 13:08+0800\n"
"Last-Translator: <PERSON><PERSON> (Artoria2e5) <<EMAIL>>\n"
"Language-Team: Chinese (simplified) <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.4.4\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s：--block-list 的无效参数"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s：--block-list 得到过多参数"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "在 --block-list 中，块大小在过滤器链编号 '%c:' 之后缺失"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 仅可用于 --block-list 的最后一个元素"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s：未知文件格式类型"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s：不支持的完整性检查类型"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "仅可使用 '--files' 或 '--files0' 指定单个文件。"

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s：%s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "环境变量 %s 包含过多参数"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "压缩支持已在构建时禁用"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "解压支持已在构建时禁用"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "不支持对 lzip 文件 (.lz) 的压缩"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "除非压缩为 .xz 格式，--block-list 将被忽略"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "启用 --format-raw 选项时，必须指定 --suffix=.SUF 获知写入至标准输出"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "过滤器最多数量为四"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "在 --filters%s=过滤器 选项中出现错误："

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "内存用量限制对指定过滤器设置过低。"

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "--block-list 使用了过滤器链 %u，但未经由 --filters%u= 指定"

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "不推荐在 raw 模式使用预设等级。"

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "各个预设等级所使用的准确选项列表在不同软件版本之间可能不同。"

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr ".lzma 格式只支持 LZMA1 过滤器"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 无法用于 .xz 格式"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "过滤器链 %u 和 --flush-timeout 不兼容"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "因 --flush-timeout 而切换至单线程模式"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "过滤器链 %u 中存在不支持的选项"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "使用最多 %<PRIu32> 个线程。"

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "不支持的过滤器链或过滤器选项"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "解压缩需要 %s MiB 的内存。"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "已将所使用的线程数从 %s 减小为 %s，以不超出 %s MiB 的内存用量限制"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "已将所使用的线程数从 %s 减小为 1。这仍然超出了自动的内存使用限制 %s MiB。需要 %s MiB 的内存。继续操作。"

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "正在切换到单线程模式以不超出 %s MiB 的内存用量限制"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "已调整 LZMA%c 字典大小（从 %s MiB 调整为 %s MiB），以不超出 %s MiB 的内存用量限制"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "已调整 --filters%2$u LZMA%1$c 字典大小（从 %3$s MiB 调整为 %4$s MiB），以不超出 %5$s MiB 的内存用量限制"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "更改为过滤器链 %u 时出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "创建管道时出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s：poll() 失败：%s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s：文件似乎已移动，不再进行删除操作"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s：无法删除：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s：无法设置文件所有者：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s：无法设置文件所有组：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s：无法设置文件权限：%s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s：关闭文件失败：%s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s：关闭文件失败：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "从标准输入获取文件状态标志出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s：是符号链接，跳过"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s：是目录，跳过"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s：不是标准文件，跳过"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s：文件有设置用户ID或设置组ID标识，跳过"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s：文件有粘滞位标识，跳过"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s：输入文件有多于一个硬链接，跳过"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "空文件名，跳过"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "回复标准输入的状态标志时出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "获取标准输出的文件状态标志时出错：%s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s：关闭文件失败：%s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s：不是标准文件，跳过"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "恢复标准输出的 O_APPEND 标志时出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s：关闭文件失败：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s：尝试创建稀疏文件时 seek 失败：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s：读取错误：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s：seek 文件时出错：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s：未预期的文件结束"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s：写入错误：%s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "已禁用"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "物理内存（RAM）用量："

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "处理器线程数："

#: src/xz/hardware.c
msgid "Compression:"
msgstr "压缩："

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "解压缩："

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "多线程解压缩："

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "-T0 的默认值："

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "硬件信息："

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "内存使用限制："

#: src/xz/list.c
msgid "Streams:"
msgstr "流："

#: src/xz/list.c
msgid "Blocks:"
msgstr "块："

#: src/xz/list.c
msgid "Compressed size:"
msgstr "压缩后大小："

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "解压缩大小："

#: src/xz/list.c
msgid "Ratio:"
msgstr "比例："

#: src/xz/list.c
msgid "Check:"
msgstr "校验："

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "流填充大小："

#: src/xz/list.c
msgid "Memory needed:"
msgstr "所需内存："

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "头部存放大小："

#: src/xz/list.c
msgid "Number of files:"
msgstr "文件数量："

#: src/xz/list.c
msgid "Stream"
msgstr "流"

#: src/xz/list.c
msgid "Block"
msgstr "块"

#: src/xz/list.c
msgid "Blocks"
msgstr "块"

#: src/xz/list.c
msgid "CompOffset"
msgstr "压缩偏移量"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "解压偏移量"

#: src/xz/list.c
msgid "CompSize"
msgstr "压缩后大小"

#: src/xz/list.c
msgid "UncompSize"
msgstr "解压缩大小"

#: src/xz/list.c
msgid "TotalSize"
msgstr "总大小"

#: src/xz/list.c
msgid "Ratio"
msgstr "比例"

#: src/xz/list.c
msgid "Check"
msgstr "校验"

#: src/xz/list.c
msgid "CheckVal"
msgstr "校验值"

#: src/xz/list.c
msgid "Padding"
msgstr "填充"

#: src/xz/list.c
msgid "Header"
msgstr "头部"

#: src/xz/list.c
msgid "Flags"
msgstr "标志"

#: src/xz/list.c
msgid "MemUsage"
msgstr "内存用量"

#: src/xz/list.c
msgid "Filters"
msgstr "过滤器"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "无"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "未知-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "未知-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "未知-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "未知-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "未知-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "未知-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "未知-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "未知-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "未知-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "未知-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "未知-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "未知-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s：文件为空"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s：过小，不可能是有效的 .xz 文件"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "   流      块     压缩大小     解压大小   比例  校验    文件名"

#: src/xz/list.c
msgid "Yes"
msgstr "是"

#: src/xz/list.c
msgid "No"
msgstr "否"

#: src/xz/list.c
#, fuzzy
#| msgid "  Minimum XZ Utils version: %s\n"
msgid "Minimum XZ Utils version:"
msgstr "  最低 XZ Utils 版本：%s\n"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s 文件\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "总计："

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list 仅适用于 .xz 文件（--format=xz 或 --format=auto）"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "试试用“lzmainfo”处理 .lzma 文件。"

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list 不支持从标准输入读取"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s：读取文件名列表时出错：%s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s：读取文件名列表时遇到未预期的输入结束"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s：读取文件名列表时获得了空字符；您可能想要使用 '--files0' 而非 '--files'？"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "尚不支持带 --robot 的压缩和解压缩。"

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "无法同时从标准输入读取数据和文件名列表"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s："

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "内部错误（bug）"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "无法建立信号处理器"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "无完整性检查；将不验证文件完整性"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "不支持的完整性检查类型；将不验证文件完整性"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "达到内存使用限制"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "无法识别文件格式"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "不支持的选项"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "压缩数据已损坏"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "输入意外结束"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "需要 %s MiB 的内存空间。限制已禁用。"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "需要 %s MiB 的内存空间。限制为 %s。"

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s：过滤器链：%s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "请尝试执行 '%s --help' 来获取更多信息。"

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "Usage: %s [OPTION]... [FILE]...\n"
#| "Compress or decompress FILEs in the .xz format.\n"
#| "\n"
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""
"用法：%s [选项]... [文件]...\n"
"使用 .xz 格式压缩或解压缩文件。\n"
"\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "必选参数对长短选项同时适用。\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr " 操作模式：\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force compression"
msgstr "解压缩："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force decompression"
msgstr "解压缩："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
" 操作修饰符：\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "写入标准输出失败"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --single-stream decompress only the first stream, and silently\n"
#| "                      ignore possible remaining input data"
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "      --single-stream 仅解压缩第一个流，忽略其后可能继续出现的输入数据"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Basic file format and compression options:\n"
msgid "Basic file format and compression options:"
msgstr ""
"\n"
" 基本文件格式和压缩选项：\n"

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --ignore-check  don't verify the integrity check when decompressing"
msgid "don't verify the integrity check when decompressing"
msgstr "      --ignore-check  解压缩时不要进行完整性检查验证"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -0 ... -9           compression preset; default is 6; take compressor *and*\n"
#| "                      decompressor memory usage into account before using 7-9!"
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""
"  -0 ... -9           压缩预设等级；默认为 6；使用 7-9 的等级之前，请先考虑\n"
"                      压缩和解压缩所需的内存用量！（会占用大量内存空间）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -e, --extreme       try to improve compression ratio by using more CPU time;\n"
#| "                      does not affect decompressor memory requirements"
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""
"  -e, --extreme       尝试使用更多 CPU 时间来改进压缩比率；\n"
"                      不会影响解压缩的内存需求量"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -T, --threads=NUM   use at most NUM threads; the default is 0 which uses\n"
#| "                      as many threads as there are processor cores"
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""
"  -T, --threads=数量  使用最多指定数量的线程；默认值为 0，即\n"
"                      可以使用与处理器内核数量相同的线程数"

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-size=SIZE\n"
#| "                      start a new .xz block after every SIZE bytes of input;\n"
#| "                      use this to set the block size for threaded compression"
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""
"      --block-size=块大小\n"
"                      输入每读取指定块大小的数据后即开始一个新的 .xz 块；\n"
"                      使用该选项可以设置多线程压缩中的块大小"

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-list=BLOCKS\n"
#| "                      start a new .xz block after the given comma-separated\n"
#| "                      intervals of uncompressed data; optionally, specify a\n"
#| "                      filter chain number (0-9) followed by a ':' before the\n"
#| "                      uncompressed data size"
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""
"      --block-list=块大小列表\n"
"                      在所给出的未压缩数据间隔大小的数据之后开始一个新的\n"
"                      .xz 块（使用逗号分隔）\n"
"                      可选：在未压缩大小之前提供一个过滤器链号（0-9），用\n"
"                      “:”和大小分开"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --flush-timeout=TIMEOUT\n"
#| "                      when compressing, if more than TIMEOUT milliseconds has\n"
#| "                      passed since the previous flush and reading more input\n"
#| "                      would block, all pending data is flushed out"
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""
"      --flush-timeout=超时时间\n"
"                      进行压缩时，如果从上次刷洗输出之后经过了指定的超时时间\n"
"                      且读取更多数据会被阻塞，则刷洗输出所有缓冲数据"

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --no-adjust     if compression settings exceed the memory usage limit,\n"
#| "                      give an error instead of adjusting the settings downwards"
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "      --no-adjust     如果压缩设置超出内存用量限制，不调整设置而直接报错"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Custom filter chain for compression (alternative for using presets):"
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""
"\n"
" 用于压缩的自定义过滤器链（不使用预设等级时的备选用法）："

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| "  --filters=FILTERS   set the filter chain using the liblzma filter string\n"
#| "                      syntax; use --filters-help for more information"
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""
"\n"
"--filters=FILTERS     使用 liblzma 过滤器字符串语法设置过滤器链\n"
"                      使用 --filters-help 了解更多信息"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters1=FILTERS ... --filters9=FILTERS\n"
#| "                      set additional filter chains using the liblzma filter\n"
#| "                      string syntax to use with --block-list"
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""
"--filters1=过滤器 ... --filters9=过滤器\n"
"                      使用 liblzma 过滤器语法设置其他过滤器链，\n"
"                      与 --block-list 一起使用的"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters-help      display more information about the liblzma filter string\n"
#| "                      syntax and exit."
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""
"--filters-help        显示有关 liblzma 过滤器字符串语法的更多信息\n"
"                      然后退出。"

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Number of processor threads:"
msgid "number of position bits"
msgstr "处理器线程数："

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "compression mode"
msgstr "解压缩："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
" 其它选项：\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -Q, --no-warn       make warnings not affect the exit status"
msgid "make warnings not affect the exit status"
msgstr "  -Q, --no-warn       使得警告信息不影响程序退出返回值"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr "      --robot         使用机器可解析的信息（对于脚本有用）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --info-memory   display the total amount of RAM and the currently active\n"
#| "                      memory usage limits, and exit"
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "      --info-memory   显示 RAM 总量和当前配置的内存用量限制，然后退出"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr "  -V, --version       显示软件版本号并退出"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"如果没有指定文件，或者文件为\"-\"，则从标准输入读取。\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr ""
"请使用英文或芬兰语向 <%s> 报告软件错误。\n"
"请使用中文向 TP 简体中文翻译团队 <<EMAIL>>\n"
"报告软件的简体中文翻译错误。\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "%s 主页：<%s>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "这是开发版本，不适用于生产环境使用。"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy, c-format
#| msgid ""
#| "Filter chains are set using the --filters=FILTERS or\n"
#| "--filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain\n"
#| "can be separated by spaces or '--'. Alternatively a preset <0-9>[e] can be\n"
#| "specified instead of a filter chain.\n"
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""
"设置过滤器链使用 --filters=FILTERS 或\n"
"--filters1=FILTER … --filters9=FILTER选项。链中的每个过滤器\n"
"可以用空格或“--”分隔。或者可以指定预设 <0-9>[e]\n"
"而不是过滤器链。\n"
"</0-9>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "支持的筛选器及其选项包括："

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be 'name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s：选项必须按照 '名称=值' 的格式成对出现，使用半角逗号分隔"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s：无效的选项名称"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s：无效的选项值"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "不支持的 LZMA1/LZMA2 预设等级：%s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "lc 和 lp 的和必须不大于 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s：文件名有未知后缀，跳过"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s：文件已有 '%s' 后缀名，跳过"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s：无效的文件名后缀"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s：值不是非负十进制整数"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s：无效的乘数后缀"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "有效的后缀包括“KiB”（2^10）、“MiB”（2^20）和“GiB”（2^30）。"

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "选项“%s”的值必须位于 [%<PRIu64>, %<PRIu64>] 范围内"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "压缩数据不能从终端读取"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "压缩数据不能向终端写入"

#: src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""
"用法：%s [--help] [--version] [文件]...\n"
"显示存储在 .lzma 文件头中的信息"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Show information stored in the .lzma file header."
msgstr ""
"用法：%s [--help] [--version] [文件]...\n"
"显示存储在 .lzma 文件头中的信息"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "%s：过小，不可能是有效的 .lzma 文件"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "不是 .lzma 文件"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "写入标准输出失败"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "未知错误"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "不支持的选项"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "不支持的过滤器链或过滤器选项"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s：无效的选项名称"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s：无效的乘数后缀"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s：未知文件格式类型"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "LZMA1 无法用于 .xz 格式"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "过滤器最多数量为四"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -z, --compress      force compression\n"
#~ "  -d, --decompress    force decompression\n"
#~ "  -t, --test          test compressed file integrity\n"
#~ "  -l, --list          list information about .xz files"
#~ msgstr ""
#~ "  -z, --compress      强制压缩\n"
#~ "  -d, --decompress    强制解压缩\n"
#~ "  -t, --test          测试压缩文件完整性\n"
#~ "  -l, --list          列出 .xz 文件的信息"

#~ msgid ""
#~ "  -k, --keep          keep (don't delete) input files\n"
#~ "  -f, --force         force overwrite of output file and (de)compress links\n"
#~ "  -c, --stdout        write to standard output and don't delete input files"
#~ msgstr ""
#~ "  -k, --keep          保留（不要删除）输入文件\n"
#~ "  -f, --force         强制覆写输出文件和（解）压缩链接\n"
#~ "  -c, --stdout        向标准输出写入，同时不要删除输入文件"

#~ msgid ""
#~ "      --no-sparse     do not create sparse files when decompressing\n"
#~ "  -S, --suffix=.SUF   use the suffix '.SUF' on compressed files\n"
#~ "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~ "                      omitted, filenames are read from the standard input;\n"
#~ "                      filenames must be terminated with the newline character\n"
#~ "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgstr ""
#~ "      --no-sparse     解压缩时不要创建稀疏文件\n"
#~ "  -S, --suffix=.SUF   压缩文件使用指定的“.SUF”后缀名\n"
#~ "      --files[=文件]  从指定文件读取要处理的文件名列表；如果省略了指定文件名，\n"
#~ "                      将从标准输入读取文件名列表；文件名必须使用换行符分隔、终止\n"
#~ "      --files0[=文件] 类似 --files，但使用空（\\0）字符进行分隔"

#~ msgid ""
#~ "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~ "                      'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'\n"
#~ "  -C, --check=CHECK   integrity check type: 'none' (use with caution),\n"
#~ "                      'crc32', 'crc64' (default), or 'sha256'"
#~ msgstr ""
#~ "  -F, --format=格式   要编码或解码的文件格式；可能的值包括\n"
#~ "                      “auto”（默认）、“xz”、“lzma”、\n"
#~ "                      “lzip”和“raw”\n"
#~ "  -C, --check=类型    完整性检查类型：“none”（请谨慎使用）、\n"
#~ "                      “crc32”、“crc64”（默认）或“sha256”"

#, no-c-format
#~ msgid ""
#~ "      --memlimit-compress=LIMIT\n"
#~ "      --memlimit-decompress=LIMIT\n"
#~ "      --memlimit-mt-decompress=LIMIT\n"
#~ "  -M, --memlimit=LIMIT\n"
#~ "                      set memory usage limit for compression, decompression,\n"
#~ "                      threaded decompression, or all of these; LIMIT is in\n"
#~ "                      bytes, % of RAM, or 0 for defaults"
#~ msgstr ""
#~ "      --memlimit-compress=限制用量\n"
#~ "      --memlimit-decompress=限制用量\n"
#~ "      --memlimit-mt-decompress=限制用量\n"
#~ "  -M, --memlimit=限制用量\n"
#~ "                      设置压缩、解压缩、多线程解压缩或者共同的内存用量限制；\n"
#~ "                      所指定限制量单位为字节，或以百分号 % 结尾表示内存比例，\n"
#~ "                      或者指定 0 取软件默认值"

#~ msgid ""
#~ "\n"
#~ "  --lzma1[=OPTS]      LZMA1 or LZMA2; OPTS is a comma-separated list of zero or\n"
#~ "  --lzma2[=OPTS]      more of the following options (valid values; default):\n"
#~ "                        preset=PRE reset options to a preset (0-9[e])\n"
#~ "                        dict=NUM   dictionary size (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=NUM     number of literal context bits (0-4; 3)\n"
#~ "                        lp=NUM     number of literal position bits (0-4; 0)\n"
#~ "                        pb=NUM     number of position bits (0-4; 2)\n"
#~ "                        mode=MODE  compression mode (fast, normal; normal)\n"
#~ "                        nice=NUM   nice length of a match (2-273; 64)\n"
#~ "                        mf=NAME    match finder (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=NUM  maximum search depth; 0=automatic (default)"
#~ msgstr ""
#~ "\n"
#~ "  --lzma1[=选项]      LZMA1 或 LZMA2；指定选项是用逗号分隔的下列选项的组合，\n"
#~ "  --lzma2[=选项]      值应当为零或大于零（有效值；默认值）：\n"
#~ "                        preset=PRE 将选项重置为预设配置 (0-9[e])\n"
#~ "                        dict=数字  字典大小 (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=数字    literal context 位的数量 (0-4; 3)\n"
#~ "                        lp=数字    literal position 位的数量 (0-4; 0)\n"
#~ "                        pb=数字    position 位的数量 (0-4; 2)\n"
#~ "                        mode=模式  压缩模式 (fast, normal; normal)\n"
#~ "                        nice=数字  匹配的 nice 值 (2-273; 64)\n"
#~ "                        mf=名称    匹配搜索器 match finder\n"
#~ "                                   (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=数字 最大搜索深度； 0=自动（默认）"

#~ msgid ""
#~ "\n"
#~ "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~ "  --arm[=OPTS]        ARM BCJ filter\n"
#~ "  --armthumb[=OPTS]   ARM-Thumb BCJ filter\n"
#~ "  --arm64[=OPTS]      ARM64 BCJ filter\n"
#~ "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~ "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~ "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~ "  --riscv[=OPTS]      RISC-V BCJ filter\n"
#~ "                      Valid OPTS for all BCJ filters:\n"
#~ "                        start=NUM  start offset for conversions (default=0)"
#~ msgstr ""
#~ "\n"
#~ "  --x86[=选项]        x86 BCJ 过滤器（32 位和 64 位）\n"
#~ "  --arm[=选项]        ARM BCJ 过滤器\n"
#~ "  --armthumb[=选项]   ARM-Thumb BCJ 过滤器\n"
#~ "  --arm64[=选项]      ARM64 BCJ 过滤器\n"
#~ "  --powerpc[=选项]    PowerPC BCJ 过滤器（仅大端序）\n"
#~ "  --ia64[=选项]       IA-64 (Itanium，安腾) BCJ 过滤器\n"
#~ "  --sparc[=选项]      SPARC BCJ 过滤器\n"
#~ "  --riscv[=选项]      RISC-V BCJ 过滤器\n"
#~ "                      所有过滤器可用选项：\n"
#~ "                        start=数字  转换的起始偏移量（默认=0）"

#~ msgid ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta filter; valid OPTS (valid values; default):\n"
#~ "                        dist=NUM   distance between bytes being subtracted\n"
#~ "                                   from each other (1-256; 1)"
#~ msgstr ""
#~ "\n"
#~ "  --delta[=选项]      增量过滤器；有效选项（有效值；默认值）：\n"
#~ "                        dist=NUM   相减的字节之间的距离 (1-256; 1)"

#~ msgid ""
#~ "  -q, --quiet         suppress warnings; specify twice to suppress errors too\n"
#~ "  -v, --verbose       be verbose; specify twice for even more verbose"
#~ msgstr ""
#~ "  -q, --quiet         不显示警告信息；指定两次可不显示错误信息\n"
#~ "  -v, --verbose       输出详细信息；指定两次可以输出更详细的信息"

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ "  -h, --help          显示短帮助信息（仅列出基本选项）\n"
#~ "  -H, --long-help     显示本长帮助信息"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ "  -h, --help          显示本短帮助信息并退出\n"
#~ "  -H, --long-help     显示长帮助信息（同时列出高级选项）"

#~ msgid "Failed to enable the sandbox"
#~ msgstr "沙盒启用失败"

#~ msgid "The selected match finder requires at least nice=%<PRIu32>"
#~ msgstr "所选中的匹配搜索器（match finder）至少需要 nice=%<PRIu32>"

#~ msgid "Sandbox is disabled due to incompatible command line arguments"
#~ msgstr "沙盒已因不兼容的命令行参数而禁用"

#~ msgid "Sandbox was successfully enabled"
#~ msgstr "已成功启用沙盒"

#~ msgid "Memory usage limit for compression:    "
#~ msgstr "用于压缩的内存用量限制：  "

#~ msgid "  Streams:            %s\n"
#~ msgstr "  流：                %s\n"

#~ msgid "  Blocks:             %s\n"
#~ msgstr "  块：                %s\n"

#~ msgid "  Ratio:              %s\n"
#~ msgstr "  压缩比：            %s\n"

#~ msgid "  Check:              %s\n"
#~ msgstr "  校验方式：          %s\n"

#~ msgid ""
#~ "  Streams:\n"
#~ "    Stream    Blocks      CompOffset    UncompOffset        CompSize      UncompSize  Ratio  Check      Padding"
#~ msgstr ""
#~ "  流：\n"
#~ "       流         块      压缩偏移量      解压偏移量        压缩大小        解压大小   比例   校验         填充"

#~ msgid ""
#~ "  Blocks:\n"
#~ "    Stream     Block      CompOffset    UncompOffset       TotalSize      UncompSize  Ratio  Check"
#~ msgstr ""
#~ "  块：\n"
#~ "        流        块      压缩偏移量      解压偏移量        总计大小        解压大小   比例   校验"

#~ msgid "      CheckVal %*s Header  Flags        CompSize    MemUsage  Filters"
#~ msgstr "      CheckVal %*s   头部   标记        压缩大小    内存使用   过滤器"
