# SPDX-License-Identifier: 0BSD
#
# Spanish translation for xz-5.8.0-pre1.
# Copyright (C) 2024, 2025 The XZ Utils authors and contributors
# This file is published under the BSD Zero Clause License.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022, 2023, 2024, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: xz 5.8.0-pre1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2025-05-14 14:23-0600\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Argumento inválido para --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Demasiados argumentos para --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "En --block-list, falta el tamaño de bloque después del número de cadena de filtros '%c:'"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 solo se puede usar como el último elemento en --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Tipo de formato de fichero desconocido"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: No se admite el tipo de verificación de integridad"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Solo se puede especificar un fichero con '--files' o '--files0'."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "La variable de ambiente %s contiene demasiados argumentos"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Se desactivó el soporte para compresión en el momento de compilación"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Se desactivó el soporte para descompresión en el momento de compilación"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "No se admite la compresión de ficheros lzip (.lz)"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list se descarta a menos que se comprima con el formato .xz"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Con --format=raw, se requiere --suffix=.SUF a menos que se escriba a la salida estándar"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "El número máximo de filtros es cuatro"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Error en la opción --filters%s=FILTROS:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "El límite de uso de memoria es demasiado bajo para la configuración de filtros dada."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "la cadena de filtros %u es usada por --block-list pero no se especifica con --filters%u="

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "No se recomienda un modo predeterminado en modo crudo."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "El número exacto de las opciones predeterminadas puede variar entre versiones del software."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "El formato .lzma solamente admite el filtro LZMA1"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "No se puede usar LZMA1 con el formato .xz"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "La cadena de filtros %u es incompatible con --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Se cambia al modo de un solo hilo debido a --flush-timeout"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Opciones sin soporte en la cadena de filtros %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Se usan hasta %<PRIu32> hilos."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "No se admiten las opciones de cadena de filtros o de filtro"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "La descompresión necesitará %s MiB de memoria."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Se reduce el número de hilos de %s a %s para no exceder el límite de uso de memoria de %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Se reduce el número de hilos de %s a uno. Aún se está excediendo el límite automático de uso de memoria de %s MiB. Se requieren %s MiB de memoria. Continúa de cualquier manera."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Se ajusta al modo de un solo hilo para no exceder el límite de uso de memoria de %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Se ajusta el tamaño del diccionario LZMA%c de %s MiB a %s MiB para no exceder el límite de uso de memoria de %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Se ajusta el tamaño del diccionario LZMA%c para --filters%u de %s MiB a %s MiB para no exceder el límite de uso de memoria de %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Error al cambiar a la cadena de filtros %u: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Error al crear una tubería: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: falló poll(): %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Al parecer se movió el fichero, no se borra"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: No se puede borrar: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: No se puede establecer el propietario del fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: No se puede establecer el grupo del fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: No se pueden establecer los permisos del fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Falló al sincronizar el fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Falló al sincronizar el directorio del fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Error al obtener la opciones de estado de fichero de la entrada estándar: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Es un enlace simbólico, se salta"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Es un directorio, se salta"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: No es un fichero regular, se salta"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: El fichero tiene el bit setuid o setgid activo, se salta"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: El fichero tiene el bit sticky activo, se salta"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: El fichero de entrada tiene más de un enlace duro, se salta"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Nombre de fichero vacío, se salta"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Error al restaurar las opciones de estado en la entrada estándar: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Error al obtener las opciones de estado de fichero de la entrada estándar: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Falló al abrir el directorio: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Destination is not a regular file"
msgstr "%s: El destino no es un fichero regular"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Error al restaurar la opción O_APPEND a la salida estándar: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Falló al cerrar el fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Falló la búsqueda al tratar de crear un fichero disperso: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Error de lectura: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Error al buscar en el fichero: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Fin de fichero inesperado"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Error de escritura: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Desactivado"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Cantidad total de memoria física (RAM):"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Número de hilos de procesador:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Compresión"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Descompresión:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Descompresión multihilos:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Por omisión para -T0:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Información de hardware:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Límites de uso de memoria:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Flujos:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Bloques:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Tamaño comprimido:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Tamaño sin comprimir:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Tasa:"

#: src/xz/list.c
msgid "Check:"
msgstr "Verificación:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Relleno de flujo:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Memoria requerida:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Tamaños en cabeceras:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Número de ficheros:"

#: src/xz/list.c
msgid "Stream"
msgstr "Flujo"

#: src/xz/list.c
msgid "Block"
msgstr "Bloque"

#: src/xz/list.c
msgid "Blocks"
msgstr "Bloques"

#: src/xz/list.c
msgid "CompOffset"
msgstr "DesplComp"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "DesplDescomp"

#: src/xz/list.c
msgid "CompSize"
msgstr "TamComp"

#: src/xz/list.c
msgid "UncompSize"
msgstr "TamDescomp"

#: src/xz/list.c
msgid "TotalSize"
msgstr "TamTotal"

#: src/xz/list.c
msgid "Ratio"
msgstr "Tasa"

#: src/xz/list.c
msgid "Check"
msgstr "Verif"

#: src/xz/list.c
msgid "CheckVal"
msgstr "ValVerif"

#: src/xz/list.c
msgid "Padding"
msgstr "Relleno"

#: src/xz/list.c
msgid "Header"
msgstr "Cabecera"

#: src/xz/list.c
msgid "Flags"
msgstr "Opciones"

#: src/xz/list.c
msgid "MemUsage"
msgstr "UsoMem"

#: src/xz/list.c
msgid "Filters"
msgstr "Filtros"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Ninguno"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Descon-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Descon-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Descon-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Descon-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Descon-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Descon-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Descon-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Descon-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Descon-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Descon-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Descon-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Descon-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: El fichero está vacío"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Demasiado pequeño para ser un fichero .xz válido"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Flujos Bloques  Comprimido Sin-Comprimir Tasa   Verif   Nombre-Fichero"

#: src/xz/list.c
msgid "Yes"
msgstr "Sí"

#: src/xz/list.c
msgid "No"
msgstr "No"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr "Versión mínima de Herramientas XZ:"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s fichero\n"
msgstr[1] "%s ficheros\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Totales:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list solo funciona con ficheros .xz (--format=xz o --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Pruebe 'lzmainfo' con ficheros .lzma."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list no admite leer de la entrada estándar"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Error al leer nombres de fichero: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Fin de entrada inesperada al leer nombres de fichero"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Se encontraron caracteres nulos al leer nombres de ficheros. ¿Tal vez quería usar '--files0' en lugar de '--files'?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Aún no se admite la compresión y descompresión con --robot."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "No se pueden leer datos de la entrada estándar cuando se leen nombres de fichero de la entrada estándar"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Error interno (bug)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "No se pueden establecer los manejadores de señales"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "No hay revisión de integridad; no se verifica la integridad del fichero"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "No se admite el tipo de revisión de integridad; no se verifica la integridad del fichero"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Se alcanzó el límite de uso de memoria"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "No se reconoce el formato del fichero"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Opciones sin soporte"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Los datos comprimidos están corruptos"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Fin de entrada inesperado"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "Se requieren %s MiB de memoria. Se desactiva el limitador."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "Se requieren %s MiB de memoria. El límite es %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Cadena de filtro: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "Pruebe '%s --help' para obtener más información."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr "Error al mostrar el texto de ayuda (código de error %d)"

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr "Uso: %s [OPCIÓN]... [FICHERO]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr "Comprime o descomprime FICHEROs en el formato .xz."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "Los argumentos obligatorios para las opciones largas también son obligatorios para las opciones cortas."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation mode:"
msgstr "Modo de operación:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force compression"
msgstr "fuerza la compresión"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force decompression"
msgstr "fuerza la descompresión"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr "prueba la integridad del fichero comprimido"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr "muestra la información acerca de los ficheros .xz"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation modifiers:"
msgstr "Modificadores de operación:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr "conserva (no borra) los ficheros de entrada"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr "fuerza la sobreescritura del fichero de salida y (des)comprime enlaces"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "write to standard output and don't delete input files"
msgstr "escribe a la salida estándar y no borra los ficheros de entrada"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr "no sincroniza el fichero de salida con el dispositivo de almacenamiento antes de borrar el fichero de entrada"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "solo descomprime el primer flujo, y descarta silenciosamente los posibles datos de entrada restantes"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr "no crea archivos temporales al descomprimir"

#: src/xz/message.c
msgid ".SUF"
msgstr ".SUF"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr "usa el sufijo '.SUF' para los ficheros comprimidos"

#: src/xz/message.c
msgid "FILE"
msgstr "FICHERO"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr "lee los nombres de fichero a procesar del FICHERO; si se omite el FICHERO, los nombres de fichero se leen de la entrada estándar; los nombres de fichero deber de terminar con el carácter de línea nueva"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr "como --files pero usa el carácter nulo como terminador"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr "Opciones básicas de compresión y formato de fichero:"

#: src/xz/message.c
msgid "FORMAT"
msgstr "FORMATO"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr "formato de fichero para codificar o decodificar; los valores posibles son 'auto' (por defecto), 'xz', 'lzma', 'lzip', y 'raw'"

#: src/xz/message.c
msgid "NAME"
msgstr "NOMBRE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr "tipo de verificación de integridad: 'none' (usar con cuidado), 'crc32', 'crc64' (por defecto), o 'sha256'"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr "no hace la verificación de integridad al descomprimir"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr "valor predefinido de compresión; por defecto es 6. ¡Considere el compresor *y* el uso de memoria del compresor antes de usar 7-9!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr "trata de mejorar la razón de compresión usando más tiempo de procesamiento; no afecta los requisitos de memoria del descompresor"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr "NÚM"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr "usa como máximo NÚM hilos; por defecto es 0, el cual usa tantos hilos como hayan núcleos de procesador"

#: src/xz/message.c
msgid "SIZE"
msgstr "TAMAÑO"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr "inicia un nuevo bloque .xz después de cada TAMAÑO bytes de entrada; use esta opción para establecer el tamaño de bloque para la compresión con hilos"

#: src/xz/message.c
msgid "BLOCKS"
msgstr "BLOQUES"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr "inicia un nuevo bloque .xz después de cada intervalo dado, separado por comas, de datos sin comprimir; opcionalmente, especifica un número de cadena de filtros (0-9) a continuación de ':' antes del tamaño de los datos sin comprimir"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr "al comprimir, si pasaron más de NÚM milisegundos desde el último descarte y la lectura de más entrada produciría un bloqueo, todos los datos pendientes son descartados"

#: src/xz/message.c
msgid "LIMIT"
msgstr "LÍMITE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr "define el límite de uso de memoria para compresión, descompresión, descompresión con hilos, o todos los anteriores; el LÍMITE es en bytes, % de RAM, o 0 para valores por defecto"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "si la configuración de compresión excede el límite de uso de memoria, muestra un error en lugar de ajustar los valores hacia abajo"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr "Cadena de filtros personal para compresión (alternativa a usar valores predefinidos):"

#: src/xz/message.c
msgid "FILTERS"
msgstr "FILTROS"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr "define la cadena de filtros usando la sintaxis de la cadena de filtros liblzma; use --filters-help para obtener más información"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr "define cadenas de filtros adicionales usando la sintaxis de cadena de filtros liblzma para usar con --block-list"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr "muestra más información acerca de la sintaxis de cadenas de filtros liblzma y termina"

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr "OPCS"

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr "LZMA1 o LZMA2: OPCS es una lista separada por comas de cero o más de las siguientes opciones (valores válidos; por defecto):"

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr "PRE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr "cambia las opciones al valor predefinido"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr "tamaño de diccionario"

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr "número de bits de contexto literal"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr "número de bits de posición literal"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr "número de bits de posición"

#: src/xz/message.c
msgid "MODE"
msgstr "MODO"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression mode"
msgstr "modo de descompresión"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr "longitud aceptada de una coincidencia"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr "buscador de coincidencias"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr "profundidad máxima de búsqueda; 0=automática (por defecto)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr "filtro BCJ de x86 (32-bit y 64-bit)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr "filtro BCJ de ARM"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr "filtro BCJ de ARM-Thumb"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr "filtro BCJ de ARM64"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr "filtro BCJ de PowerPC (sólo big endian)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr "filtro BCJ de IA-64 (Itanium)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr "filtro BCJ de Sparc"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr "filtro BCJ de RISC-V"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr "OPCS válidas para todos los filtros BCJ:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr "desplazamiento de inicio para las conversiones (por defecto=0)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr "Delta de filtro; OPCS válidas (valores válidos; por defecto):"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr "distancia entre bytes que se restan unos de otros"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Other options:"
msgstr "Otras opciones:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr "suprime los avisos; especificar dos veces para suprimir también los errores"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr "descriptivo; especificar dos veces para ser aún más descriptivo"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr "los avisos no afectan el estado de la salida"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use machine-parsable messages (useful for scripts)"
msgstr "usa mensajes analizables por máquina (útil para scripts)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "muestra la cantidad total de RAM y los límites de uso de memoria activos, y termina"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr "muestra la ayuda corta (sólo muestra las opciones básicas)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr "muestra esta ayuda larga y termina"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr "muestra esta ayuda corta y termina"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr "muestra la ayuda larga (también muestra las opciones avanzadas)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the version number and exit"
msgstr "muestra el número de versión y termina"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "With no FILE, or when FILE is -, read standard input."
msgstr "Sin FICHEROs, o cuando el FICHERO es -, lee la entrada estándar."

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr ""
"Reporte errores a <%s> (en inglés o finlandés).\n"
"Reporte errores de traducción al español a <<EMAIL>>."

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s home page: <%s>"
msgstr "Sitio web de %s: <%s>"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "ESTA ES UNA VERSIÓN EN DESARROLLO Y NO ESTÁ LISTA PARA USO EN PRODUCCIÓN."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr "Las cadenas de filtros se definen usando las opciones --filters=FILTROS o --filters1=FILTROS ... --filters9=FILTROS. Cada filtro en la cadena se puede separar con espacios o '--'. Alternativamente, se puede especificar un valor predeterminado %s en lugar de una cadena de filtro."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "Los filtros admitidos y sus opciones son:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "Las opciones deben ser pares 'nombre=valor' separadas por comas"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Nombre de opción inválido"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Invalid option value"
msgstr "Valor de opción inválido"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "No se admite el valor predefinido LZMA1/LZMA2: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "La suma de lc y lp no debe exceder 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: El nombre de fichero tiene un sufijo desconocido, se salta"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: El fichero ya tiene un sufijo '%s', se salta"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Sufijo de nombre de fichero inválido"

#: src/xz/util.c src/liblzma/common/string_conversion.c
msgid "Value is not a non-negative decimal integer"
msgstr "El valor no es un entero decimal no-negativo"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Sufijo multiplicador inválido"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Los sufijos válidos son 'KiB' (2^10), 'MiB' (2^20), y 'GiB' (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "El valor de la opción '%s' debe estar en el intervalo [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "No se pueden leer datos comprimidos de una terminal"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "No se pueden escribir datos comprimidos a una terminal"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr "Uso: %s [--help] [--version] [FICHERO]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr "Muestra información almacenada en la cabecera del fichero .lzma"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "El fichero es demasiado pequeño para ser un fichero .lzma"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "No es un fichero .lzma"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Falló la escritura a la salida estándar"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Error desconocido"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported preset"
msgstr "Opción sin soporte"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported flag in the preset"
msgstr "Opción sin soporte en el valor predefinido"

#: src/liblzma/common/string_conversion.c
msgid "Unknown option name"
msgstr "Nombre de opción inválido"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr "El valor de la opción no puede estar vacío"

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr "Valor fuera de intervalo"

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr "Esta opción no admite ningún sufijo multiplicador"

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "Sufijo multiplicador inválido (KiB, MiB, o GiB)"

#: src/liblzma/common/string_conversion.c
msgid "Unknown filter name"
msgstr "Nombre de filtro desconocido"

#: src/liblzma/common/string_conversion.c
msgid "This filter cannot be used in the .xz format"
msgstr "Este filtro no se puede usar en el formato .xz"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr "Falló el alojamiento de memoria"

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr "No se permite una cadena vacía, pruebe con '6' si se requiere un valor por defecto"

#: src/liblzma/common/string_conversion.c
msgid "The maximum number of filters is four"
msgstr "El número máximo de filtros es cuatro"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr "Falta el nombre del filtro"

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr "Cadena de filtro inválida (¿falta 'lzma2' al final?)"
