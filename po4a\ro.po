# SPDX-License-Identifier: 0BSD
#
# Romanian translation for xz-man.
# Mesajele în limba română pentru manualul pachetului XZ Utils.
# This file is published under the BSD Zero Clause License.
#
# Remus-<PERSON> <<EMAIL>>, 2022 - 2025.
#
# Cronologia traducerii fișierului „xz-man”:
# Traducerea inițială, făcută de R-GC, pentru versiunea xz-man 5.4.0-pre1.
# Actualizare a traducerii pentru versiunea 5.4.0-pre2, făcută de R-GC, dec-2022.
# Actualizare a traducerii pentru versiunea 5.4.3, făcut<PERSON> de R-GC, mai-2023.
# Actualizare a traducerii pentru versiunea 5.4.4-pre1, făcută de R-GC, iul-2023.
# Actualizare a traducerii pentru versiunea 5.6.0-pre1, făcută de R-GC, feb-2024.
# Actualizare a traducerii pentru versiunea 5.6.0-pre2, făcută de R-GC, feb-2024.
# Actualizare a traducerii pentru versiunea 5.7.1-dev1, făcută de R-GC, ian-2025.
# Actualizare a traducerii pentru versiunea 5.8.0-pre1, făcută de R-GC, mar-2025.
# Actualizare a traducerii pentru versiunea Y, făcută de X, Z(luna-anul).
#
msgid ""
msgstr ""
"Project-Id-Version: xz-man 5.8.0-pre1\n"
"POT-Creation-Date: 2025-03-08 14:50+0200\n"
"PO-Revision-Date: 2025-03-09 20:57+0100\n"
"Last-Translator: Remus-Gabriel Chelu <<EMAIL>>\n"
"Language-Team: Romanian <<EMAIL>>\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: plural=3; plural=(n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < 20)) ? 1 : 2);\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.5\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr "8 martie 2025"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "Utilități XZ"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "NUME"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat - Comprimă sau decomprimă fișiere .xz și .lzma"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "REZUMAT"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<opțiune...>] [I<fișier...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "ALIAS COMENZI"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> este echivalent cu B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> este echivalent cu B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> este echivalent cu B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> este echivalent cu B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> este echivalent cu B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Când scrieți scripturi care trebuie să decomprime fișiere, este recomandat să folosiți întotdeauna comanda B<xz> cu argumentele adecvate (B<xz -d> sau B<xz -dc>) în loc de comenzile B<unxz> și B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "DESCRIERE"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> este un instrument de comprimare a datelor de uz general cu sintaxă de linie de comandă similară cu B<gzip>(1) și B<bzip2>(1). Formatul de fișier nativ este formatul B<.xz>, dar formatul vechi B<.lzma> folosit de LZMA Utils și fluxurile comprimate brute fără anteturi de format container sunt de asemenea acceptate. În plus, este acceptată decomprimarea formatului B<.lz> folosit de B<lzip>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> comprimă sau decomprimă fiecare I<fișier> în funcție de modul de operare selectat. Dacă nu sunt date I<fișiere> sau I<fișier> este B<->, B<xz> citește de la intrarea standard și scrie datele procesate la ieșirea standard. B<xz> va refuza (afișează o eroare și omite I<fișier>) să scrie date comprimate la ieșirea standard dacă este un terminal. În mod similar, B<xz> va refuza să citească datele comprimate de la intrarea standard dacă este un terminal."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "Cu excepția cazului în care este specificată opțiunea B<--stdout>, I<fișierele> altele decât B<-> sunt scrise într-un fișier nou al cărui nume este derivat din numele I<fișierului> sursă:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "La comprimare, sufixul formatului de fișier țintă (B<.xz> sau B<.lzma>) este atașat la numele fișierului sursă pentru a se obține numele fișierului țintă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "La decomprimare, sufixul B<.xz>, B<.lzma> sau B<.lz> este eliminat din numele fișierului pentru a se obține numele fișierului țintă. B<xz> recunoaște și sufixele B<.txz> și B<.tlz> și le înlocuiește cu sufixul B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Dacă fișierul țintă există deja, este afișată o eroare și I<fișier> este omis."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "Cu excepția cazului în care scrie la ieșirea standard, B<xz> va afișa un avertisment și va omite I<fișier>ul dacă se aplică oricare dintre următoarele:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<Fișierul> nu este un fișier obișnuit. Legăturile simbolice nu sunt urmate și, prin urmare, nu sunt considerate fișiere obișnuite."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "I<Fișierul> are mai mult de o legătură dură."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "I<Fișierul> are activat bitul «setuid», «setgid» sau cel lipicios(sticky)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "Modul de operare este stabilit la comprimare și I<fișier> are deja un sufix al formatului de fișier țintă (B<.xz> sau B<.txz> când se comprimă în formatul B<.xz> și B<.lzma> sau B<.tlz> când se comprimă în formatul B<.lzma>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "Modul de operare este stabilit la decomprimare și I<fișierul> nu are un sufix al niciunui format de fișier acceptat (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, sau B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "După comprimarea sau decomprimarea cu succes a I<fișierului>, B<xz> copiază proprietarul, grupul, permisiunile, timpul de acces și timpul de modificare din I<fișierul> sursă în fișierul țintă. Dacă copierea grupului eșuează, permisiunile sunt modificate astfel încât fișierul țintă să nu devină accesibil utilizatorilor care nu aveau permisiunea de a accesa I<fișierul> sursă. B<xz> nu acceptă încă copierea altor metadate, cum ar fi listele de control al accesului sau atributele extinse."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Odată ce fișierul țintă a fost închis cu succes, I<fișierul> sursă este eliminat dacă nu a fost specificată opțiunea B<--keep>. I<Fișierul> sursă nu este niciodată eliminat dacă rezultatul este scris la ieșirea standard sau dacă apare o eroare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "Trimiterea unui semnal B<SIGINFO> sau B<SIGUSR1> către procesul B<xz> face ca acesta să imprime informații despre progres la ieșirea de eroare standard. Acest lucru are o utilizare limitată, deoarece atunci când ieșirea de eroare standard este un terminal, folosind opțiunea B<--verbose> va afișa un indicator de progres de actualizare automată."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Utilizarea memoriei"

# R-GC, scrie:
# nu am respectat forma de-a scrie procentele,
# ale autorilor: „5\ % to 20\ %”; pentru că noi
# folosim forma: „5\% la 20\%”; adică, ceea ce
# va vedea utilizatorul la afișarea acestui mesaj,
# va fi: „5% la 20%”, și nu: „5 % la 20 %”
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "Cantitatea de memorie utilizată de B<xz> variază de la câteva sute de kiloocteți la câțiva gigaocteți, în funcție de opțiunile de comprimare. Opțiunile utilizate la comprimarea unui fișier determină cerințele de memorie ale instrumentului de decomprimare. De obicei, instrumentul de decomprimare are nevoie de 5% până la 20% din cantitatea de memorie de care a avut nevoie instrumentul de comprimare la crearea fișierului. De exemplu, decomprimarea unui fișier creat cu B<xz -9> necesită în prezent 65Mio de memorie. Totuși, este posibil să aveți fișiere B<.xz> care necesită câțiva gigaocteți de memorie pentru decomprimare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "În special utilizatorii de sisteme mai vechi pot considera deranjantă posibilitatea unei utilizări foarte mari a memoriei. Pentru a preveni surprizele neplăcute, B<xz> are încorporat un limitator de utilizare a memoriei, care este dezactivat implicit. În timp ce unele sisteme de operare oferă modalități de a limita utilizarea memoriei proceselor, bazarea pe aceasta nu a fost considerată a fi suficient de flexibilă (de exemplu, utilizarea B<ulimit>(1) pentru a limita memoria virtuală tinde să paralizeze B<mmap>(2))."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "Limitatorul de utilizare a memoriei poate fi activat cu opțiunea din linia de comandă B<--memlimit=>I<limita>. Adesea este mai convenabil să activați limitatorul în mod implicit prin definirea variabilei de mediu B<XZ_DEFAULTS>, de exemplu, B<XZ_DEFAULTS=--memlimit=150MiB>. Este posibil să stabiliți limitele separat pentru comprimare și decomprimare folosind B<--memlimit-compress=>I<limita> și B<--memlimit-decompress=>I<limita>. Utilizarea acestor două opțiuni în afara B<XZ_DEFAULTS> este foarte rar utilă, deoarece o singură rulare a B<xz> nu poate face atât comprimarea, cât și decomprimarea și B<--memlimit=>I<limita> (sau B<-M> I<limita> ) este mai scurt de tastat pe linia de comandă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Dacă limita de utilizare a memoriei specificată este depășită la decomprimare, B<xz> va afișa o eroare și decomprimarea fișierului va eșua. Dacă limita este depășită la comprimare, B<xz> va încerca să reducă valorile stabilite astfel încât limita să nu mai fie depășită (cu excepția cazului în care se utilizează opțiunea B<--format=raw> sau B<--no-adjust>). În acest fel, operațiunea nu va eșua decât dacă limita stabilită este foarte mică. Scalarea valorilor stabilite se face în pași care nu se potrivesc cu valorile prestabilite ale nivelului de comprimare, de exemplu, dacă limita este doar puțin mai mică decât cantitatea necesară pentru B<xz -9>, valorile stabilite vor fi reduse doar puțin , nu până la valoarea prestabilită a lui B<xz -8>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Concatenare și completare (prin umplere cu octeți nuli) cu fișiere .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "Este posibil să concatenați fișierele B<.xz> așa cum sunt. B<xz> va decomprima astfel de fișiere ca și cum ar fi un singur fișier B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "Este posibil să se introducă umplutură între părțile concatenate sau după ultima parte.  Umplutura trebuie să fie compusă din octeți nuli, iar dimensiunea umpluturii trebuie să fie un multiplu de patru octeți. Acest lucru poate fi util, de exemplu, dacă fișierul B<.xz> este stocat pe un mediu care măsoară dimensiunile fișierelor în blocuri de 512 de octeți."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Concatenarea și completarea nu sunt permise cu fișierele B<.lzma> sau fluxurile brute."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "OPȚIUNI"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Sufixe de numere întregi și valori speciale"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "În majoritatea locurilor în care este de așteptat un număr întreg ca argument, un sufix opțional este acceptat pentru a indica cu ușurință numerele întregi mari. Nu trebuie să existe spațiu între numărul întreg și sufix."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Înmulțește numărul întreg cu 1.024 (2^10).  B<Ki>, B<k>, B<kB>, B<K> și B<KB> sunt acceptate ca sinonime pentru B<KiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Înmulțește numărul întreg cu 1,048,576 (2^20). B<Mi>, B<m>, B<M>, și B<MB> sunt acceptate ca sinonime pentru B<MiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Înmulțește numărul întreg cu 1,073,741,824 (2^30). B<Gi>, B<g>, B<G>, și B<GB> sunt acceptate ca sinonime pentru B<GiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "Valoarea specială B<max> poate fi utilizată pentru a indica valoarea maximă întreagă suportată de opțiune."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Mod de operare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Dacă sunt date mai multe opțiuni de mod de funcționare, ultima dintre ele, este cea care va avea efect."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Comprimare. Acesta este modul de operare implicit atunci când nu este specificată nicio opțiune de mod de funcționare și nici un alt mod de operare nu este implicat din numele comenzii (de exemplu, B<unxz> implică B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "După o comprimare reușită, fișierul sursă este eliminat, cu excepția cazului în care a fost specificată scrierea la ieșirea standard sau opțiunea B<--keep>."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Decomprimare. După o decomprimare reușită, fișierul sursă este eliminat, cu excepția cazului în care a fost specificată scrierea la ieșirea standard sau opțiunea B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Testează integritatea I<fișierelor> comprimate. Această opțiune este echivalentă cu B<--decompress --stdout> cu excepția faptului că datele decomprimate sunt înlăturate în loc să fie scrise la ieșirea standard. Nu sunt create sau eliminate fișiere."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Afișează informații despre I<fișiere> comprimate. Nu are loc nicio decomprimare la ieșire și nu sunt create sau eliminate fișiere. În modul listă, programul nu poate citi datele comprimate din intrarea standard sau din alte surse care nu pot fi explorate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "Listarea implicită arată informații de bază despre I<fișiere>, câte un fișier pe linie. Pentru a obține informații mai detaliate, utilizați și opțiunea B<--verbose>. Pentru și mai multe informații, utilizați opțiunea B<--verbose> de două ori, dar rețineți că acest lucru poate fi lent, deoarece obținerea tuturor informațiilor suplimentare necesită multe căutări. Lățimea ieșirii detaliate depășește 80 de caractere, deci canalizarea ieșirii către, de exemplu, B<less\\ -S> poate fi convenabilă dacă terminalul nu este suficient de lat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "Ieșirea exactă poate varia între versiunile B<xz> și diferitele localizări(configurările regionale). Pentru ieșiri care pot fi citite de mașină, ar trebui utilizată opțiunea B<--robot --list>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Modificatori de operare"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Nu șterge fișierele de intrare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Începând cu B<xz> 5.2.6, această opțiune face ca B<xz> să comprime sau să decomprime, chiar dacă intrarea este o legătură simbolică către un fișier obișnuit, are mai mult de-o legătură dură sau are marcați biții setuid, setgid sau bitul lipicios. Biții setuid, setgid și bitul lipicios nu sunt copiați în fișierul țintă. În versiunile anterioare acest lucru se făcea numai cu ajutorul opțiunii B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Această opțiune are mai multe efecte:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Dacă fișierul țintă există deja, îl șterge înainte de comprimare sau decomprimare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Comprimă sau decomprimă chiar dacă intrarea este o legătură simbolică către un fișier obișnuit, are mai mult de-o legătură dură sau are marcați biții setuid, setgid sau bitul lipicios. Biții setuid, setgid și bitul lipicios nu sunt copiați în fișierul țintă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Când este utilizată cu opțiunile B<--decompress> și B<--stdout>, comanda B<xz> nu poate recunoaște tipul fișierului sursă, și copiază fișierul sursă așa cum este la ieșirea standard. Acest lucru permite comenzii B<xzcat> B<--force> să fie folosită drept comanda B<cat>(1) pentru fișierele care nu au fost comprimate cu B<xz>. Rețineți că, în viitor, B<xz> ar putea să accepte noi formate de fișiere comprimate, ceea ce poate face ca B<xz> să decomprime mai multe tipuri de fișiere în loc să le copieze așa cum sunt la ieșirea standard. Opțiunea B<--format=>I<format> poate fi folosită pentru a restricționa B<xz> să decomprime doar un singur format de fișier."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Scrie datele comprimate sau decomprimate la ieșirea standard în loc de într-un fișier. Aceasta implică B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Decomprimă numai primul flux B<.xz> și ignoră în tăcere posibilele date de intrare rămase în urma fluxului. În mod normal, astfel de resturi rămase face ca B<xz> să afișeze o eroare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> nu decomprimă niciodată mai mult de un flux din fișierele B<.lzma> sau din fluxurile brute, dar această opțiune face ca B<xz> să ignore posibilele resturi de date rămase după fișierul B<.lzma> sau fluxul brut."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Această opțiune nu are efect dacă modul de funcționare nu este B<--decompress> sau B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr "Începând cu B<xz> 5.7.1alpha, B<--single-stream> implică B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Dezactivează crearea de fișiere dispersate. În mod implicit, dacă decomprimă într-un fișier obișnuit, B<xz> încearcă să facă fișierul dispersat dacă datele decomprimate conțin secvențe lungi de zerouri binare. De asemenea, funcționează atunci când scrie la ieșirea standard, atâta timp cât ieșirea standard este conectată la un fișier obișnuit și sunt îndeplinite anumite condiții suplimentare pentru a o face în siguranță. Crearea de fișiere dispersate poate economisi spațiu pe disc și poate accelera decomprimarea prin reducerea cantității de date de In/Ieș pe disc."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "Când comprimă, utilizează I<.suf> ca sufix pentru fișierul țintă în loc de B<.xz> sau B<.lzma>. Dacă nu scrie la ieșirea standard și fișierul sursă are deja sufixul I<.suf>, este afișat un avertisment și fișierul este omis."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "Când decomprimă, recunoaște fișierele cu sufixul I<.suf> în plus față de fișierele cu sufixul B<.xz>, B<.txz>, B<.lzma>, B<.tlz> sau B<.lz>. Dacă fișierul sursă are sufixul I<.suf>, sufixul este eliminat pentru a obține numele fișierului țintă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "La comprimarea sau decomprimarea fluxurilor brute (B<--format=raw>), sufixul trebuie să fie întotdeauna specificat, cu excepția cazului în care se scrie la ieșirea standard, deoarece nu există un sufix implicit pentru fluxurile brute."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<fișier>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Citește numele fișierelor de procesat din I<fișier>; dacă I<fișierul> este omis, numele fișierelor sunt citite de la intrarea standard. Numele de fișiere trebuie să fie terminate cu caracterul de linie nouă. O liniuță (B<->) este luată ca nume de fișier obișnuit; nu înseamnă intrarea standard. Dacă numele de fișiere sunt date și ca argumente în linia de comandă, ele sunt procesate înainte ca numele fișierelor să fie citite din I<fișier>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<fișier>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Această opțiune este identică cu B<--files>[B<=>I<fișier>], cu excepția faptului că fiecare nume de fișier trebuie să fie terminat cu caracterul nul."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Formatul de bază al fișierului și opțiunile de comprimare"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<format>, B<--format=>I<format>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Specifică I<formatul> fișierului pentru comprimare sau decomprimare:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Aceasta este valoarea implicită. La comprimare, B<auto> este echivalent cu B<xz>. La decomprimare, formatul fișierului de intrare este detectat automat. Rețineți că fluxurile brute (create cu B<--format=raw>) nu pot fi detectate automat."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Comprimă în formatul de fișier B<.xz> sau acceptă numai fișierele B<.xz> când decomprimă."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Comprimă în formatul de fișier B<.lzma> vechi sau acceptă numai fișierele B<.lzma> când decomprimă.  Numele alternativ B<alone> este furnizat pentru compatibilitatea cu versiunile mai vechi de LZMA Utils."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Acceptă numai fișierele B<.lz> când decomprimă. Comprimarea nu este acceptată."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "Formatul B<.lz> versiunea 0 și versiunea neextinsă 1 sunt acceptate. Fișierele versiunea 0 au fost produse de B<lzip> cu versiunea 1.3 sau mai veche. Astfel de fișiere nu sunt obișnuite, dar pot fi găsite în arhivele de fișiere, deoarece câteva pachete sursă au fost lansate în acest format. Oamenii ar putea avea și fișiere personale vechi în acest format. Suportul de decomprimare pentru versiunea de format 0 a fost eliminat în B<lzip> 1.18."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip> 1.4 și versiunile ulterioare creează fișiere în formatul versiunea 1. Extensia „sync flush marker” pentru versiunea 1 de format a fost adăugată în B<lzip> 1.6. Această extensie este folosită rar și nu este acceptată de B<xz> (diagnosticată ca intrare coruptă)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Comprimă sau decomprimă un flux brut (fără anteturi). Acest lucru este destinat doar utilizatorilor avansați. Pentru a decodifica fluxurile brute, trebuie să utilizați opțiunea B<--format=raw> și să specificați în mod explicit lanțul de filtre, care în mod normal ar fi fost stocat în anteturile containerului."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<verificarea>, B<--check=>I<verificarea>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Specifică tipul verificării integrității. Verificarea este calculată din datele necomprimate și stocată în fișierul B<.xz>. Această opțiune are efect numai la comprimarea în format B<.xz>; formatul B<.lzma> nu acceptă verificări de integritate. Verificarea integrității (dacă există) este efectuată atunci când fișierul B<.xz> este decomprimat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Tipuri de I<verificare> acceptate:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Nu calculează deloc o verificare a integrității. Aceasta este de obicei o idee proastă. Acest lucru poate fi util atunci când integritatea datelor este oricum verificată prin alte mijloace."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Calculează CRC32 folosind polinomul din IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Calculează CRC64 folosind polinomul din ECMA-182. Aceasta este valoarea implicită, deoarece este ceva mai bună decât CRC32 la detectarea fișierelor deteriorate, iar diferența de viteză este neglijabilă."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Calculează SHA-256. Acest lucru este oarecum mai lent decât CRC32 și CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "Integritatea antetelor B<.xz> este întotdeauna verificată cu CRC32. Nu este posibilă modificarea sau dezactivarea acesteia."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Nu efectuează verificarea integrității datelor comprimate la decomprimare. Valorile CRC32 din antetele B<.xz> vor fi însă verificate normal."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Nu utilizați această opțiune decât dacă știți ce faceți>. Motive posibile pentru a utiliza această opțiune:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Încercarea de a recupera datele dintr-un fișier .xz corupt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Accelerarea decomprimării. Acest lucru contează mai ales cu SHA-256 sau cu fișierele care s-au comprimat extrem de bine. Este recomandat să nu utilizați această opțiune în acest scop decât dacă integritatea fișierului este verificată extern într-un alt mod."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Selectează un nivel prestabilit de comprimare. Valoarea implicită este B<-6>. Dacă sunt specificate mai multe niveluri prestabilite, ultimul are efect. Dacă a fost deja specificat un lanț de filtre personalizat, specificarea unui nivel prestabilit de comprimare șterge lanțul de filtre personalizat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Diferențele dintre valorile prestabilite sunt mai semnificative decât cu B<gzip>(1) și B<bzip2>(1). Valorile de comprimare selectate determină cerințele de memorie ale instrumentului de decomprimare, astfel încât utilizarea unui nivel prea mare prestabilit ar putea face „dureroasă” decomprimarea fișierului pe un sistem vechi cu puțină memorie RAM. Mai exact, B<nu este o idee bună să folosiți orbește -9 pentru tot> așa cum se întâmplă adesea cu B<gzip>(1) și B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Acestea sunt valorile prestabilite oarecum rapide. B<-0> este uneori mai rapid decât B<gzip -9> în timp ce comprimă mult mai bine. Cele mai ridicate au adesea viteza comparabilă cu B<bzip2>(1) cu un raport de comprimare comparabil sau mai bun, deși rezultatele depind foarte mult de tipul de date care sunt comprimate."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Comprimare bună spre foarte bună, păstrând în același timp utilizarea memoriei de către instrumentul de decomprimare la un nivel rezonabil chiar și pentru sistemele vechi. B<-6> este valoarea implicită, care este de obicei o alegere bună pentru distribuirea fișierelor care trebuie să poată fi decomprimate chiar și pe sisteme cu doar 16Mio de memorie RAM. Opțiunile (B<-5e> sau B<-6e> ar putea fi demne de luat în considerare. A se vedea opțiunea B<--extreme>.)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Acestea sunt precum B<-6>, dar cu cerințe mai mari de memorie pentru comprimare și decomprimare. Acestea sunt utile numai atunci când comprimați fișiere mai mari de 8Mio, 16Mio și, respectiv, 32Mio."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "Pe același hardware, viteza de decomprimare este aproximativ un număr constant de octeți de date comprimate pe secundă. Cu alte cuvinte, cu cât comprimarea este mai bună, cu atât decomprimarea va fi de obicei mai rapidă. Aceasta înseamnă, de asemenea, că valoarea de la ieșire a cantității de date necomprimate produsă pe secundă poate varia foarte mult."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "Următorul tabel rezumă caracteristicile valorilor prestabilite:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "ValPrestab"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "DimDict"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "CPUComp"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "MemComp"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "MemDec"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Descrieri coloane:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "DimDict este dimensiunea dicționarului LZMA2. Este o risipă de memorie să folosești un dicționar mai mare decât dimensiunea fișierului necomprimat. De aceea este bine să evitați utilizarea valorilor prestabilite B<-7> ... B<-9> atunci când nu este nevoie cu adevărat de ele. Pentru valoarea prestabilită B<-6> sau alta mai mică, cantitatea de memorie irosită este de obicei suficient de mică pentru a nu conta."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "CPUComp este o reprezentare simplificată a configurărilor LZMA2 care afectează viteza de comprimare. Dimensiunea dicționarului afectează și viteza, așa că, în timp ce CPUComp este aceeași pentru nivelurile B<-6> ... B<-9>, nivelurile mai mari tind să fie puțin mai lente. Pentru a obține o comprimare și mai lentă și, astfel, posibil mai bună, consultați opțiunea B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr "MemComp conține cerințele de memorie ale comprimării în modul cu un singur fir de execuție. Poate varia ușor între versiunile B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "MemDec conține cerințele de memorie pentru decomprimare. Adică, configurările de comprimare determină cerințele de memorie ale decomprimării. Cantitatea exactă a memoriei utilizate la decomprimare este puțin mai mare decât dimensiunea dicționarului LZMA2, dar valorile din tabel au fost rotunjite la următorul Mio."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr "Cerințele de memorie ale modului cu mai multe fire de execuție sunt semnificativ mai mari decât cele ale modului cu un singur fir de execuție. Cu valoarea implicită a lui B<--block-size>, fiecare fir are nevoie de 3*3*DictSize plus MemComp sau MemDec. De exemplu, patru fire de execuție cu valoarea prestabilită B<-6> au nevoie de 660\\(en670\\ Mio de memorie."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Utilizează o variantă mai lentă a nivelului prestabilit de comprimare selectat (B<-0> ... B<-9>) pentru a obține un raport de comprimare puțin mai bun, dar din nefericire, acest lucru îl poate înrăutăți. Utilizarea memoriei pentru decomprimare nu este afectată, dar utilizarea memoriei la comprimare crește puțin la nivelurile prestabilite B<-0> ... B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Deoarece există două valori prestabilite cu dimensiuni ale dicționarului de 4Mio și 8Mio, valorile prestabilite B<-3e> și B<-5e> folosesc configurări puțin mai rapide (CPUComp mai mic) decât B<-4e> și B<-6e>, respectiv. În acest fel, nu există două nivele prestabilite identice."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "De exemplu, există un total de patru nivele prestabilite care folosesc dicționarul 8Mio, a căror ordine de la cel mai rapid la cel mai lent este B<-5>, B<-6>, B<-5e> și B<-6e> ."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Acestea sunt alias de opțiuni, oarecum înșelătoare pentru B<-0> și, respectiv, B<-9>. Acestea sunt furnizate numai pentru compatibilitatea cu LZMA Utils. Evitați utilizarea acestor opțiuni."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<dimensiunea>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "Când comprimă în formatul B<.xz>, împarte datele de intrare în blocuri de I<dimensiunea> octeți. Blocurile sunt comprimate independent unul de celălalt, ceea ce ajută în modul cu mai multe fire de execuție și face posibilă decomprimarea cu acces aleatoriu limitat. Această opțiune este de obicei folosită pentru a suprascrie dimensiunea implicită a blocului în modul cu mai multe fire de execuție, dar această opțiune poate fi folosită și în modul cu un singur fir de execuție."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr "În modul cu mai multe fire de execuție, aproximativ de trei ori I<dimensiunea> de octeți vor fi alocați în fiecare fir pentru stocarea intrării și ieșirii. I<Dimensiunea> implicită este de trei ori dimensiunea dicționarului LZMA2 sau 1Mio, oricare dintre acestea este mai mare. În mod obișnuit, o valoare bună este de două la patru ori dimensiunea dicționarului LZMA2 sau de cel puțin 1Mio. Utilizarea unei I<dimensiuni> mai mici decât dimensiunea dicționarului LZMA2 este o risipă de memorie RAM, deoarece atunci memoria tampon a dicționarului LZMA2 nu va fi niciodată utilizată pe deplin. În modul cu mai multe fire de execuție, dimensiunile blocurilor sunt stocate în anteturile blocurilor. Aceste informații privind dimensiunea sunt necesare pentru decomprimarea cu mai multe fire."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr "În modul cu un singur fir de execuție, nicio divizare a blocurilor nu se face în mod implicit. Folosirea acestei opțiuni nu afectează utilizarea memoriei. Nu sunt stocate informații despre dimensiune în antetele blocurilor, astfel încât fișierele create în modul cu un singur fir de execuție nu vor fi identice cu fișierele create în modul cu mai multe fire de execuție. Lipsa informațiilor privind dimensiunea înseamnă, de asemenea, că B<xz> nu va putea decomprima fișierele în modul cu mai multe fire. de execuție."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-list=>I<items>"
msgstr "B<--block-list=>I<elemente>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr "Când comprimă în formatul B<.xz>, începe un nou bloc cu un lanț de filtre personalizat opțional după intervalele specificate de date necomprimate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr "I<elementele> sunt o listă separată prin virgule. Fiecare element este format dintr-un număr opțional de lanț de filtrare între 0 și 9, urmat de două puncte (B<:>) și de o dimensiune cerută a datelor necomprimate. Omiterea unui element (două sau mai multe virgule consecutive) este o prescurtare pentru a utiliza dimensiunea și filtrele din elementul anterior."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr "În cazul în care fișierul de intrare este mai mare decât suma dimensiunilor din I<elemente>, ultimul element se repetă până la sfârșitul fișierului. O valoare specială de B<0> poate fi utilizată ca ultimă dimensiune pentru a indica faptul că restul fișierului trebuie să fie codificat ca un singur bloc."

# R-GC, scrie:
# în acest mesaj, mărimile „MiB” nu au fost
# traduse ca „Mio”, cum era normal, pentru
# că aici se indică cum trebuie să treacă seria
# de parametrii în linia de comandă, utilizatorul,
# pentru ca utilitățile xz să „înțeleagă” despre
# ce este vorba...
#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr "Un lanț de filtre alternativ pentru fiecare bloc poate fi specificat în combinație cu opțiunile B<--filters1=>I<filtre> \\&...\\& B<--filters9=>I<filtre>. Aceste opțiuni definesc lanțuri de filtre cu un identificator cuprins între 1\\(en9. Lanțul de filtre 0 poate fi utilizat pentru a se referi la lanțul de filtre implicit, ceea ce este același lucru cu a nu specifica un lanț de filtre. Identificatorul lanțului de filtre poate fi utilizat înaintea dimensiunii necomprimate, urmat de două puncte (B<:>). De exemplu, dacă se specifică B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB>, atunci blocurile vor fi create folosind:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr "Lanțul de filtre specificat de B<--filters1> și 2 Mio de intrare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr "Lanțul de filtre specificat de B<--filters3> și 2 Mio de intrare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr "Lanțul de filtre specificat de B<--filters2> și 4 Mio de intrare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr "Lanțul de filtre implicit și 2 MiB de intrare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr "Lanțul de filtre implicit și 4 MiB de intrare pentru fiecare bloc până la sfârșitul intrării."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr "Dacă se specifică o dimensiune care depășește dimensiunea blocului codificatorului (fie valoarea implicită în modul cu fire de execuție, fie valoarea specificată cu B<--block-size=>I<dimensiune>), codificatorul va crea blocuri suplimentare, păstrând limitele specificate în I<elemente>. De exemplu, dacă se specifică B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> și fișierul de intrare este de 80 MiB, se vor obține 11 blocuri: 5, 10, 8, 10, 10, 2, 10, 10, 10, 4, 10, 10, 10 și 1 Mio."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "În modul cu mai multe fire de execuție, dimensiunile blocurilor sunt stocate în antetele blocurilor. Acest lucru nu se face în modul cu un singur fir de execuție, astfel încât ieșirea codificată nu va fi identică cu cea a modului cu mai multe fire de execuție."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<timp_limită>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "La comprimare, dacă au trecut mai mult de I<timp_limită> milisecunde (un întreg pozitiv) de la curățarea anterioară și citirea mai multor intrări s-ar bloca, toate datele de intrare în așteptare sunt eliminate din codificator și puse la dispoziție în fluxul de ieșire. Acest lucru poate să fie util dacă B<xz> este utilizat pentru a comprima datele care sunt transmise în flux printr-o rețea. Valorile mici de I<timp_limită> fac datele disponibile la capătul de recepție cu o mică întârziere, dar valorile mari de I<timp_limită> oferă un raport de comprimare mai bun."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Această caracteristică este dezactivată în mod implicit. Dacă această opțiune este specificată de mai multe ori, ultima este cea care se ia în considerare. Valoarea specială a lui I<timp_limită> de B<0>, poate fi utilizată pentru a dezactiva în mod explicit această caracteristică."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Această caracteristică nu este disponibilă în sistemele non-POSIX."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Această caracteristică este încă experimentală>. În prezent, B<xz> este nepotrivit pentru decomprimarea fluxului în timp real datorită modului în care B<xz> utilizează memoria tampon."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sync>"
msgstr "B<--no-sync>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr "Nu sincronizează fișierul țintă și directorul acestuia cu dispozitivul de stocare înainte de a elimina fișierul sursă. Acest lucru poate îmbunătăți performanța în cazul comprimării sau decomprimării multor fișiere mici. Cu toate acestea, dacă sistemul se blochează imediat după ștergere, este posibil ca fișierul țintă să nu fi fost scris pe dispozitivul de stocare, dar operația de ștergere să fi fost. În acest caz, nici fișierul sursă original, nici fișierul țintă nu sunt disponibile."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr "Această opțiune are efect numai atunci când B<xz> urmează să elimine fișierul sursă. În alte cazuri, sincronizarea nu se face niciodată."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr "Sincronizarea și B<--no-sync> au fost adăugate în B<xz> 5.7.1alpha."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<limita>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Stabilește o limită de utilizare a memoriei pentru comprimare. Dacă această opțiune este specificată de mai multe ori, ultima va avea efect."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Dacă parametrii de comprimare depășesc I<limita>, B<xz> va încerca să ajusteze parametrii scăzând valorile acestora, astfel încât limita să nu mai fie depășită și va afișa o notificare că ajustarea automată a fost efectuată. Ajustările se fac în această ordine: reducerea numărului de fire, trecerea la modul un singur fir de execuție dacă chiar și un singur fir în modul cu mai multe fire de execuție depășește I<limita> și, în final, reducerea dimensiunii dicționarului LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "Când comprimă cu opțiunea B<--format=raw> sau dacă a fost specificată opțiunea B<--no-adjust>, numai numărul de fire poate fi redus, deoarece se poate face fără a afecta rezultatul comprimării."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Dacă I<limita> nu poate fi îndeplinită chiar și cu ajustările descrise mai sus, este afișată o eroare și B<xz> va ieși cu starea de ieșire 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "I<Limita> poate fi specificata în mai multe moduri:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "I<Limita> poate fi o valoare absolută în octeți. Utilizarea unui sufix întreg precum B<MiB> poate fi utilă. De exemplu: B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "I<Limita> poate fi specificată ca procent din memoria fizică totală (RAM). Acest lucru poate fi util mai ales atunci când definiți variabila de mediu B<XZ_DEFAULTS> într-un script de inițializare shell care este partajat între diferite calculatoare. În acest fel, limita este automat mai mare pe sistemele cu mai multă memorie. De exemplu: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "I<Limita> poate fi restabilită la valoarea implicită dându-i valoarea B<0>. În prezent, aceasta este echivalentă cu stabilirea I<limitei> la B<max> (fără limită de utilizare a memoriei)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "Pentru B<xz> pe 32 de biți există un caz special: dacă I<limita> ar fi peste B<4020MiB>, I<limita> este stabilită la B<4020MiB>.  Pe MIPS32 este stabilită în schimb la B<2000MiB>; (valorile B<0> și B<max> nu sunt afectate de acest lucru -- o caracteristică similară nu există pentru decomprimare). Acest lucru poate fi util atunci când un executabil pe 32 de biți are acces la un spațiu de adrese de 4Gio (2Gio pe MIPS32), se speră că nu produce daune în alte situații."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Consultați și secțiunea B<Utilizarea memoriei>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<limita>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Stabilește o limită de utilizare a memoriei pentru decomprimare. Acest lucru afectează și modul B<--list>. Dacă operațiunea nu este posibilă fără a depăși I<limita>, B<xz> va afișa o eroare și decomprimarea fișierului va eșua. Consultați B<--memlimit-compress=>I<limita> pentru modalitățile posibile de a specifica I<limita>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<limita>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "Stabilește o limită de utilizare a memoriei pentru decomprimarea cu mai multe fire de execuție. Acest lucru poate afecta doar numărul de fire de execuție; acest lucru nu îl va face niciodată pe B<xz> să refuze decomprimarea unui fișier. Dacă I<limita> este prea scăzută pentru a permite orice mod cu mai multe fire de execuție, I<limita> este ignorată și B<xz> va continua în modul cu un singur fir de execuție. Rețineți că, dacă se folosește și opțiunea B<--memlimit-decompress>, se va aplica întotdeauna atât modurilor cu un singur fir de execuție, cât și modurilor cu mai multe fire de execuție și astfel I<limita> efectivă pentru modul cu mai multe fire de execuție nu va fi niciodată mai mare decât limita stabilită cu opțiunea B<--memlimit-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "Spre deosebire de celelalte opțiuni de limită de utilizare a memoriei, opțiunea B<--memlimit-mt-decompress=>I<limita> are o I<limită> implicită specifică sistemului. Comanda B<xz --info-memory> poate fi folosită pentru a vedea valoarea curentă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Această opțiune și valoarea ei implicită există deoarece, fără nicio limită, decomprimarea cu (mai multe) fire de execuție ar putea ajunge să aloce o cantitate „nebună” de memorie cu unele fișiere de intrare. Dacă I<limita> implicită este prea scăzută pe sistemul dumneavoastră, nu ezitați să creșteți I<limita>, dar niciodată să nu o stabiliți la o valoare mai mare decât cantitatea de memorie RAM utilizabilă și cu niște fișiere de intrare adecvate, B<xz> va încerca să utilizeze acea cantitate de memorie chiar și cu un număr redus de fire de execuție. Rularea lui B<xz> cu depășirea cantității de memorie fizice(RAM) sau a celei de interschimb(swap) nu va îmbunătăți performanța de decomprimare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Consultați opțiunea B<--memlimit-compress=>I<limita> pentru modalități posibile de a specifica I<limita>. Stabilirea I<limitei> la B<0> restabilește I<limita> la valoarea implicită specifică sistemului."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<limita>, B<--memlimit=>I<limita>, B<--memory=>I<limita>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Aceasta este echivalentă cu specificarea opțiunilor: B<--memlimit-compress=>I<limita> B<--memlimit-decompress=>I<limita> B<--memlimit-mt-decompress=>I<limita>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "Afișează o eroare și iese dacă limita de utilizare a memoriei nu poate fi îndeplinită fără ajustarea parametrilor care afectează ieșirea comprimată. Adică, acest lucru împiedică B<xz> să comute codificatorul din modul cu mai multe fire de execuție în modul cu un singur fir de execuție și să reducă dimensiunea dicționarului LZMA2. Chiar și atunci când această opțiune este utilizată, numărul de fire de execuție poate fi redus pentru a îndeplini limita de utilizare a memoriei, deoarece aceasta nu va afecta comprimarea."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "Ajustarea automată este întotdeauna dezactivată la crearea fluxurilor brute (B<--format=raw>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<număr>, B<--threads=>I<număr>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "Specifică numărul de fire de execuție de utilizat. Stabilirea I<numărului> la valoarea specială B<0>, face ca B<xz> să utilizeze până la atâtea fire de execuție câte procesoare sunt în sistem. Numărul real de fire de execuție poate fi mai mic decât I<număr> dacă fișierul de intrare nu este suficient de mare pentru a trece la modul cu mai multe fire de execuție cu parametrii dați, sau dacă folosirea mai multor fire de execuție ar depăși limita de utilizare a memoriei."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "Operațiile de comprimare cu un singur fir de execuție și cele cu mai multe fire de execuție produc ieșiri diferite. Comprimarea cu un singur fir de execuție va oferi cea mai mică dimensiune a fișierului, dar numai ieșirea de la comprimarea cu mai multe fire de execuție poate fi decomprimată folosind mai multe fire. Stabilirea I<numărului> la B<1> va determina ca B<xz> să folosească modul cu un singur fir de execuție. Stabilirea I<numărului> la orice altă valoare, inclusiv B<0>, va determina ca B<xz> să folosească comprimarea cu mai multe fire de execuție chiar dacă sistemul acceptă doar un fir hardware; (B<xz> 5.2.x folosește modul cu un singur fir de execuție în această situație)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Pentru a utiliza modul cu mai multe fire de execuție cu un singur fir, stabiliți I<numărul> la B<+1>. Prefixul B<+> nu are efect cu alte valori decât B<1>. O limită de utilizare a memoriei poate face în continuare B<xz> să treacă în modul cu un singur fir, cu excepția cazului în care este utilizată opțiunea B<--no-adjust>. Suportul pentru prefixul B<+> a fost adăugat în B<xz> 5.4.0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr "Dacă a fost solicitat un număr automat de fire și nu a fost specificată nicio limită de utilizare a memoriei, atunci o limită „maleabilă” implicită specifică sistemului va fi utilizată pentru a limita eventual numărul de fire de execuție. Este o limită „maleabilă” în sensul că este ignorată dacă numărul de fire devine unul, astfel o limită „maleabilă” nu va opri niciodată B<xz> să comprime sau să decomprime. Această limită „maleabilă” implicită nu va face B<xz> să treacă de la modul cu mai multe fire de execuție la modul cu un singur fir de execuție. Limitele active pot fi văzute rulând comanda B<xz --info-memory>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "În prezent, singura metodă de procesare cu fire de execuție este împărțirea intrării în blocuri și comprimarea lor independent unul de celălalt. Dimensiunea implicită a blocului depinde de nivelul de comprimare și poate fi înlocuită cu opțiunea B<--block-size=>I<dimensiune>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "Decomprimarea cu fire de execuție funcționează numai pe fișierele care conțin mai multe blocuri cu informații despre dimensiune în antetele blocurilor. Toate fișierele suficient de mari comprimate în modul cu mai multe fire de execuție îndeplinesc această condiție, dar fișierele comprimate în modul cu un singur fir de execuție nu o îndeplinesc chiar dacă a fost folosită opțiunea B<--block-size=>I<dimensiune>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr "Valoarea implicită pentru I<fire de execuție> este B<0>. În B<xz> 5.4.x și mai vechi, valoarea implicită este B<1>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Lanțuri de filtrare personalizate pentru instrumentul de comprimare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Un lanț de filtrare personalizat permite specificarea parametrilor de comprimare în detaliu, în loc să se bazeze pe cei asociați opțiunilor prestabilite. Când este specificat un lanț de filtrare personalizat, opțiunile prestabilite (B<-0> \\&...\\& B<-9> și B<--extreme>) de mai devreme din linia de comandă sunt uitate. Dacă o opțiune prestabilită este specificată după una sau mai multe opțiuni de lanț de filtrare personalizat, noua prestabilire intră în vigoare și opțiunile lanțului de filtrare personalizat, specificate mai devreme sunt uitate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Un lanț de filtrare este comparabil cu conductele din linia de comandă. La comprimare, intrarea necomprimată merge la primul filtru, a cărui ieșire merge la următorul filtru (dacă există). Ieșirea ultimului filtru este scrisă în fișierul comprimat. Numărul maxim de filtre din lanț este de patru, dar de obicei un lanț de filtrare are doar unul sau două filtre."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Multe filtre au limitări în ceea ce privește locul în care se pot afla în lanțul de filtrare: unele filtre pot funcționa doar ca ultimul filtru din lanț, altele doar ca non-ultim filtru și unele funcționează în orice poziție din lanț. În funcție de filtru, această limitare este fie inerentă proiectării filtrului, fie există pentru a preveni problemele de securitate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr "Un lanț de filtre personalizat poate fi specificat în două moduri diferite. Opțiunile B<--filters=>I<filtre> și B<--filters1=>I<filtre> \\&...\\& B<--filters9=>I<filtre> permit specificarea unui întreg lanț de filtre într-o singură opțiune, folosind sintaxa șirului de filtre liblzma. Alternativ, un lanț de filtre poate fi specificat prin utilizarea uneia sau mai multor opțiuni de filtrare individuale în ordinea în care sunt dorite în lanțul de filtre. Adică, ordinea opțiunilor de filtrare individuale este semnificativă! La decodificarea fluxurilor brute (B<--format=raw>), lanțul de filtre trebuie să fie specificat în aceeași ordine în care a fost specificat la comprimare. Orice filtru individual sau opțiuni presetate specificate înainte de opțiunea de lanț complet (B<--filters=>I<filtre>) vor fi uitate. Filtrele individuale specificate după opțiunea „lanț complet” vor reinițializa lanțul de filtre."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr "Atât opțiunile de filtrare completă, cât și cele de filtrare individuală acceptă I<opțiuni> specifice filtrului sub forma unei liste separate prin virgule. Se ignoră virgulele suplimentare din I<opțiuni>. Fiecare opțiune are o valoare implicită, deci specificați-le pe cele pe care doriți să le modificați."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Pentru a vedea întregul lanț de filtre și I<opțiuni>, utilizați B<xz -vv> (adică folosiți B<--verbose> de două ori). Acest lucru funcționează și pentru vizualizarea opțiunilor lanțului de filtre utilizate de valorile prestabilite."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters=>I<filters>"
msgstr "B<--filters=>I<filtre>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr "Specificați întregul lanț de filtre sau o presetare într-o singură opțiune. Fiecare filtru poate fi separat prin spații sau două liniuțe (B<-->). Este posibil să fie necesar ca I<filtrele> să fie puse între ghilimele în linia de comandă a shell-ului pentru a fi analizate ca o singură opțiune. Pentru a indica I<opțiuni>, utilizați B<:> sau B<=>. O presetare poate fi prefixată cu un B<-> și urmată de zero sau mai multe indicatoare. Singurul indicator suportat este B<e> pentru a aplica aceleași opțiuni ca și B<--extreme>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr "B<--filters1>=I<filtre> ... B<--filters9>=I<filtre>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr "Specifică până la nouă lanțuri de filtre suplimentare care pot fi utilizate cu B<--block-list>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr "De exemplu, atunci când se comprimă o arhivă cu fișiere executabile urmate de fișiere text, partea executabilă ar putea utiliza un lanț de filtre cu un filtru BCJ, iar partea de text doar filtrul LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters-help>"
msgstr "B<--filters-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr "Afișează un mesaj de ajutor care descrie modul de specificare a presetărilor și a lanțurilor de filtre personalizate în opțiunile B<--filters> și B<--filters1=>I<filtre> \\&...\\& B<--filters9=>I<filtre> și iese."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<opțiuni>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Adaugă filtrul LZMA1 sau LZMA2 la lanțul de filtre. Aceste filtre pot fi folosite doar ca ultimul filtru din lanț."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 este un filtru vechi, care este acceptat aproape exclusiv datorită formatului de fișier vechi B<.lzma>, care acceptă numai LZMA1. LZMA2 este o versiune actualizată a LZMA1 pentru a rezolva unele probleme practice ale LZMA1. Formatul B<.xz> folosește LZMA2 și nu acceptă deloc LZMA1. Viteza de comprimare și rapoartele LZMA1 și LZMA2 sunt practic aceleași."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 și LZMA2 au același set de I<opțiuni>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<prestabilit>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "Reconfigurează toate I<opțiunile> LZMA1 sau LZMA2 la I<prestabilit>. I<prestabilit> constă dintr-un număr întreg, care poate fi urmat de modificatori prestabiliți cu o singură literă.  Numărul întreg poate fi de la B<0> la B<9>, potrivindu-se cu opțiunile liniei de comandă B<-0> \\&...\\& B<-9>. Singurul modificator acceptat în prezent este B<e>, care se potrivește cu B<--extreme>. Dacă nu este specificat B<prestabilit>, valorile implicite ale I<opțiunilor> LZMA1 sau LZMA2 sunt preluate din prestabilirea B<6>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<dimensiunea>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "I<Dimensiunea> dicționarului (istoricul memoriei tampon) indică câți octeți din datele necomprimate recent procesate sunt păstrați în memorie. Algoritmul încearcă să găsească secvențe de octeți care se repetă (potriviri) în datele necomprimate și să le înlocuiască cu referințe la datele aflate în prezent în dicționar. Cu cât dicționarul este mai mare, cu atât este mai mare șansa de a găsi o potrivire. Astfel, creșterea I<dimensiunii> dicționarului îmbunătățește de obicei raportul de comprimare, dar un dicționar mai mare decât fișierul necomprimat este risipă de memorie."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "I<Dimensiunea>tipică a dicționarului este de la 64Kio până la 64Mio. Minimul este de 4Kio. Maximul pentru compresie este în prezent de 1,5Gio (1536Mio). Decomprimarea acceptă deja dicționare cu până la un octet mai puțin de 4Gio, care este maximul pentru formatele de flux LZMA1 și LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "I<Dimensiunea> dicționarului și găsitorul de potriviri (match finder) → (I<mf>) determină împreună utilizarea memoriei de către codificatorul LZMA1 sau LZMA2. Aceeași I<dimensiune> a dicționarului (sau mai mare) care a fost utilizată la comprimare, este necesară pentru decomprimare, astfel încât utilizarea memoriei de către decodificator este determinată de dimensiunea dicționarului utilizată la comprimare. Antetele B<.xz> stochează I<dimensiunea> dicționarului fie ca 2^I<n>, fie ca 2^I<n> + 2^(I<n>-1), deci aceste I<dimensiuni> sunt oarecum preferate pentru comprimare. Alte I<dimensiuni> vor fi rotunjite atunci când sunt stocate în anteturile B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Specifică numărul de biți de context literal. Minimul este 0 și maximul este 4; implicit este 3.  În plus, suma I<lc> și I<lp> nu trebuie să depășească 4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Toți octeții care nu pot fi codificați ca potriviri sunt codificați ca literali. Adică, literalii sunt pur și simplu octeți de 8 biți care sunt codificați unul câte unul."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "Codificarea literală presupune că cei mai mari biți I<lc> ai octetului anterior necomprimat se corelează cu octetul următor. De exemplu, în textul tipic englezesc, o literă mare este adesea urmată de o literă mică, iar o literă mică este urmată de obicei de o altă literă mică. În setul de caractere US-ASCII, cei mai mari trei biți sunt 010 pentru literele mari și 011 pentru literele mici. Când I<lc> este cel puțin 3, codificarea literală poate profita de această proprietate în datele necomprimate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "Valoarea implicită (3) este de obicei bună. Dacă doriți o comprimare maximă, testați B<lc=4>. Uneori ajută puțin, iar uneori înrăutățește comprimarea . Dacă o agravează, încercați de-asemeni cu B<lc=2>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Specifică numărul de biți de poziție literală. Minimul este 0 și maximul este 4; implicit este 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> afectează ce fel de aliniere în datele necomprimate este presupusă la codificarea literalelor. Consultați argumentul I<pb> de mai jos pentru mai multe informații despre aliniere."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Specifică numărul de biți de poziție. Minimul este 0 și maximul este 4; implicit este 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> afectează ce fel de aliniere în datele necomprimate este presupusă în general. Valoarea implicită înseamnă alinierea pe patru octeți (2^I<pb>=2^2=4), care este adesea o alegere bună atunci când nu există o ipoteză mai bună."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Când alinierea este cunoscută, definirea lui I<pb> în mod corespunzător poate reduce puțin dimensiunea fișierului. De exemplu, cu fișierele text cu aliniere pe un octet (US-ASCII, ISO-8859-*, UTF-8), definirea B<pb=0> poate îmbunătăți ușor comprimarea. Pentru textul UTF-16, B<pb=1> este o alegere bună. Dacă alinierea este un număr impar, cum ar fi 3 octeți, B<pb=0> ar putea fi cea mai bună alegere."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Chiar dacă alinierea presupusă poate fi ajustată cu I<pb> și I<lp>, LZMA1 și LZMA2 încă favorizează ușor alinierea pe 16 octeți. Ar putea fi demn de luat în considerare atunci când proiectați formate de fișiere care pot fi adesea comprimate cu LZMA1 sau LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<mf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "Căutarea potrivirilor are un efect major asupra vitezei codificatorului, utilizării memoriei și raportului de comprimare. De obicei, găsitorii de potriviri din lanțul sumelor de control sunt mai rapizi decât găsitorii de potriviri din arborele binar. Valoarea implicită depinde de I<prestabilit>: 0 folosește B<hc3>, 1\\(en3 folosește B<hc4>, iar restul folosește B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Sunt acceptate următoarele opțiuni de căutare de potriviri. Formulele de utilizare a memoriei de mai jos sunt aproximări estimative, care se apropie cel mai mult de realitate atunci când I<dict> este o putere a lui doi."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Lanț de sumă de control, cu suma de control de 2 și 3 octeți"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Valoarea minimă pentru I<nice>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Utilizarea memoriei:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 7.5 (dacă I<dict> E<lt>= 16 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 5.5 + 64 MiB (dacă I<dict> E<gt> 16 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Lanț de sumă de control, cu suma de control de 2, 3 și 4 octeți"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Valoarea minimă pentru I<nice>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 7.5 (dacă I<dict> E<lt>= 32 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 6.5 (dacă I<dict> E<gt> 32 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Arbore binar cu suma de control de 2 octeți"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Valoarea minimă pentru I<nice>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Utilizarea memoriei: I<dict> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Arbore binar cu suma de control de 2 și 3 octeți"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 11.5 (dacă I<dict> E<lt>= 16 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 9.5 + 64 MiB (dacă I<dict> E<gt> 16 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Arbore binar cu suma de control de 2, 3 și 4 octeți"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 11.5 (dacă I<dict> E<lt>= 32 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 10.5 (dacă I<dict> E<gt> 32 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<mod>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "Comprimarea I<mod> specifică metoda de analiză a datelor produse de găsitorul de potriviri. I<Modurile> acceptate sunt B<fast>(rapid) și B<normal>. Valoarea implicită este B<fast> pentru I<prestabiliri> 0\\(en3 și B<normal> pentru I<prestabiliri> 4\\(en9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "De obicei, B<fast> este folosit cu instrumentele de căutare de potriviri ale lanțului de sume de control, și B<normal> cu instrumentele de căutare de potriviri din arborele binar. Aceasta este și ceea ce fac I<prestabiririle>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<nice>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Specifică ceea ce este considerat a fi o lungime bună(nice) pentru o potrivire. Odată ce este găsită o potrivire de cel puțin I<nice> octeți, algoritmul nu mai caută după potriviri posibile mai bune."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "I<Nice> poate fi de 2\\(en273 octeți. Valorile mai mari tind să ofere un raport de comprimare mai bun în detrimentul vitezei. Valoarea implicită depinde de I<prestabilit>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<adâncimea>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Specifică adâncimea maximă de căutare în găsitorul de potriviri. Valoarea implicită este valoarea specială de 0, ceea ce face ca instrumentul de comprimare să determine o I<adâncime> rezonabilă pornind de la valorile I<mf> și I<nice>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "I<Adâncimea> rezonabilă pentru lanțuri de sumă de control este 4\\(en100 și 16\\(en1000 pentru arbori binari. Folosirea unor valori foarte mari pentru I<adâncime> poate face codificatorul extrem de lent cu unele fișiere. Evitați să stabiliți I<adâncimea> la valori peste 1000, cu excepția cazului în care sunteți pregătit să întrerupeți comprimarea în cazul în care durează prea mult."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "La decodificarea fluxurilor brute (B<--format=raw>), LZMA2 are nevoie doar de I<dimensiunea> dicționarului. LZMA1 are nevoie de asemenea de I<lc>, I<lp> și I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<opțiuni>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<opțiuni>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Adaugă un filtru de ramură/apel/salt (branch/call/jump ⟶ „BCJ”) la lanțul de filtre. Aceste filtre pot fi utilizate numai ca un filtru care nu este ultimul din lanțul de filtrare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "Un filtru BCJ convertește adresele relative din codul mașinii în omoloagele lor absolute. Acest lucru nu modifică dimensiunea datelor, dar crește redundanța, ceea ce poate ajuta LZMA2 să producă fișier B<.xz> cu 0\\(en15\\ % mai mic. Filtrele BCJ sunt întotdeauna reversibile, deci folosind un filtru BCJ pentru tipul greșit de date nu provoacă nicio pierdere de date, deși poate înrăutăți puțin raportul de comprimare. Filtrele BCJ sunt foarte rapide și folosesc o cantitate nesemnificativă de memorie."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Aceste filtre BCJ au probleme cunoscute legate de raportul de comprimare:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "Unele tipuri de fișiere care conțin cod executabil (de exemplu, fișiere obiect, biblioteci statice și module de kernel Linux) au adresele din instrucțiuni completate cu valori de umplere. Aceste filtre BCJ vor face în continuare conversia adresei, ceea ce va înrăutăți comprimarea cu aceste fișiere."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Dacă pe o arhivă este aplicat un filtru BCJ, este posibil ca raportul de comprimare să fie mai rău decât la neutilizarea unui filtru BCJ. De exemplu, dacă există executabile similare sau chiar identice, filtrarea va face probabil fișierele mai puțin asemănătoare și astfel comprimarea este mai proastă. Conținutul fișierelor neexecutabile din aceeași arhivă poate conta și el. În practică, trebuie să încercați cu și fără filtru BCJ pentru a vedea care rezultat este mai bun în fiecare situație."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Seturi de instrucțiuni diferite au o aliniere diferită: fișierul executabil trebuie aliniat la un multiplu al acestei valori în datele de intrare pentru ca filtrul să funcționeze."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Filtru"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Aliniere"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Note"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr ""
"x86 pe 32 de biți\n"
";;sau 64 de biți"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr ""
"alinierea pe 4096-octeți\n"
";;este cea mai bună"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Doar big endian"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr "RISC-V"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr "Deoarece datele filtrate prin BCJ sunt de obicei comprimate cu LZMA2, raportul de comprimare poate fi ușor îmbunătățit dacă opțiunile LZMA2 sunt definite pentru a se potrivi cu alinierea filtrului BCJ selectat. Exemple:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr "Filtrul IA-64 are o aliniere de 16 octeți, astfel încât B<pb=4,lp=4,lc=0> este alegere adecvată cu LZMA2 (2^4=16)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr "Codul RISC-V are o aliniere pe 2 sau 4 octeți, depinzând de faptul că fișierul conține instrucțiuni comprimate pe 16 biți (extensia C) sau nu. Atunci când se utilizează instrucțiuni pe 16 biți, B<pb=2,lp=1,lc=3> sau B<pb=1,lp=1,lc=3> este o alegere bună. Atunci când nu sunt prezente instrucțiuni pe 16 biți, B<pb=2,lp=2,lc=2> este cea mai bună alegere. B<readelf -h> poate fi utilizată pentru a verifica dacă „RVC” apare în linia „Indicatori\"."

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr "ARM64 este întotdeauna aliniat pe 4 octeți, astfel încât B<pb=2,lp=2,lc=2> este cea mai bună alegere."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr "Filtrul x86 este o excepție. De obicei, este bine să rămâneți la valorile implicite ale LZMA2 (B<pb=2,lp=0,lc=3>) atunci când comprimați executabile x86."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Toate filtrele BCJ acceptă același I<opțiuni>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<poziție>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Specifică I<poziția> de pornire care este utilizată la conversia între adresele relative și absolute. I<Poziția> trebuie să fie un multiplu al alinierii filtrului (consultați tabelul de mai sus).  Valoarea implicită este zero. În practică, valoarea implicită este bună; specificarea unei I<poziții> personalizate nu este aproape niciodată utilă."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<opțiuni>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Adaugă filtrul Delta în lanțul de filtrare. Filtrul Delta poate fi folosit doar ca un filtru care nu este ultimul în lanțul de filtrare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "În prezent, este acceptat doar calculul delta simplu de octeți. Poate fi util la comprimarea, de exemplu, a imaginilor bitmap necomprimate sau a sunetului PCM necomprimat. Cu toate acestea, algoritmii cu scop special pot da rezultate semnificativ mai bune decât Delta + LZMA2. Acest lucru este valabil mai ales în cazul audio, care se comprimă mai repede și mai bine, de exemplu, cu B<flac>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "I<Opțiuni> acceptate:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<distanța>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "Specifică I<distanța> calculului delta în octeți. I<Distanța> trebuie să fie 1\\(en256. Valoarea implicită este 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "De exemplu, cu B<dist=2> și intrare de opt octeți: A1 B1 A2 B3 A3 B5 A4 B7, ieșirea va fi: A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Alte opțiuni"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Suprimă avertismentele și notificările. Specificați acest lucru de două ori pentru a suprima și erorile. Această opțiune nu are niciun efect asupra stării de ieșire. Adică, chiar dacă o avertizare a fost suprimată, starea de ieșire pentru a indica o avertizare este încă utilizată."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Oferă informații detaliate. Dacă ieșirea de eroare standard este conectată la un terminal, B<xz> va afișa un indicator de progres. Specificarea opțiunii B<--verbose> de două ori, va avea ca rezultat oferirea de informații și mai detaliate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "Indicatorul de progres afișează următoarele informații:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "Procentul de completare este afișat dacă se cunoaște dimensiunea fișierului de intrare. Adică, procentul nu poate fi afișat la procesarea fișierului prin conducte(pipe)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Cantitatea de date comprimate produse (comprimare) sau consumate (decomprimare)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Cantitatea de date necomprimate consumate (comprimare) sau produse (decomprimare)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Raportul de comprimare, care se calculează împărțind cantitatea de date comprimate procesate până acum la cantitatea de date necomprimate procesate până acum."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Viteza de comprimare sau decomprimare. Aceasta este măsurată drept cantitatea de date necomprimate consumate (comprimare) sau produse (decomprimare) pe secundă. Este afișată după ce au trecut câteva secunde de când B<xz> a început procesarea fișierului."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Timpul scurs în format M:SS sau H:MM:SS."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "Timpul rămas estimat este afișat numai atunci când dimensiunea fișierului de intrare este cunoscută și au trecut deja câteva secunde de când B<xz> a început procesarea fișierului. Ora este afișată într-un format mai puțin precis, care nu are niciodată două puncte, de exemplu, 2 min 30 s."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Când ieșirea de eroare standard nu este un terminal, B<--verbose> va face B<xz> să imprime numele fișierului, dimensiunea comprimată, dimensiunea necomprimată, raportul de comprimare și, eventual, de asemenea, viteza și timpul scurs pe o singură linie la ieșirea de eroare standard după comprimarea sau decomprimarea fișierului. Viteza și timpul scurs sunt incluse numai atunci când operațiunea a durat cel puțin câteva secunde. Dacă operațiunea nu s-a încheiat, de exemplu, din cauza întreruperii din partea utilizatorului, se imprimă și procentul de completare dacă se cunoaște dimensiunea fișierului de intrare."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Nu comută starea de ieșire la 2 chiar dacă a fost detectată o condiție care merită avertizată. Această opțiune nu afectează nivelul de detaliere al informațiilor, astfel încât atât B<--quiet> cât și B<--no-warn> trebuie folosite pentru a nu afișa avertismente și pentru a nu modifica starea de ieșire."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Afișează mesajele într-un format care poate fi analizat de mașină. Acest lucru are scopul de a ușura scrierea interfețelor în care se dorește să se folosească B<xz> în loc de liblzma, ceea ce poate fi cazul cu diferite scripturi. Ieșirea cu această opțiune activată este menită să fie stabilă în toate versiunile B<xz>. Consultați secțiunea B<MOD ROBOT> pentru detalii."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "Afișează, într-un format care poate fi citit de om, câtă memorie fizică (RAM) și câte fire de execuție de procesor B<xz> crede că are sistemul și limitele de utilizare a memoriei pentru comprimare și decomprimare și iese cu succes."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Afișează un mesaj de ajutor care descrie opțiunile cele mai frecvent utilizate și iese cu succes."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Afișează un mesaj de ajutor care descrie toate caracteristicile B<xz> și iese cu succes"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Afișează numărul versiunii B<xz> și liblzma într-un format care poate fi citit de om. Pentru a obține rezultate analizabile de mașină, specificați B<--robot> înainte de B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "MOD ROBOT"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr "Modul robot este activat cu opțiunea B<--robot>. Face ieșirea lui B<xz> mai ușor de analizat de către alte programe.  În prezent, opțiunea B<--robot> este acceptată numai împreună cu opțiunile B<--list>, B<--filters-help>, B<--info-memory>, și B<--version>. Va fi acceptată pentru comprimare și decomprimare în viitor."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Modul listă"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> utilizează o ieșire separată de tabulatori. Prima coloană a fiecărei linii are un șir care indică tipul de informații găsite pe acea linie:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Aceasta este întotdeauna prima linie când începe să se listeze un fișier. A doua coloană de pe linie este numele fișierului."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Această linie conține informații generale despre fișierul B<.xz>. Această linie este întotdeauna tipărită după linia B<name>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Acest tip de linie este utilizat numai atunci când a fost specificată opțiunea B<--verbose>. Există tot atâtea linii B<stream> câte fluxuri există în fișierul B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Acest tip de linie este utilizat numai atunci când a fost specificată opțiunea B<--verbose>. Există tot atâtea linii B<block> câte blocuri există în fișierul B<.xz>. Liniile B<block> sunt afișate după toate liniile B<stream>; tipurile diferite de linii nu sunt intercalate."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Acest tip de linie este folosit numai atunci când opțiunea B<--verbose> a fost specificată de două ori. Această linie este afișată după toate liniile B<block>. Ca și linia B<file>, linia B<summary> conține informații generale despre fișierul B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Această linie este întotdeauna ultima linie din lista afișată la ieșire. Aceasta arată numărul total și dimensiunile."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Coloanele din liniile B<file>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Numărul de fluxuri din fișier"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Numărul total de blocuri din fluxuri"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Dimensiunea comprimată a fișierului"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Dimensiunea necomprimată a fișierului"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Raportul de comprimare, de exemplu, B<0,123>. Dacă raportul este peste 9,999, în locul raportului sunt afișate trei liniuțe (B<--->)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Lista de nume de verificare a integrității, separate prin virgule. Următoarele șiruri sunt utilizate pentru tipurile de verificare cunoscute: B<None>, B<CRC32>, B<CRC64> și B<SHA-256>. Pentru tipurile de verificări necunoscute, se utilizează B<Unknown->I<N>, unde I<N> este ID-ul de verificare ca număr zecimal (una sau două cifre)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Dimensiunea totală a umpluturii fluxului din fișier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Coloanele din liniile B<stream>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Numărul fluxului (primul flux este 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Numărul de blocuri din flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Poziția de pornire a comprimării"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Poziția de pornire a decomprimării"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Dimensiune comprimată (nu include umplutura fluxului)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Dimensiune necomprimată"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Raport de comprimare"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Numele verificării de integritate"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Dimensiunea umpluturii fluxului"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Coloanele din liniile B<block>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Numărul fluxului care conține acest bloc"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Numărul blocului în raport cu începutul fluxului (primul bloc este 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Numărul blocului în raport cu începutul fișierului"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Poziția de pornire a comprimării în raport cu începutul fișierului"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Poziția de pornire necomprimată în raport cu începutul fișierului"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Dimensiunea totală comprimată a blocului (include antetele)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Dacă opțiunea B<--verbose> a fost specificată de două ori, coloane suplimentare sunt incluse pe liniile B<block>. Acestea nu sunt afișate cu o singură specificare a opțiunii B<--verbose>, deoarece obținerea acestor informații necesită multe căutări și, prin urmare, poate fi lentă:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Valoarea verificării integrității în hexazecimal"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Dimensiunea antetului blocului"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Indicatori de bloc: B<c> indică faptul că este prezentă dimensiunea comprimată, iar B<u> indică faptul că este prezentă dimensiunea necomprimată. Dacă indicatorul nu este determinat, este afișată o liniuță (B<->) pentru a menține lungimea șirului fixă. Pot fi adăugate noi indicatoare la sfârșitul șirului, în viitor."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Dimensiunea datelor comprimate reale din bloc (acest lucru exclude antetul blocului, umplutura blocului și câmpurile de verificare)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Cantitatea de memorie (în octeți) necesară pentru a decomprima acest bloc cu această versiune B<xz>"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Lanț de filtrare. Rețineți că majoritatea opțiunilor utilizate în timpul comprimării nu pot fi cunoscute, deoarece doar opțiunile necesare pentru decomprimare sunt stocate în anteturile B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Coloanele din liniile B<summary>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Cantitatea de memorie (în octeți) necesară pentru a decomprima acest fișier cu această versiune B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> sau B<no> indicând dacă toate antetele blocurilor au atât dimensiunea comprimată, cât și dimensiunea necomprimată stocate în ele"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Începând cu> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Versiunea B<xz> minimă necesară pentru a decomprima fișierul"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Coloanele din linia B<totals>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Numărul de fluxuri"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Numărul de blocuri"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Dimensiunea comprimată"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Raportul mediu de comprimare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Lista de nume de verificare a integrității, separate prin virgule, care au fost prezente în fișiere"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Dimensiunea umpluturii fluxului"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Numărul de fișiere. Aceasta este aici pentru a păstra ordinea coloanelor anterioare la fel ca pe liniile B<file>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Dacă opțiunea B<--verbose> a fost specificată de două ori, pe linia B<totals> sunt incluse coloane suplimentare:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Cantitatea maximă de memorie (în octeți) necesară pentru a decomprima fișierele cu această versiune B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Versiunile viitoare pot adăuga noi tipuri de linii și pot fi adăugate coloane noi la tipurile de linii existente, dar coloanele existente nu vor fi modificate."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Filters help"
msgstr "Ajutor pentru filtrare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr "B<xz --robot --filters-help> afișează filtrele acceptate în următorul format:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr "I<filtru>B<:>I<opțiune>B<=E<lt>>I<valoare>B<E<gt>,>I<opțiune>B<=E<lt>>I<valoare>B<E<gt>>..."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<filter>"
msgstr "B<filtru>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the filter"
msgstr "Numele filtrului"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<option>"
msgstr "I<opțiune>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr "Numele unei opțiuni specifice unui filtru"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr "I<valoare>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr "Intervalele numerice I<valoare> apar ca B<E<lt>>I<min>B<->I<max>B<E<gt>>. Alegerile I<valoare> de tip șir de caractere sunt afișate în cadrul B<E<lt> E<gt>> și separate de un caracter B<|>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr "Fiecare filtru este afișat pe propria linie."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Informații privind limita memoriei"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr "B<xz --robot --info-memory> afișează o singură linie cu multiple coloane separate prin tabulatoare:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Cantitatea totală de memorie fizică (RAM) în octeți."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limita de utilizare a memoriei pentru comprimare în octeți (B<--memlimit-compress>). O valoare specială de B<0> indică configurarea implicită, care pentru modul cu un singur fir este la fel ca fără limită."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limita de utilizare a memoriei pentru decomprimare în octeți (B<--memlimit-decompress>). O valoare specială de B<0> indică configurarea implicită, care pentru modul cu un singur fir este la fel ca fără limită."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "Începând cu B<xz> 5.3.4alpha: Utilizarea memoriei pentru decomprimarea cu mai multe fire în octeți (B<--memlimit-mt-decompress>). Acesta nu este niciodată zero, deoarece o valoare implicită specifică sistemului afișată în coloana 5 este utilizată dacă nu a fost specificată în mod explicit nicio limită. De asemenea, aceasta nu este niciodată mai mare decât valoarea din coloana 3, chiar dacă a fost specificată o valoare mai mare cu B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "Începând cu B<xz> 5.3.4alpha: o limită implicită de utilizare a memoriei specifică sistemului, care este utilizată pentru a limita numărul de fire de execuție atunci când se comprimă cu un număr automat de fire de execuție (B<--threads=0>) și nicio limită de utilizare a memoriei nu fost specificată cu (B<--memlimit-compress>). Aceasta este, de asemenea, utilizată ca valoare implicită pentru B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "Începând cu B<xz> 5.3.4alpha: numărul de fire de execuție de procesor disponibile."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "În viitor, rezultatul B<xz --robot --info-memory> poate avea mai multe coloane, dar niciodată mai mult de o singură linie."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Versiunea"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr "B<xz --robot --version> va afișa numărul versiunii B<xz> și liblzma în următorul format:"

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Versiunea majoră."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Versiunea minoră.  Numerele pare sunt prezente în versiunile stabile. Numerele impare sunt prezente în versiunile alfa sau beta."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Nivelul de plasture(patch) pentru versiunile stabile sau doar un contor pentru versiunile de dezvoltare."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Stabilitate.  0 este alfa, 1 este beta și 2 este stabil. I<S> trebuie să fie întotdeauna 2 atunci când I<AAA> este par."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> sunt aceleași pe ambele linii dacă B<xz> și liblzma sunt din aceeași versiune XZ Utils."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Exemple: 4.999.9beta este B<49990091> și 5.0.0 este B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "STARE DE IEȘIRE"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Totul este bine."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "A apărut o eroare."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "A apărut ceva care merită să fie avertizat, dar nu au apărut erori reale."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Notificările (nu avertismentele sau erorile) afișate la ieșirea de eroare standard nu afectează starea de ieșire."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "VARIABILE DE MEDIU"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> analizează liste de opțiuni separate prin spații din variabilele de mediu B<XZ_DEFAULTS> și B<XZ_OPT>, în această ordine, înainte de a analiza opțiunile din linia de comandă. Rețineți că numai opțiunile sunt analizate din variabilele de mediu; toate non-opțiunile sunt ignorate în tăcere. Analiza se face cu funcția B<getopt_long>(3) care este folosită și pentru argumentele liniei de comandă."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr "B<Avertisment:> Prin definirea acestor variabile de mediu, se modifică efectiv programele și scripturile care rulează B<xz>.  De cele mai multe ori este sigur să se definească limitele de utilizare a memoriei, numărul de fire și opțiunile de comprimare prin intermediul variabilelor de mediu.  Cu toate acestea, unele opțiuni pot întrerupe scripturile. Un exemplu evident este B<--help> care face ca B<xz> să afișeze textul de ajutor în loc să comprime sau să decomprime un fișier. Exemple mai subtile sunt B<--quiet> și B<--verbose>. În multe cazuri funcționează bine activarea indicatorului de progres folosind B<--verbose>, dar în unele situații mesajele suplimentare creează probleme. Nivelul de detaliere al mesajelor afectează, de asemenea, comportamentul lui B<--list>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Opțiuni implicite specifice utilizatorului sau la nivelul întregului sistem. De obicei, acest lucru este specificat într-un script de inițializare shell pentru a activa limitatorul de utilizare a memoriei lui B<xz> implicit sau pentru a stabili numărul implicit de fire. Excluzând scripturile de inițializare shell și cazurile speciale similare, scripturile nu trebuie niciodată să modifice sau să dezactiveze B<XZ_DEFAULTS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Acest lucru este pentru transmiterea opțiunilor către B<xz> atunci când nu este posibil să definiți opțiunile direct în linia de comandă a B<xz>. Acesta este cazul când B<xz> este rulat de un script sau de un instrument, de exemplu, GNU B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Scripturile pot folosi B<XZ_OPT>, de exemplu, pentru a configura opțiunile implicite de comprimare specifice scriptului. Se recomandă totuși să se permită utilizatorilor să înlocuiască B<XZ_OPT> dacă acest lucru este rezonabil. De exemplu, în scripturile B<sh>(1) se poate folosi ceva de genul acesta:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "COMPATIBILITATE CU LZMA-UTILS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "Sintaxa liniei de comandă a lui B<xz> este practic o super-colecție de B<lzma>, B<unlzma> și B<lzcat> așa cum se găsește în LZMA Utils 4.32.x. În cele mai multe cazuri, este posibil să înlocuiți LZMA Utils cu XZ Utils fără a întrerupe scripturile existente. Există totuși unele incompatibilități, care uneori pot cauza probleme."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Niveluri de comprimare prestabilite"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "Numerotarea nivelurilor de comprimare prestabilite nu este identică în B<xz> și LZMA Utils. Cea mai importantă diferență este modul în care dimensiunile dicționarului sunt atribuite diferitelor niveluri prestabilite. Dimensiunea dicționarului este aproximativ egală cu memoria utilizată la decomprimare."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Nivel"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "N/A"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Diferențele de dimensiune a dicționarului afectează deasemenea cantitatea de memorie utilizată la comprimare dar există și alte diferențe între LZMA Utils și XZ Utils, care fac diferența și mai mare:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Nivelul prestabilit implicit în LZMA Utils este B<-7>, în timp ce în XZ Utils este B<-6>, deci ambele folosesc un dicționar de 8Mio în mod implicit."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Fișiere .lzma transmise în flux vs. netransmise în flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "Dimensiunea necomprimată a fișierului poate fi stocată în antetul B<.lzma>. LZMA Utils face asta atunci când comprimă fișiere obișnuite. Alternativa este să marcați că dimensiunea necomprimată este necunoscută și să folosiți marcajul de sfârșit de încărcare pentru a indica unde ar trebui să se oprească decomprimarea. LZMA Utils folosește această metodă atunci când dimensiunea necomprimată nu este cunoscută, ceea ce este cazul, de exemplu, când se folosesc conducte."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> acceptă decomprimarea fișierelor B<.lzma> cu sau fără marcaj de sfârșit de încărcare, dar toate fișierele B<.lzma> create de B<xz> vor folosi marcajul de sfârșit de încărcare și vor avea dimensiunea necomprimată marcată ca necunoscută în antetul B<.lzma>. Aceasta poate fi o problemă în unele situații mai puțin frecvente. De exemplu, un instrument de decomprimare B<.lzma> încorporat într-un dispozitiv poate funcționa numai cu fișiere care au dimensiunea necomprimată cunoscută. Dacă întâmpinați această problemă, trebuie să utilizați LZMA Utils sau LZMA SDK pentru a crea fișiere B<.lzma> cu dimensiunea necomprimată cunoscută."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Fișiere .lzma neacceptate"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "Formatul B<.lzma> permite valori I<lc> de până la 8 și valori I<lp> de până la 4. LZMA Utils poate decomprima fișiere cu orice I<lc> și I<lp>, dar creează întotdeauna fișiere cu B<lc=3> și B<lp=0>. Crearea de fișiere cu alte I<lc> și I<lp> este posibilă cu B<xz> și cu LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "Implementarea filtrului LZMA1 în liblzma necesită ca suma I<lc> și I<lp> să nu depășească 4. Altfel, fișierele B<.lzma>, care depășesc această limitare, nu pot fi decomprimate cu B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "LZMA Utils creează numai fișiere B<.lzma> care au o dimensiune de dicționar de 2^I<n> (o putere de 2), dar acceptă fișiere cu orice dimensiune de dicționar. liblzma acceptă numai fișierele B<.lzma> care au dimensiunea de dicționar de 2^I<n> sau 2^I<n> + 2^(I<n>-1). Acest lucru este pentru a reduce numărul de „fals pozitiv” atunci când se detectează fișiere B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Aceste limitări nu ar trebui să fie o problemă în practică, deoarece practic toate fișierele B<.lzma> au fost comprimate cu opțiuni pe care liblzma le va accepta."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Resturi rămase"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Când decomprimă, LZMA Utils ignoră în tăcere totul după primul flux B<.lzma>. În majoritatea situațiilor, aceasta este o eroare. Aceasta înseamnă, de asemenea, că LZMA Utils nu acceptă decomprimarea fișierelor B<.lzma> concatenate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Dacă au rămas date după primul flux B<.lzma>, B<xz> consideră că fișierul este corupt, cu excepția cazului în care a fost utilizată opțiunea B<--single-stream>. Acest lucru poate rupe scripturile obscure(scrise deficitar) care presupun că resturile rămase sunt ignorate."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "NOTE"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "Rezultatul comprimării poate varia"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "Ieșirea exactă comprimată produsă din același fișier de intrare necomprimat poate varia între versiunile XZ Utils, chiar dacă opțiunile de comprimare sunt identice. Acest lucru se datorează faptului că instrumentul codificator poate fi îmbunătățit (comprimare mai rapidă sau mai bună) fără a afecta formatul fișierului. Ieșirea poate varia chiar și între compilările diferite ale aceleiași versiuni XZ Utils, dacă sunt utilizate opțiuni diferite de compilare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Cele de mai sus înseamnă că odată ce opțiunea B<--rsyncable> a fost utilizată, fișierele rezultate nu vor fi neapărat sincronizate cu rsync decât dacă atât fișierele vechi, cât și cele noi au fost comprimate cu aceeași versiune xz. Această problemă poate fi remediată dacă o parte a implementării codificatorului este înghețată pentru a menține stabilă ieșirea „rsyncabilă” între versiunile xz."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Instrumente de decomprimare .xz încorporate"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "Implementările instrumentului de decomprimare B<.xz> încorporat, cum ar fi XZ Embedded, nu acceptă neapărat fișiere create cu tipuri de I<verificare> a integrității, altele decât B<none> și B<crc32>. Deoarece valoarea implicită este B<--check=crc64>, trebuie să utilizați B<--check=none> sau B<--check=crc32> atunci când creați fișiere pentru sistemele încorporate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "În afara sistemelor încorporate, toate instrumentele de decomprimare în format B<.xz> acceptă toate tipurile de I<verificare> sau cel puțin pot decomprima fișierul fără a efectua verificarea integrității dacă acel tip de I<verificare> nu este acceptat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded acceptă filtre BCJ, dar numai cu poziție de pornire implicită."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "EXEMPLE"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Bazice"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Comprimă fișierul I<foo> în I<foo.xz> folosind nivelul de comprimare implicit (B<-6>) și elimină fișierul I<foo> dacă comprimarea are succes:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz foo\\fR\n"
msgstr "\\f(CRxz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Decomprimă I<bar.xz> în I<bar> și nu elimină I<bar.xz> chiar dacă decomprimarea este efectuată cu succes:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "\\f(CRxz -dk bar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "Creează I<baz.tar.xz> cu nivelul prestabilit B<-4e> (B<-4 --extreme>), care este mai lent decât nivelul prestabilit implicit B<-6>, dar necesită mai puțină memorie pentru comprimare și decomprimare (48Mio și, respectiv, 5Mio):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Un amestec de fișiere comprimate și necomprimate poate fi decomprimat la ieșirea standard cu o singură comandă:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Comprimarea în paralel a mai multor fișiere"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "În sisteme GNU și *BSD, B<find>(1) și B<xargs>(1) pot fi utilizate pentru a paraleliza comprimarea mai multor fișiere:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "Opțiunea B<-P> pentru comanda B<xargs>(1) stabilește numărul de procese paralele B<xz>.  Cea mai bună valoare pentru opțiunea B<-n> depinde de câte fișiere trebuie să fie comprimate. Dacă există doar câteva fișiere, valoarea ar trebui probabil să fie 1; cu zeci de mii de fișiere, 100 sau chiar mai mult poate să fie valoarea potrivită pentru a reduce numărul de procese B<xz> pe care B<xargs>(1) le va crea în final."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "Opțiunea B<-T1> pentru B<xz> este acolo pentru a-l forța să ruleze în modul cu un singur fir de execuție, deoarece B<xargs>(1) este folosit pentru a controla cantitatea de paralelizare."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Modul robot"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Calculează câți octeți au fost salvați în total după comprimarea mai multor fișiere:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Un script poate dori să afle dacă folosește o versiune B<xz> suficient de nouă.  Următorul script B<sh>(1) verifică dacă numărul versiunii instrumentului B<xz> este cel puțin 5.0.0. Această metodă este compatibilă cu versiunile beta vechi, care nu acceptau opțiunea B<--robot>:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Versiunea dumneavoastră de „xz” este prea veche!\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Stabilește o limită de utilizare a memoriei pentru decomprimare folosind variabila de mediu B<XZ_OPT>, dar dacă o limită a fost deja stabilită, nu o mărește:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "Cea mai simplă utilizare a lanțurilor de filtrare personalizate este personalizarea unei opțiuni prestabilite LZMA2. Acest lucru poate fi util, deoarece opțiunile prestabilite acoperă doar un subset al combinațiilor potențial utile de opțiuni de comprimare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "Coloanele CPUComp din tabelele de descriere a opțiunilor B<-0> ... B<-9> și B<--extreme> sunt utile atunci când personalizați opțiunilor prestabilite LZMA2. Iată părțile relevante colectate din aceste două tabele:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Dacă știți că un fișier necesită un dicționar oarecum mare (de exemplu, 32Mio) pentru a se comprima bine, dar doriți să-l comprimați mai repede decât ar face B<xz -8>, o opțiune prestabilită cu o valoare CPUComp scăzută (de exemplu, 1) poate fi modificată pentru a utiliza un dicționar mai mare:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Cu anumite fișiere, comanda de mai sus poate fi mai rapidă decât B<xz -6> în timp ce comprimă semnificativ mai bine. Cu toate acestea, trebuie subliniat că doar unele fișiere se beneficiază de un dicționar mare, păstrând în același timp valoarea CPUComp scăzută. Cea mai evidentă situație, în care un dicționar mare poate ajuta foarte mult, este o arhivă care conține fișiere foarte asemănătoare de cel puțin câțiva megaocteți fiecare. Dimensiunea dicționarului trebuie să fie semnificativ mai mare decât orice fișier individual pentru a permite LZMA2 să profite din plin de asemănările dintre fișierele consecutive."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Dacă utilizarea unei mari cantități de memorie pentru comprimare și decomprimare este în regulă, iar fișierul comprimat are cel puțin câteva sute de megaocteți, poate fi util să folosiți un dicționar și mai mare decât cei 64Mio pe care i-ar folosi B<xz -9>:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Utilizarea opțiunii B<-vv> (B<--verbose --verbose>) ca în exemplul de mai sus, poate fi utilă pentru a vedea cerințele de memorie la comprimare și decomprimare. Amintiți-vă că utilizarea unui dicționar mai mare decât dimensiunea fișierului necomprimat este risipă de memorie, de aceea, comanda de mai sus nu este utilă pentru fișiere mici."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "Uneori, timpul de comprimare nu contează, dar utilizarea memoriei la decomprimare trebuie menținută la un nivel scăzut, de exemplu, pentru a face posibilă decomprimarea fișierului pe un sistem încorporat. Următoarea comandă folosește B<-6e> (B<-6 --extreme>) ca bază și fixează dimensiunea dicționarului la doar 64Kio. Fișierul rezultat poate fi decomprimat cu XZ Embedded (de aceea există B<--check=crc32>) folosind aproximativ 100Kio de memorie."

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Dacă doriți să stoarceți cât mai mulți octeți posibil, ajustarea numărului de biți de context literal (I<lc>) și a numărului de biți de poziție (I<pb>) poate ajuta uneori. Ajustarea numărului de biți de poziție literală (I<lp>) ar putea ajuta, de asemenea, dar de obicei I<lc> și I<pb> sunt mai importante. De exemplu, o arhivă de cod sursă conține în mare parte text US-ASCII, așa că ceva precum comanda următoare, ar putea oferi un fișier „mai slăbuț” (aproximativ cu 0,1%) mai mic decât cu B<xz -6e> (încercați și fără B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 fișierul_sursă.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "Utilizarea unui alt filtru împreună cu LZMA2 poate îmbunătăți comprimarea cu anumite tipuri de fișiere. De exemplu, pentru a comprima o bibliotecă partajată x86 pe 32 de biți sau x86 pe 64 de biți folosind filtrul BCJ x86:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Rețineți că ordinea opțiunilor de filtrare este semnificativă. Dacă B<--x86> este specificată după B<--lzma2>, B<xz> va da o eroare, deoarece nu poate exista niciun filtru după LZMA2 și, de asemenea, pentru că filtrul x86 BCJ nu poate fi utilizat ca ultimul filtru din lanțul de filtrare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Filtrul Delta împreună cu LZMA2 pot da rezultate bune cu imagini bitmap. De obicei, ar trebui să întreacă comprimarea PNG, care are câteva filtre mai avansate decât delta simplă, dar utilizează Deflate pentru comprimarea reală."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "Imaginea trebuie să fie salvată în format necomprimat, de exemplu, ca TIFF necomprimat. Parametrul de distanță al filtrului Delta este fixat să se potrivească cu numărul de octeți per pixel din imagine. De exemplu, bitmap-ul RGB pe 24 de biți necesită B<dist=3> și este, de asemenea, bine să pasați B<pb=0> la LZMA2 pentru a se adapta alinierii pe trei octeți:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Dacă mai multe imagini au fost introduse într-o singură arhivă (de exemplu, B<.tar>), filtrul Delta va funcționa și pe aceasta atâta timp cât toate imaginile au același număr de octeți per pixel."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "CONSULTAȚI ȘI"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr "8 aprilie 2024"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec - Programe de decomprimare mici de fișiere .xz și .lzma"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<opțiune...>] [I<fișier...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<opțiune...>] [I<fișier...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> este un instrument de decomprimare bazat pe liblzma pentru fișierele B<.xz> (și numai B<.xz>). B<xzdec> este destinat să funcționeze ca un înlocuitor pentru B<xz>(1) în cele mai frecvente situații în care un script a fost scris pentru a utiliza B<xz --decompress --stdout> (și posibil câteva alte opțiuni frecvent utilizate) pentru a decomprima fișierele B<.xz>. B<lzmadec> este identic cu B<xzdec> cu excepția faptului că B<lzmadec> acceptă fișierele B<.lzma> în loc de fișierele B<.xz>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Pentru a reduce dimensiunea executabilului, B<xzdec> nu acceptă modul cu mai multe fire de execuție sau localizarea(afișarea mesajelor în limba stabilită de configurările regionale) și nu citește opțiunile din variabilele de mediu B<XZ_DEFAULTS> și B<XZ_OPT>. B<xzdec> nu acceptă afișarea informațiilor intermediare de progres: trimiterea semnalului B<SIGINFO> la B<xzdec> nu face nimic, iar trimiterea semnalului B<SIGUSR1> încheie procesul în loc să afișeze informații despre progres."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Ignorat pentru compatibilitate cu B<xz>(1). B<xzdec> acceptă numai decomprimarea."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Ignorat pentru compatibilitate cu B<xz>(1). B<xzdec> nu creează sau elimină niciodată niciun fișier."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Ignorat pentru compatibilitate cu B<xz>(1). B<xzdec> scrie întotdeauna datele decomprimate la ieșirea standard."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Specificarea acestui lucru o dată nu face nimic, deoarece B<xzdec> nu afișează niciodată avertismente sau notificări. Specificați acest lucru de două ori pentru a suprima erorile."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Ignorat pentru compatibilitate cu B<xz>(1). B<xzdec> nu folosește niciodată starea de ieșire 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Afișează un mesaj de ajutor și iese cu succes."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Afișează numărul versiunii B<xzdec> și liblzma."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Toate au fost bine."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> nu are niciun mesaj de avertizare precum B<xz>(1), astfel că starea de ieșire 2 nu este folosită de B<xzdec>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Utilizați B<xz>(1) în loc de B<xzdec> sau B<lzmadec> pentru utilizarea normală de zi cu zi. B<xzdec> sau B<lzmadec> sunt destinate numai situațiilor în care este important să aveți un instrument de decomprimare mai mic decât B<xz>(1), cu funcții complete."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> și B<lzmadec> nu sunt chiar atât de mici. Dimensiunea poate fi redusă și mai mult prin eliminarea caracteristicilor din liblzma în timpul compilării, dar acest lucru nu ar trebui să se facă de obicei pentru executabilele distribuite în distribuții tipice de sisteme de operare neîncorporate. Dacă aveți nevoie de un instrument de decomprimare B<.xz> cu adevărat mic, luați în considerare utilizarea XZ Embedded."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30.06.2013"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo - afișează informațiile stocate în antetul fișierului .lzma"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<fișier...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> afișează informațiile stocate în antetul fișierului B<.lzma>. Citește primii 13 octeți din I<fișierul> specificat, decodifică antetul și îl afișează la ieșirea standard în format care poate fi citit de om. Dacă nu sunt date I<fișiere> sau dacă I<fișier> este B<->, se citește intrarea standard."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "De obicei, cele mai interesante informații sunt dimensiunea necomprimată și dimensiunea dicționarului. Dimensiunea necomprimată poate fi afișată numai dacă fișierul este în varianta formatului B<.lzma> netransmis în flux. Cantitatea de memorie necesară pentru a decomprima fișierul este de câteva zeci de kiloocteți plus dimensiunea dicționarului."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> este inclus în XZ Utils în primul rând pentru compatibilitatea cu LZMA Utils."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "ERORI"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> folosește sufixul B<MB> în timp ce sufixul corect ar fi B<MiB> (2^20 octeți). Acest lucru este pentru a menține ieșirea compatibilă cu LZMA Utils."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "2025-03-06"
msgstr "6 martie 2025"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff - compară fișierele comprimate"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xzcmp> [I<opțiune...>] I<fișier1> [I<fișier2>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr "B<xzdiff> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzcmp> \\&...  (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzdiff> \\&...  (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr "B<xzcmp> și B<xzdiff> compară conținutul necomprimat a două fișiere. Datele necomprimate și opțiunile sunt transmise la B<cmp>(1) sau B<diff>(1), cu excepția cazului în care se specifică B<--help> sau B<--version>."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr "Dacă sunt specificate atât I<fișier1>, cât și I<fișier2>, acestea pot fi fișiere necomprimate sau fișiere în formate pe care B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) sau B<lz4>(1) le poate decomprima. Comenzile de decomprimare necesare sunt determinate de sufixele numelor de fișiere I<fișier1> și I<fișier2>. Se presupune că un fișier cu un sufix necunoscut este fie necomprimat, fie într-un format pe care B<xz>(1) îl poate decomprima."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr "În cazul în care se furnizează un singur nume de fișier, I<fișier1> trebuie să aibă un sufix al unui format de comprimare acceptat, iar numele pentru I<fișier2> se presupune că este I<fișier1> fără sufixul formatului de comprimare."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Comenzile B<lzcmp> și B<lzdiff> sunt furnizate pentru compatibilitate retroactivă cu LZMA Utils.  Acestea sunt depreciate și vor fi eliminate într-o versiune viitoare."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr "În cazul în care apare o eroare de decomprimare, starea de ieșire este B<2>. În caz contrar, se utilizează starea de ieșire B<cmp>(1) sau B<diff>(1)."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr "xzgrep - caută modele în fișiere posibil comprimate"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xzgrep> [I<opțiune...>] I<listă-modele> [I<fișier...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&...  (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&...  (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&...  (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr "B<xzgrep> invocă B<grep>(1) asupra conținutului necomprimat al fișierelor. Formatele I<fișierelor> sunt determinate de sufixele numelor de fișiere. Orice I<fișier> cu un sufix acceptat de B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) sau B<lz4>(1) va fi decomprimat; toate celelalte fișiere sunt presupuse a fi necomprimate."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr "Dacă nu se specifică niciun I<fișier> sau dacă I<fișier> este B<->, se citește intrarea standard. Atunci când se citește de la intrarea standard, sunt decomprimate numai fișierele acceptate de B<xz>(1).  Se presupune că celelalte fișiere sunt deja în format necomprimat."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr "Sunt acceptate majoritatea I<opțiunilor> din B<grep>(1). Cu toate acestea, următoarele opțiuni nu sunt acceptate:"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-r>, B<--recursive>"
msgstr "B<-r>, B<--recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-R>, B<--dereference-recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-d>, B<--directories=>I<acțiune>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-Z>, B<--null>"
msgstr "B<-Z>, B<--null>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-z>, B<--null-data>"
msgstr "B<-z>, B<--null-data>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--include=>I<glob>"
msgstr "B<--include=>I<global>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr "B<--exclude=>I<global>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-from=>I<file>"
msgstr "B<--exclude-from=>I<fișier>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr "B<--exclude-dir=>I<global>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr "B<xzegrep> este un alias pentru B<xzgrep -E>. B<xzfgrep> este un alias pentru B<xzgrep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Comenzile B<lzgrep>, B<lzegrep> și B<lzfgrep> sunt furnizate pentru compatibilitate retroactivă cu LZMA Utils. Acestea sunt depreciate și vor fi eliminate într-o versiune viitoare."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "A fost găsită cel puțin o potrivire din cel puțin unul dintre fișierele de la intrare. Nu au apărut erori."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "Nu au fost găsite potriviri din niciunul dintre fișierele de la intrare. Nu au apărut erori."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "A apărut una sau mai multe erori. Nu se cunoaște dacă au fost găsite potriviri."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr "Dacă variabila de mediu B<GREP> este stabilită la o valoare nevidă, aceasta este utilizată în locul lui B<grep>, B<grep -E> sau B<grep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless - vizualizează fișierele (text) comprimate xz sau lzma"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<fișier>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<fișier>...] (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr "B<xzless> este un filtru care afișează textul din fișierele comprimate pe un terminal. Fișierele acceptate de B<xz>(1) sunt decomprimate; se presupune că celelalte fișiere sunt deja în format necomprimat. Dacă nu se dă nici un I<fișier>, B<xzless> citește de la intrarea standard."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> folosește B<less>(1) pentru a-și prezenta rezultatul. Spre deosebire de B<xzmore>, alegerea sa de pager nu poate fi modificată prin definirea unei variabile de mediu. Comenzile se bazează atât pe B<more>(1) cât și pe B<vi>(1) și permit mișcarea înainte și înapoi și căutarea. Consultați manualul B<less>(1) pentru mai multe informații."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Comanda numită B<lzless> este furnizată pentru compatibilitatea cu LZMA Utils. Aceasta este depreciată și va fi eliminată într-o versiune viitoare."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "O listă de caractere speciale pentru shell. Definită de B<xzless>, cu excepția cazului în care este deja definită în mediu."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Aceasta este definită în linia de comandă pentru a invoca instrumentul de decomprimare B<xz>(1) pentru preprocesarea fișierelor de intrare pentru B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore - vizualizează fișierele (text) comprimate xz sau lzma"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzmore> [I<fișier>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzmore> [I<fișier>...] (DEPRECIATĂ)"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr "B<xzmore> afișează textul din fișierele comprimate pe un terminal folosind B<more>(1). Fișierele acceptate de B<xz>(1) sunt decomprimate; se presupune că celelalte fișiere sunt deja în format necomprimat. Dacă nu se dă nici un I<fișiere>, B<xzmore> citește de la intrarea standard. Consultați manualul B<more>(1) pentru comenzile de la tastatură."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr "Rețineți că este posibil ca derularea înapoi să nu fie posibilă în funcție de implementarea lui B<more>(1). Acest lucru se datorează faptului că B<xzmore> utilizează o conductă pentru a transmite datele decomprimate către B<more>(1). B<xzless>(1) utilizează B<less>(1) care oferă caracteristici mai avansate."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Comanda B<lzmore> este furnizată pentru compatibilitate retroactivă cu LZMA Utils. Aceasta este depreciată și va fi eliminată într-o versiune viitoare."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr "B<PAGER>"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr "Dacă variabila de mediu B<PAGER>, este definită, valoarea sa este utilizată ca paginator în loc de B<more>(1)."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
