/* SPDX-License-Identifier: 0BSD */

/*
 * Author: <PERSON><PERSON>
 */

#include <winresrc.h>
#ifdef HAVE_CONFIG_H
# include "config.h"
#endif
#define LZMA_H_INTERNAL
#define LZMA_H_INTERNAL_RC
#include "lzma/version.h"

#ifndef MY_BUILD
# define MY_BUILD 0
#endif
#define MY_VERSION LZMA_VERSION_MAJOR,LZMA_VERSION_MINOR,LZMA_VERSION_PATCH,MY_BUILD

#define MY_FILENAME    MY_NAME MY_SUFFIX
#define MY_COMPANY     "The Tukaani Project <https://tukaani.org/>"
#define MY_PRODUCT     PACKAGE_NAME " <" PACKAGE_URL ">"

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US

VS_VERSION_INFO VERSIONINFO
FILEVERSION MY_VERSION
PRODUCTVERSION MY_VERSION
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS 0
FILEOS VOS_NT_WINDOWS32
FILETYPE MY_TYPE
FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", MY_COMPANY
            VALUE "FileDescription", MY_DESC
            VALUE "FileVersion", LZMA_VERSION_STRING
            VALUE "InternalName", MY_NAME
            VALUE "OriginalFilename", MY_FILENAME
            VALUE "ProductName", MY_PRODUCT
            VALUE "ProductVersion", LZMA_VERSION_STRING
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

/* Omit the manifest on Cygwin and MSYS2 (both define __CYGWIN__). */
#if MY_TYPE == VFT_APP && !defined(__CYGWIN__)
CREATEPROCESS_MANIFEST_RESOURCE_ID RT_MANIFEST "w32_application.manifest"
#endif
