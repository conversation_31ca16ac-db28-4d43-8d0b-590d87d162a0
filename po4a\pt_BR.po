# Brazilian Portuguese translations for xz package
# Traduções em português brasileiro para o pacote xz.
# This file is put in the public domain.
# <PERSON> <<EMAIL>>, 2022-2023.
#
msgid ""
msgstr ""
"Project-Id-Version: xz-man 5.4.0-pre2\n"
"POT-Creation-Date: 2025-03-25 12:28+0200\n"
"PO-Revision-Date: 2023-01-26 13:29-0300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Brazilian Portuguese <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n > 1)\n"
"X-Generator: Gtranslator 42.0\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr ""

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "XZ Utils"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "NOME"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat - Compacta ou descompacta arquivos .xz e .lzma"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "SINOPSE"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<opção...>] [I<arquivo...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "COMANDOS APELIDOS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> é equivalente a  B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> é equivalente a  B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> é equivalente a  B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> é equivalente a  B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> é equivalente a  B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Ao escrever scripts que precisam descompactar arquivos, é recomendável sempre usar o nome B<xz> com os argumentos apropriados (B<xz -d> ou B<xz -dc>) em vez dos nomes B<unxz> e B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "DESCRIÇÃO"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> é uma ferramenta de compactação de dados de uso geral com sintaxe de linha de comando semelhante ao B<gzip>(1) e ao B<bzip2>(1). O formato de arquivo nativo é o formato B<.xz>, mas o formato legado B<.lzma> usado por LZMA Utils e fluxos compactados brutos sem cabeçalhos de formato de contêiner também são suportados. Além disso, a descompactação do formato B<.lz> usado por B<lzip> é suportada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> compacta ou descompacta cada I<arquivo> de acordo com o modo de operação selecionado. Se nenhum I<arquivo> for fornecido ou I<arquivo> for B<->, B<xz> lê da entrada padrão e grava os dados processados na saída padrão. B<xz> recusará (exibirá um erro e ignorará o I<arquivo>) para gravar dados compactados na saída padrão se for um terminal. Da mesma forma, B<xz> se recusará a ler dados compactados da entrada padrão se for um terminal."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "A menos que B<--stdout> seja especificado, I<arquivos> diferentes de B<-> são gravados em um novo arquivo cujo nome é derivado do nome I<arquivo> de origem:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "Ao compactar, o sufixo do formato de arquivo de destino (B<.xz> ou B<.lzma>) é anexado ao nome do arquivo de origem para obter o nome do arquivo de destino."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "Ao descompactar, o sufixo B<.xz>, B<.lzma> ou B<.lz> é removido do nome do arquivo para obter o nome do arquivo de destino. B<xz> também reconhece os sufixos B<.txz> e B<.tlz> e os substitui pelo sufixo B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Se o arquivo de destino já existir, um erro será exibido e I<arquivo> será ignorado."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "A menos que grave na saída padrão, B<xz> exibirá um aviso e pulará o I<arquivo> se qualquer um dos seguintes se aplicar:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<Arquivo> não é um arquivo normal. Links simbólicos não são seguidos e, portanto, não são considerados arquivos comuns."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "I<Arquivo> tem mais de um link físico."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "I<File> tem setuid, setgid ou sticky bit definido."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "O modo de operação está definido para compactar e o I<arquivo> já possui um sufixo do formato de arquivo de destino (B<.xz> ou B<.txz> ao compactar para o formato B<.xz> e B<.lzma > ou B<.tlz> ao compactar para o formato B<.lzma>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "O modo de operação está definido para descompactar e o I<arquivo> não possui um sufixo de nenhum dos formatos de arquivo suportados (B<.xz>, B<.txz>, B<.lzma>, B<.tlz> , ou B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Depois de compactar ou descompactar com êxito o I<arquivo>, o B<xz> copia o dono, grupo, permissões, horário de acesso e horário de modificação do I<arquivo> de origem para o arquivo de destino. Se a cópia do grupo falhar, as permissões serão modificadas para que o arquivo de destino não se torne acessível a usuários que não têm permissão para acessar o I<arquivo> de origem. B<xz> ainda não oferece suporte à cópia de outros metadados, como listas de controle de acesso ou atributos estendidos."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Depois que o arquivo de destino for fechado com êxito, o I<arquivo> de origem será removido, a menos que B<--keep> tenha sido especificado. O I<arquivo> de origem nunca é removido se a saída for gravada na saída padrão ou se ocorrer um erro."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "O envio de B<SIGINFO> ou B<SIGUSR1> para o processo do B<xz> faz com que ele imprima informações de andamento para erro padrão. Isso tem uso limitado, pois quando o erro padrão é um terminal, usar B<--verbose> exibirá um indicador de progresso de atualização automática."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Uso de memória"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "O uso de memória de B<xz> varia de algumas centenas de kilobytes a vários gigabytes, dependendo das configurações de compactação. As configurações usadas ao compactar um arquivo determinam os requisitos de memória do descompactador. Normalmente, o descompactador precisa de 5\\ % a 20\\ % da quantidade de memória que o compactador precisou ao criar o arquivo. Por exemplo, descompactar um arquivo criado com B<xz -9> atualmente requer 65\\ MiB de memória. Ainda assim, é possível ter arquivos B<.xz> que requerem vários gigabytes de memória para descompactar."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "Especialmente os usuários de sistemas mais antigos podem achar irritante a possibilidade de uso de memória muito grande. Para evitar surpresas desconfortáveis, o B<xz> possui um limitador de uso de memória embutido, que está desabilitado por padrão. Embora alguns sistemas operacionais forneçam maneiras de limitar o uso de memória dos processos, confiar nele não foi considerado flexível o suficiente (por exemplo, usar B<ulimit>(1) para limitar a memória virtual tende a prejudicar B<mmap>(2))."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "O limitador de uso de memória pode ser ativado com a opção de linha de comando B<--memlimit=>I<limite>. Geralmente é mais conveniente habilitar o limitador por padrão definindo a variável de ambiente B<XZ_DEFAULTS>, por exemplo, B<XZ_DEFAULTS=--memlimit=150MiB>. É possível definir os limites separadamente para compactação e descompactação usando B<--memlimit-compress=>I<limite> e B<--memlimit-decompress=>I<limite>. Usar essas duas opções fora de B<XZ_DEFAULTS> raramente é útil porque uma única execução de B<xz> não pode fazer compactação e descompactação e B<--memlimit=>I<limite> (ou B<-M> I<limite> ) é mais curto para digitar na linha de comando."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Se o limite de uso de memória especificado for excedido durante a descompactação, B<xz> exibirá um erro e a descompactação do arquivo falhará. Se o limite for excedido durante a compactação, B<xz> tentará reduzir as configurações para que o limite não seja mais excedido (exceto ao usar B<--format=raw> ou B<--no-adjust>). Dessa forma, a operação não falhará, a menos que o limite seja muito pequeno. A escala das configurações é feita em etapas que não correspondem às predefinições do nível de compactação, por exemplo, se o limite for apenas um pouco menor que o valor necessário para B<xz -9>, as configurações serão reduzidas apenas um pouco , não até B<xz -8>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Concatenação e preenchimento com arquivos .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "É possível concatenar arquivos B<.xz> como estão. B<xz> irá descompactar tais arquivos como se fossem um único arquivo B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "É possível inserir preenchimento entre as partes concatenadas ou após a última parte. O preenchimento deve consistir em bytes nulos e o tamanho do preenchimento deve ser um múltiplo de quatro bytes. Isso pode ser útil, por exemplo, se o arquivo B<.xz> for armazenado em uma mídia que mede tamanhos de arquivo em blocos de 512 bytes."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Concatenação e preenchimento não são permitidos com arquivos B<.lzma> ou fluxos brutos."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "OPÇÕES"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Sufixos inteiros e valores especiais"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "Na maioria dos lugares onde um argumento inteiro é esperado, um sufixo opcional é suportado para indicar facilmente números inteiros grandes. Não deve haver espaço entre o número inteiro e o sufixo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Multiplica o inteiro por 1.024 (2^10). B<Ki>, B<k>, B<kB>, B<K> e B<KB> são aceitos como sinônimos de B<KiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Multiplica o número inteiro por 1.048.576 (2^20). B<Mi>, B<m>, B<M> e B<MB> são aceitos como sinônimos de B<MiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Multiplica o número inteiro por 1.073.741.824 (2^30). B<Gi>, B<g>, B<G> e B<GB> são aceitos como sinônimos de B<GiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "O valor especial B<max> pode ser usado para indicar o valor inteiro máximo suportado pela opção."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Modo de operação"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Se várias opções de modo de operação forem dadas, a última entrará em vigor."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Compacta. Este é o modo de operação padrão quando nenhuma opção de modo de operação é especificada e nenhum outro modo de operação está implícito no nome do comando (por exemplo, B<unxz> implica em B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr ""

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Testa a integridade de I<arquivos> compactados. Esta opção é equivalente a B<--decompress --stdout> exceto que os dados descompactados são descartados em vez de serem gravados na saída padrão. Nenhum arquivo é criado ou removido."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Imprime informações sobre I<arquivos> compactados. Nenhuma saída descompactada é produzida e nenhum arquivo é criado ou removido. No modo de lista, o programa não pode ler os dados compactados da entrada padrão ou de outras fontes não pesquisáveis."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "A listagem padrão mostra informações básicas sobre I<arquivos>, um arquivo por linha. Para obter informações mais detalhadas, use também a opção B<--verbose>. Para obter ainda mais informações, use B<--verbose> duas vezes, mas observe que isso pode ser lento, porque obter todas as informações extras requer muitas buscas. A largura da saída detalhada excede 80 caracteres, portanto, canalizar a saída para, por exemplo, B<less\\ -S> pode ser conveniente se o terminal não tiver largura o suficiente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "A saída exata pode variar entre versões B<xz> e localidades diferentes. Para saída legível por máquina, B<--robot --list> deve ser usado."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Modificadores de operação"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Não exclui os arquivos de entrada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Desde B<xz> 5.2.6, esta opção também faz B<xz> compactar ou descompactar mesmo se a entrada for um link simbólico para um arquivo comum, tiver mais de um link físico ou tiver o setuid, setgid ou sticky bit definir. Os bits setuid, setgid e sticky não são copiados para o arquivo de destino. Nas versões anteriores, isso era feito apenas com B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Esta opção tem vários efeitos:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Se o arquivo de destino já existir, o exclui antes de compactar ou descompactar."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Compacta ou descompacta, mesmo que a entrada seja um link simbólico para um arquivo normal, tenha mais de um link físico ou tenha setuid, setgid ou sticky bit definido. Os bits setuid, setgid e sticky não são copiados para o arquivo de destino."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Quando usado com B<--decompress> B<--stdout> e B<xz> não consegue reconhecer o tipo do arquivo de origem, copia o arquivo de origem como está na saída padrão. Isso permite que B<xzcat> B<--force> seja usado como B<cat>(1) para arquivos que não foram compactados com B<xz>. Observe que, no futuro, o B<xz> pode oferecer suporte a novos formatos de arquivo compactado, o que pode fazer com que o B<xz> descompacte mais tipos de arquivos em vez de copiá-los como na saída padrão. B<--format=>I<formato> pode ser usado para restringir B<xz> para descompactar apenas um único formato de arquivo."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Grava os dados compactados ou descompactados na saída padrão em vez de em um arquivo. Isso implica em B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Descompacta apenas o primeiro fluxo de B<.xz> e ignora silenciosamente possíveis dados de entrada restantes após o fluxo. Normalmente, esse restante posterior sem uso faz com que B<xz> exiba um erro."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> nunca descompacta mais de um fluxo de arquivos B<.lzma> ou fluxos brutos, mas esta opção ainda faz B<xz> ignorar os possíveis dados posteriores após o arquivo B<.lzma> ou fluxo bruto."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Esta opção não tem efeito se o modo de operação não for B<--decompress> ou B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Desativa a criação de arquivos esparsos. Por padrão, ao descompactar em um arquivo normal, B<xz> tenta tornar o arquivo esparso se os dados descompactados contiverem longas sequências de zeros binários. Ele também funciona ao gravar na saída padrão, desde que a saída padrão esteja conectada a um arquivo normal e certas condições adicionais sejam atendidas para torná-la segura. A criação de arquivos esparsos pode economizar espaço em disco e acelerar a descompactação, reduzindo a quantidade de E/S do disco."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "Ao compactar, usa I<.suf> como sufixo para o arquivo de destino em vez de B<.xz> ou B<.lzma>. Se não estiver gravando na saída padrão e o arquivo de origem já tiver o sufixo I<.suf>, um aviso será exibido e o arquivo será ignorado."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "Ao descompactar, reconhece arquivos com o sufixo I<.suf> além de arquivos com o sufixo B<.xz>, B<.txz>, B<.lzma>, B<.tlz> ou B<.lz> . Se o arquivo de origem tiver o sufixo I<.suf>, o sufixo será removido para obter o nome do arquivo de destino."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "Ao compactar ou descompactar fluxos brutos (B<--format=raw>), o sufixo sempre deve ser especificado, a menos que seja gravado na saída padrão, porque não há sufixo padrão para fluxos brutos."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<arquivo>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Lê os nomes dos arquivos a serem processados em I<arquivo>; se I<arquivo> for omitido, os nomes dos arquivos serão lidos da entrada padrão. Os nomes de arquivo devem terminar com o caractere de nova linha. Um traço (B<->) é considerado um nome de arquivo regular; não significa entrada padrão. Se os nomes de arquivo forem fornecidos também como argumentos de linha de comando, eles serão processados antes da leitura dos nomes de arquivo de I<arquivo>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<arquivo>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Isso é idêntico a B<--files>[B<=>I<arquivo>], exceto que cada nome de arquivo deve ser finalizado com o caractere nulo."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Opções básicas de formato de arquivo e de compactação"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<formato>, B<--format=>I<formato>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Especifica o I<formato> de arquivo para compactar ou descompactar:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Este é o padrão. Ao compactar, B<auto> é equivalente a B<xz>. Ao descompactar, o formato do arquivo de entrada é detectado automaticamente. Observe que os fluxos brutos (criados com B<--format=raw>) não podem ser detectados automaticamente."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Compacta no formato de arquivo B<.xz> ou aceite apenas arquivos B<.xz> ao descompactar."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Compacta no formato de arquivo legado B<.lzma> ou aceite apenas arquivos B<.lzma> ao descompactar. O nome alternativo B<alone> é fornecido para compatibilidade com versões anteriores do LZMA Utils."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Aceita apenas arquivos B<.lz> ao descompactar. Sem suporte a compactação."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "O formato B<.lz> versão 0 e a versão não estendida 1 são suportados. Os arquivos da versão 0 foram produzidos por B<lzip> 1.3 e anteriores. Esses arquivos não são comuns, mas podem ser encontrados em arquivos compactados, pois alguns pacotes de origem foram lançados nesse formato. As pessoas também podem ter arquivos pessoais antigos neste formato. O suporte de descompactação para o formato versão 0 foi removido em B<lzip> 1.18."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip> 1.4 e posteriores criam arquivos no formato versão 1. A extensão do marcador de descarga de sincronização para o formato versão 1 foi adicionada em B<lzip> 1.6. Esta extensão raramente é usada e não é suportada por B<xz> (diagnosticada como entrada corrompida)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Compacta ou descompacta um fluxo bruto (sem cabeçalhos). Isso é destinado apenas a usuários avançados. Para decodificar fluxos brutos, você precisa usar B<--format=raw> e especificar explicitamente a cadeia de filtros, que normalmente seria armazenada nos cabeçalhos do contêiner."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<verificação>, B<--check=>I<verificação>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Especifica o tipo de verificação de integridade. A verificação é calculada a partir dos dados descompactados e armazenados no arquivo B<.xz>. Esta opção tem efeito somente ao compactar no formato B<.xz>; o formato B<.lzma> não oferece suporte a verificações de integridade. A verificação de integridade (se for o caso) é verificada quando o arquivo B<.xz> é descompactado."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Tipos de I<verificação> suportados:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Não calcula uma verificação de integridade. Isso geralmente é uma má ideia. Pode ser útil quando a integridade dos dados é verificada por outros meios."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Calcula CRC32 usando o polinômio do IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Calcula CRC64 usando o polinômio de ECMA-182. Este é o padrão, pois é um pouco melhor que o CRC32 na detecção de arquivos danificados e a diferença de velocidade é insignificante."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Calcula SHA-256. Isso é um pouco mais lento do que CRC32 e CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "A integridade dos cabeçalhos de B<.xz> é sempre verificada com CRC32. Não é possível alterá-la ou desativá-la."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

# Enquanto verify=verificar, achei melhor alterar para conferir para evitar excesso de "verificar" e "verificação" -- Rafael
#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Não confere a verificação de integridade dos dados compactados ao descompactar. Os valores CRC32 nos cabeçalhos B<.xz> ainda serão conferidos normalmente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Não use esta opção a menos que saiba o que está fazendo.> Possíveis razões para usar esta opção:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Tentativa de recuperar dados de um arquivo .xz corrompido."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Acelerar a descompactação. Isso é importante principalmente com SHA-256 ou com arquivos extremamente bem compactados. É recomendável não usar essa opção para essa finalidade, a menos que a integridade do arquivo seja verificada externamente de alguma outra forma."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Seleciona um nível de predefinição de compactação. O padrão é B<-6>. Se vários níveis de predefinição forem especificados, o último terá efeito. Se uma cadeia de filtro personalizada já foi especificada, especificar um nível de predefinição de compactação limpa a cadeia de filtro personalizada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "As diferenças entre as predefinições são mais significativas do que com B<gzip>(1) e B<bzip2>(1). As configurações de compactação selecionadas determinam os requisitos de memória do descompactador, portanto, usar um nível de predefinição muito alto pode dificultar a descompactação do arquivo em um sistema antigo com pouca RAM. Especificamente, B<não é uma boa ideia usar cegamente -9 para tudo> como costuma acontecer com B<gzip>(1) e B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Estas são predefinições um tanto rápidas. B<-0> às vezes é mais rápida que B<gzip -9> ao mesmo tempo que compacta muito melhor. As mais altas geralmente têm velocidade comparável ao B<bzip2>(1) com taxa de compactação comparável ou melhor, embora os resultados dependam muito do tipo de dados que estão sendo compactados."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Compactação boa a muito boa, mantendo o uso de memória do descompactador razoável mesmo para sistemas antigos. B<-6> é o padrão, que geralmente é uma boa escolha para distribuir arquivos que precisam ser descompactáveis, mesmo em sistemas com apenas 16\\ MiB de RAM. (B<-5e> ou B<-6e> também vale a pena considerar. Veja B<--extreme>.)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Eles são como B<-6>, mas com requisitos de memória de compressor e descompressor mais altos. Eles são úteis apenas ao compactar arquivos maiores que 8\\ MiB, 16\\ MiB e 32\\ MiB, respectivamente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "No mesmo hardware, a velocidade de descompactação é aproximadamente um número constante de bytes de dados compactados por segundo. Em outras palavras, quanto melhor a compactação, mais rápida será a descompactação. Isso também significa que a quantidade de saída não compactada produzida por segundo pode variar muito."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "A tabela a seguir resume os recursos das predefinições:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Predefinição"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "DicTam"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "CompCPU"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "CompMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "DecMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Descrições das colunas:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "DicTam é o tamanho do dicionário LZMA2. É desperdício de memória usar um dicionário maior que o tamanho do arquivo descompactado. É por isso que é bom evitar usar as predefinições B<-7> ... B<-9> quando não há real necessidade deles. Em B<-6> e inferior, a quantidade de memória desperdiçada geralmente é baixa o suficiente para não importar."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "CompCPU é uma representação simplificada das configurações LZMA2 que afetam a velocidade de compactação. O tamanho do dicionário também afeta a velocidade, portanto, embora o CompCPU seja o mesmo para os níveis B<-6> ... B<-9>, níveis mais altos ainda tendem a ser um pouco mais lentos. Para obter uma compactação ainda mais lenta e possivelmente melhor, consulte B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "DecMem contém os requisitos de memória do descompactador. Ou seja, as configurações de compactação determinam os requisitos de memória do descompactador. O uso exato da memória do descompactador é um pouco maior do que o tamanho do dicionário LZMA2, mas os valores na tabela foram arredondados para o próximo MiB completo."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Usa uma variante mais lenta do nível de predefinição de compactação selecionado (B<-0> ... B<-9>) para obter uma taxa de compactação um pouco melhor, mas, com azar, isso também pode piorar. O uso da memória do descompressor não é afetado, mas o uso da memória do compactador aumenta um pouco nos níveis de predefinição B<-0> ... B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Como existem duas predefinições com tamanhos de dicionário 4\\ MiB e 8\\ MiB, as predefinições B<-3e> e B<-5e> usam configurações um pouco mais rápidas (CompCPU inferior) do que B<-4e> e B<-6e>, respectivamente. Dessa forma, não há duas predefinições idênticas."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "Por exemplo, há um total de quatro predefinições que usam o dicionário 8\\ MiB, cuja ordem do mais rápido ao mais lento é B<-5>, B<-6>, B<-5e> e B<-6e>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Esses são apelidos um tanto enganosos para B<-0> e B<-9>, respectivamente. Eles são fornecidos apenas para compatibilidade com versões anteriores do LZMA Utils. Evite usar essas opções."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<tamanho>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "Ao compactar para o formato B<.xz>, divida os dados de entrada em blocos de I<tamanho> bytes. Os blocos são compactados independentemente uns dos outros, o que ajuda no multi-threading e torna possível a descompactação limitada de acesso aleatório. Essa opção normalmente é usada para substituir o tamanho de bloco padrão no modo multi-thread, mas também pode ser usada em thread única."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--block-size=>I<size>"
msgid "B<--block-list=>I<items>"
msgstr "B<--block-size=>I<tamanho>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "No modo multi-thread, os tamanhos dos blocos são armazenados nos cabeçalhos dos blocos. Isso não é feito no modo de thread única, portanto, a saída codificada não será idêntica à do modo multi-thread."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<tempo_limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "Ao compactar, se mais de I<tempo_limite> milissegundos (um número inteiro positivo) se passaram desde a liberação anterior e a leitura de mais entrada seria bloqueada, todos os dados de entrada pendentes serão liberados do codificador e disponibilizados no fluxo de saída. Isso pode ser útil se B<xz> for usado para compactar dados transmitidos por uma rede. Valores I<tempo_limite> pequenos tornam os dados disponíveis na extremidade receptora com um pequeno atraso, mas valores I<tempo_limite> grandes oferecem melhor taxa de compactação."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Esse recurso está desabilitado por padrão. Se esta opção for especificada mais de uma vez, a última terá efeito. O valor especial I<tempo_limite> de B<0> pode ser usado para desabilitar explicitamente esse recurso."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Este recurso não está disponível em sistemas não-POSIX."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Este recurso ainda é experimental.> Atualmente, B<xz> não é adequado para descompactar o fluxo em tempo real devido à forma como B<xz> faz o buffer."

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--no-sparse>"
msgid "B<--no-sync>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Define um limite de uso de memória para compactação. Se esta opção for especificada várias vezes, a última entrará em vigor."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Se as configurações de compactação excederem o I<limite>, B<xz> tentará ajustar as configurações para baixo para que o limite não seja mais excedido e exibirá um aviso de que o ajuste automático foi feito. Os ajustes são feitos nesta ordem: reduzindo o número de encadeamentos, alternando para o modo sigle-thread se até mesmo uma thread no modo multi-thread exceder o I<limite> e, finalmente, reduzindo o tamanho do dicionário LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "Ao compactar com B<--format=raw> ou se B<--no-adjust> tiver sido especificado, apenas o número de threads pode ser reduzido, pois isso pode ser feito sem afetar a saída compactada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Se o I<limite> não puder ser alcançado mesmo com os ajustes descritos acima, um erro será exibido e B<xz> sairá com status de saída 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "O I<limite> pode ser especificado de várias maneiras:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "O I<limite> pode ser um valor absoluto em bytes. Usar um sufixo inteiro como B<MiB> pode ser útil. Exemplo: B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "O I<limite> pode ser especificado como uma porcentagem da memória física total (RAM). Isso pode ser útil especialmente ao definir a variável de ambiente B<XZ_DEFAULTS> em um script de inicialização de shell que é compartilhado entre diferentes computadores. Dessa forma o limite é automaticamente maior em sistemas com mais memória. Exemplo: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "O I<limite> pode ser redefinido para seu valor padrão, definindo-o como B<0>. Atualmente, isso equivale a definir I<limite> como B<max> (sem limite de uso de memória)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "Para B<xz> de 32 bits, há um caso especial: se o I<limite> estiver acima de B<4020\\ MiB>, o I<limite> é definido como B<4020\\ MiB>. No MIPS32 B<2000\\ MiB> é usado em seu lugar. (Os valores B<0> e B<max> não são afetados por isso. Um recurso semelhante não existe para descompactação.) Isso pode ser útil quando um executável de 32 bits tem acesso a espaço de endereço de 4\\ GiB (2 GiB no MIPS32) enquanto espero não causar danos em outras situações."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Consulte também a seção B<Uso de memória>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Define um limite de uso de memória para descompactação. Isso também afeta o modo B<--list>. Se a operação não for possível sem exceder o I<limite>, B<xz> exibirá um erro e a descompactação do arquivo falhará. Consulte B<--memlimit-compress=>I<limite> para possíveis maneiras de especificar o I<limite>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "Define um limite de uso de memória para descompactação multi-thread. Isso pode afetar apenas o número de threads; isso nunca fará com que B<xz> se recuse a descompactar um arquivo. Se I<limite> for muito baixo para permitir qualquer multi-thread, o I<limite> será ignorado e B<xz> continuará no modo de thread única. Observe que se B<--memlimit-decompress> também for usado, ele sempre se aplicará aos modos de thread única e multi-thread e, portanto, o I<limite> efetivo para multi-threading nunca será maior que o limite definido com B<--memlimit-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "Em contraste com as outras opções de limite de uso de memória, B<--memlimit-mt-decompress=>I<limite> tem um padrão I<limite> específico do sistema. B<xz --info-memory> pode ser usado para ver o valor atual."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Esta opção e seu valor padrão existem porque, sem qualquer limite, o descompactador usando threads pode acabar alocando uma quantidade insana de memória com alguns arquivos de entrada. Se o I<limite> padrão for muito baixo em seu sistema, sinta-se à vontade para aumentar o I<limite>, mas nunca defina-o para um valor maior que a quantidade de RAM utilizável, pois com os arquivos de entrada apropriados B<xz> tentará usar essa quantidade de memória mesmo com um baixo número de threads. Ficar sem memória ou trocar não melhorará o desempenho da descompactação."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Consulte B<--memlimit-compress=>I<limite> para possíveis maneiras de especificar o I<limite>. Definir I<limite> como B<0> redefine I<limite> para o valor padrão específico do sistema."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<limite>, B<--memlimit=>I<limite>, B<--memory=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Isso é equivalente a especificar B<--memlimit-compress=>I<limite> B<--memlimit-decompress=>I<limite> B<--memlimit-mt-decompress=>I<limite>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "Exibe um erro e saia se o limite de uso de memória não puder ser atendido sem ajustar as configurações que afetam a saída compactada. Ou seja, isso evita que B<xz> alterne o codificador do modo multi-thread para o modo encadeado único e reduza o tamanho do dicionário LZMA2. Mesmo quando esta opção é usada, o número de threads pode ser reduzido para atender ao limite de uso de memória, pois isso não afetará a saída compactada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "O ajuste automático é sempre desativado ao criar fluxos brutos (B<--format=raw>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<threads>, B<--threads=>I<threads>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "Especifica o número de threads de trabalho a serem usados. Definir I<threads> para um valor especial B<0> faz com que B<xz> use tantos threads quanto o(s) processador(es) no suporte do sistema. O número real de encadeamentos pode ser menor que I<threads> se o arquivo de entrada não for grande o suficiente para subdividir em threads com as configurações fornecidas ou se o uso de mais threads exceder o limite de uso de memória."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "Os compactadores usando thread única e várias threads produzem saídas diferentes. O compactador de thread única fornecerá o menor tamanho de arquivo, mas apenas a saída do compactador de várias threads pode ser descompactada usando várias threads. Definir I<threads> como B<1> usará o modo de thread única. Definir I<threads> para qualquer outro valor, incluindo B<0>, usará o compressor de várias threads, mesmo que o sistema tenha suporte a apenas uma thread de hardware. (B<xz> 5.2.x usou o modo de thread única nesta situação.)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Para usar o modo de várias threads com apenas uma thread, defina I<threads> como B<+1>. O prefixo B<+> não tem efeito com valores diferentes de B<1>. Um limite de uso de memória ainda pode fazer B<xz> alternar para o modo de thread única, a menos que B<--no-adjust> seja usado. O suporte para o prefixo B<+> foi adicionado no B<xz> 5.4.0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr "Se um número automático de threads foi solicitado e nenhum limite de uso de memória foi especificado, um limite flexível padrão específico do sistema será usado para possivelmente limitar o número de threads. É um limite flexível no sentido de que é ignorado se o número de threads se tornar um, portanto, um limite flexível nunca impedirá B<xz> de compactar ou descompactar. Este limite flexível padrão não fará com que B<xz> alterne do modo de várias threads para o modo de thread única. Os limites ativos podem ser vistos com B<xz --info-memory>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "Atualmente, o único método de threading é dividir a entrada em blocos e comprimi-los independentemente um do outro. O tamanho padrão do bloco depende do nível de compactação e pode ser substituído com a opção B<--block-size=>I<tamanho>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "A descompactação em threads funciona apenas em arquivos que contêm vários blocos com informações de tamanho nos cabeçalhos dos blocos. Todos os arquivos grandes o suficiente compactados no modo de várias thread atendem a essa condição, mas os arquivos compactados no modo de thread única não atendem, mesmo se B<--block-size=>I<tamanho> tiver sido usado."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Cadeias de filtro de compressor personalizadas"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Uma cadeia de filtro personalizada permite especificar as configurações de compactação em detalhes, em vez de confiar nas configurações associadas às predefinições. Quando uma cadeia de filtro personalizada é especificada, as opções predefinidas (B<-0> \\&...\\& B<-9> e B<--extreme>) anteriores na linha de comando são esquecidas. Se uma opção predefinida for especificada após uma ou mais opções de cadeia de filtros personalizados, a nova predefinição entrará em vigor e as opções de cadeia de filtros personalizados especificadas anteriormente serão esquecidas."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Uma cadeia de filtro é comparável à tubulação na linha de comando. Ao compactar, a entrada descompactada vai para o primeiro filtro, cuja saída vai para o próximo filtro (se houver). A saída do último filtro é gravada no arquivo compactado. O número máximo de filtros na cadeia é quatro, mas normalmente uma cadeia de filtros tem apenas um ou dois filtros."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Muitos filtros têm limitações sobre onde podem estar na cadeia de filtros: alguns filtros podem funcionar apenas como o último filtro na cadeia, alguns apenas como filtro não-último e alguns funcionam em qualquer posição na cadeia. Dependendo do filtro, essa limitação é inerente ao projeto do filtro ou existe para evitar problemas de segurança."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Para ver toda a cadeia de filtros e I<opções>, use B<xz -vv> (isto é, use B<--verbose> duas vezes). Isso também funciona para visualizar as opções da cadeia de filtros usadas pelas predefinições."

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--filters=>I<filters>"
msgstr "B<--files>[B<=>I<arquivo>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<-h>, B<--help>"
msgid "B<--filters-help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<opções>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Adiciona o filtro LZMA1 ou LZMA2 à cadeia de filtros. Esses filtros podem ser usados apenas como o último filtro na cadeia."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 é um filtro legado, que é suportado quase exclusivamente devido ao formato de arquivo legado B<.lzma>, que suporta apenas LZMA1. LZMA2 é uma versão atualizada do LZMA1 para corrigir alguns problemas práticos do LZMA1. O formato B<.xz> usa LZMA2 e não suporta LZMA1. A velocidade de compactação e as proporções de LZMA1 e LZMA2 são praticamente as mesmas."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 e LZMA2 compartilham o mesmo conjunto de I<opções>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<predefinição>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "Redefine todas as I<opções> de LZMA1 ou LZMA2 para I<predefinição>. I<Predefinição> consiste em um número inteiro, que pode ser seguido por modificadores de predefinição de uma única letra. O inteiro pode ser de B<0> a B<9>, correspondendo às opções de linha de comando B<-0> \\&...\\& B<-9>. O único modificador suportado atualmente é B<e>, que corresponde a B<--extreme>. Se nenhum B<preset> for especificado, os valores padrão das I<opções> LZMA1 ou LZMA2 serão obtidos da predefinição B<6>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<tamanho>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "O I<tamanho> do dicionário (buffer de histórico) indica quantos bytes dos dados não compactados processados recentemente são mantidos na memória. O algoritmo tenta encontrar sequências de bytes repetidos (correspondências) nos dados não compactados e substituí-los por referências aos dados atualmente no dicionário. Quanto maior o dicionário, maior a chance de encontrar uma correspondência. Portanto, aumentar o dicionário I<tamanho> geralmente melhora a taxa de compactação, mas um dicionário maior que o arquivo não compactado é um desperdício de memória."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "Um I<tamanho> de dicionário típico é de 64\\ KiB a 64\\ MiB. O mínimo é 4\\ KiB. O máximo para compactação é atualmente 1,5\\ GiB (1536\\ MiB). O descompactador já oferece suporte a dicionários de até um byte a menos de 4\\ GiB, que é o máximo para os formatos de fluxo LZMA1 e LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "O I<tamanho> de dicionário e o localizador de correspondência (I<mf>) juntos determinam o uso de memória do codificador LZMA1 ou LZMA2. O mesmo (ou maior) I<tamanho> de dicionário é necessário para descompactar que foi usado durante a compactação, portanto, o uso de memória do decodificador é determinado pelo tamanho do dicionário usado durante a compactação. Os cabeçalhos B<.xz> armazenam o I<tamanho> de dicionário como 2^I<n> ou 2^I<n> + 2^(I<n>-1), então esses I<tamanhos> são um tanto preferidos para compactação. Outros I<tamanhos> serão arredondados quando armazenados nos cabeçalhos B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Especifica o número de bits de contexto literais. O mínimo é 0 e o máximo é 4; o padrão é 3. Além disso, a soma de I<lc> e I<lp> não deve exceder 4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Todos os bytes que não podem ser codificados como correspondências são codificados como literais. Ou seja, literais são simplesmente bytes de 8 bits que são codificados um de cada vez."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "A codificação literal assume que os bits I<lc> mais altos do byte não compactado anterior se correlacionam com o próximo byte. Por exemplo, em um texto típico em inglês, uma letra maiúscula geralmente é seguida por uma letra minúscula, e uma letra minúscula geralmente é seguida por outra letra minúscula. No conjunto de caracteres US-ASCII, os três bits mais altos são 010 para letras maiúsculas e 011 para letras minúsculas. Quando I<lc> é pelo menos 3, a codificação literal pode aproveitar essa propriedade nos dados não compactados."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "O valor padrão (3) geralmente é bom. Se você deseja compactação máxima, experimente B<lc=4>. Às vezes ajuda um pouco, às vezes piora a compactação. Se piorar, experimente B<lc=2> também."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Especifica o número de bits de posição literal. O mínimo é 0 e o máximo é 4; o padrão é 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> afeta que tipo de alinhamento nos dados não compactados é assumido ao codificar literais. Consulte I<pb> abaixo para obter mais informações sobre alinhamento."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Especifica o número de bits de posição. O mínimo é 0 e o máximo é 4; o padrão é 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> afeta que tipo de alinhamento nos dados não compactados é assumido em geral. O padrão significa alinhamento de quatro bytes (2^I<pb>=2^2=4), que geralmente é uma boa escolha quando não há melhor estimativa."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Quando o alinhamento é conhecido, definir I<pb> adequadamente pode reduzir um pouco o tamanho do arquivo. Por exemplo, com arquivos de texto com alinhamento de um byte (US-ASCII, ISO-8859-*, UTF-8), a configuração B<pb=0> pode melhorar um pouco a compactação. Para texto UTF-16, B<pb=1> é uma boa escolha. Se o alinhamento for um número ímpar como 3 bytes, B<pb=0> pode ser a melhor escolha."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Embora o alinhamento assumido possa ser ajustado com I<pb> e I<lp>, LZMA1 e LZMA2 ainda favorecem ligeiramente o alinhamento de 16 bytes. Pode valer a pena levar em consideração ao projetar formatos de arquivo que provavelmente serão compactados com LZMA1 ou LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<mf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "O localizador de correspondência tem um efeito importante na velocidade do codificador, uso de memória e taxa de compactação. Normalmente, os localizadores de correspondência de Hash Chain são mais rápidos do que os localizadores de correspondência de árvore binária. O padrão depende do I<predefinição>: 0 usa B<hc3>, 1\\(en3 usa B<hc4> e o resto usa B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Os seguintes localizadores de correspondência são suportados. As fórmulas de uso de memória abaixo são aproximações aproximadas, que estão mais próximas da realidade quando I<dict> é uma potência de dois."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Cadeia de hashs com hashing de 2 e 3 bytes"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Valor mínimo para I<nice>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Uso de memória:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Cadeia de hashs com hashing de 2, 3 e 4 bytes"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Valor mínimo para I<nice>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Árvore binária com hashing de 2 bytes"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Valor mínimo para I<nice>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Uso de memória: I<dict> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Árvore binária com hashing de 2 e 3 bytes"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Árvore binária com hashing de 2, 3 e 4 bytes"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<modo>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "O I<modo> de compactação especifica o método para analisar os dados produzidos pelo localizador de correspondência. Os I<modos> suportados são B<fast> e B<normal>. O padrão é B<fast> para I<predefinições> 0\\(en3 e B<normal> para I<predefinições> 4\\(en9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Normalmente, B<fast> é usado com localizadores de correspondência cadeia de hashs e B<normal> com localizadores de correspondência de árvore binária. Isso também é o que os I<predefinições> fazem."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<nice>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Especifica o que é considerado um bom comprimento para uma correspondência. Uma vez que uma correspondência de pelo menos I<nice> bytes é encontrada, o algoritmo para de procurar correspondências possivelmente melhores."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "I<Nice> pode ser 2\\(en273 bytes. Valores mais altos tendem a fornecer melhor taxa de compactação em detrimento da velocidade. O padrão depende do I<predefinição>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<profundidade>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Especifica a profundidade máxima de pesquisa no localizador de correspondências. O padrão é o valor especial de 0, que faz com que o compressor determine um I<profundidade> razoável de I<mf> e I<nice>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "Uma I<profundidade> razoável para cadeias de hash é 4\\(en100 e 16\\(en1000 para árvores binárias. Usar valores muito altos para I<profundidade> pode tornar o codificador extremamente lento com alguns arquivos. Evite definir I<profundidade> acima de 1000 a menos que você esteja preparado para interromper a compactação caso ela esteja demorando muito."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "Ao decodificar fluxos brutos (B<--format=raw>), o LZMA2 precisa apenas do dicionário I<tamanho>. LZMA1 também precisa de I<lc>, I<lp> e I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<opções>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<opções>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Adiciona um filtro de ramificação/chamada/salto (BCJ) à cadeia de filtros. Esses filtros podem ser usados apenas como um filtro não último na cadeia de filtros."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "Um filtro BCJ converte endereços relativos no código de máquina em suas contrapartes absolutas. Isso não altera o tamanho dos dados, mas aumenta a redundância, o que pode ajudar o LZMA2 a produzir um arquivo B<.xz> 0\\(en15\\ % menor. Os filtros BCJ são sempre reversíveis, portanto, usar um filtro BCJ para o tipo errado de dados não causa nenhuma perda de dados, embora possa piorar um pouco a taxa de compactação.Os filtros BCJ são muito rápidos e usam uma quantidade insignificante de memória."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Esses filtros BCJ têm problemas conhecidos relacionados à taxa de compactação:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "Alguns tipos de arquivos contendo código executável (por exemplo, arquivos de objeto, bibliotecas estáticas e módulos do kernel do Linux) têm os endereços nas instruções preenchidos com valores de preenchimento. Esses filtros BCJ ainda vão fazer a conversão de endereço, o que vai piorar a compactação desses arquivos."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Se um filtro BCJ for aplicado em um arquivo, é possível que isso torne a taxa de compactação pior do que não usar um filtro BCJ. Por exemplo, se houver executáveis semelhantes ou mesmo idênticos, a filtragem provavelmente tornará os arquivos menos semelhantes e, portanto, a compactação será pior. O conteúdo de arquivos não executáveis no mesmo arquivo também pode ser importante. Na prática tem que tentar com e sem filtro BCJ para ver qual é melhor em cada situação."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Conjuntos de instruções diferentes têm alinhamento diferente: o arquivo executável deve ser alinhado a um múltiplo desse valor nos dados de entrada para fazer o filtro funcionar."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Filtro"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Alinhamento"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Observações"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "x86 32 bits ou 64 bits"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr ""
"Alinhamento de 4096 bytes\n"
";;é melhor"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Somente big endian"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Todos os filtros BCJ suportam as mesmas I<opções>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<deslocamento>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Especifica o I<deslocamento> inicial que é usado na conversão entre endereços relativos e absolutos. O I<deslocamento> deve ser um múltiplo do alinhamento do filtro (ver tabela acima). O padrão é zero. Na prática, o padrão é bom; especificar um I<deslocamento> personalizado quase nunca é útil."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<opções>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Adiciona o filtro Delta à cadeia de filtros. O filtro Delta só pode ser usado como filtro não-último na cadeia de filtros."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "Atualmente, apenas o cálculo simples de delta byte a byte é suportado. Pode ser útil ao compactar, por exemplo, imagens bitmap não compactadas ou áudio PCM não compactado. No entanto, algoritmos de propósito especial podem fornecer resultados significativamente melhores do que Delta + LZMA2. Isso é verdade especialmente com áudio, que compacta mais rápido e melhor, por exemplo, com B<flac>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "I<Opções> suportadas:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<distância>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "Especifica a I<distância> do cálculo delta em bytes. I<distância> deve ser 1\\(en256. O padrão é 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "Por exemplo, com B<dist=2> e entrada de oito bytes A1 B1 A2 B3 A3 B5 A4 B7, a saída será A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Outras opções"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Suprime avisos e avisos. Especifique isso duas vezes para suprimir erros também. Esta opção não tem efeito no status de saída. Ou seja, mesmo que um aviso tenha sido suprimido, o status de saída para indicar um aviso ainda é usado."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Ser detalhado. Se o erro padrão estiver conectado a um terminal, B<xz> exibirá um indicador de progresso. Especifique B<--verbose> duas vezes dará uma saída ainda mais detalhada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "O indicador de progresso mostra as seguintes informações:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "A porcentagem de conclusão é mostrada se o tamanho do arquivo de entrada for conhecido. Ou seja, a porcentagem não pode ser mostrada em encadeamentos (pipe)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Quantidade de dados compactados produzidos (compactando) ou consumidos (descompactando)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Quantidade de dados não compactados consumidos (compactação) ou produzidos (descompactação)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Taxa de compactação, que é calculada dividindo a quantidade de dados compactados processados até o momento pela quantidade de dados não compactados processados até o momento."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Velocidade de compactação ou descompactação. Isso é medido como a quantidade de dados não compactados consumidos (compactação) ou produzidos (descompactação) por segundo. É mostrado após alguns segundos desde que B<xz> começou a processar o arquivo."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Tempo decorrido no formato M:SS ou H:MM:SS."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "O tempo restante estimado é mostrado apenas quando o tamanho do arquivo de entrada é conhecido e alguns segundos já se passaram desde que B<xz> começou a processar o arquivo. A hora é mostrada em um formato menos preciso que nunca tem dois pontos, por exemplo, 2 min 30 s."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Quando o erro padrão não é um terminal, B<--verbose> fará com que B<xz> imprima o nome do arquivo, tamanho compactado, tamanho não compactado, taxa de compactação e possivelmente também a velocidade e o tempo decorrido em uma única linha para o erro padrão após a compactação ou descompactando o arquivo. A velocidade e o tempo decorrido são incluídos apenas quando a operação leva pelo menos alguns segundos. Se a operação não foi concluída, por exemplo, devido à interrupção do usuário, também é impressa a porcentagem de conclusão se o tamanho do arquivo de entrada for conhecido."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Não define o status de saída como 2, mesmo que uma condição digna de um aviso tenha sido detectada. Esta opção não afeta o nível de detalhamento, portanto, tanto B<--quiet> quanto B<--no-warn> devem ser usados para não exibir avisos e não alterar o status de saída."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Imprime mensagens em um formato analisável por máquina. Isso visa facilitar a criação de frontends que desejam usar B<xz> em vez de liblzma, o que pode ser o caso de vários scripts. A saída com esta opção habilitada deve ser estável em versões B<xz>. Consulte a seção B<MODO ROBÔ> para obter detalhes."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "Exibe, em formato legível por humanos, quanta memória física (RAM) e quantos threads de processador B<xz> acredita que o sistema possui e os limites de uso de memória para compactação e descompactação e saia com êxito."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Exibe uma mensagem de ajuda descrevendo as opções mais usadas e sai com sucesso."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Exibe uma mensagem de ajuda descrevendo todos os recursos de B<xz> e sai com sucesso"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Exibe o número da versão de B<xz> e liblzma em formato legível por humanos. Para obter uma saída analisável por máquina, especifique B<--robot> antes de B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "MODO ROBÔ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Modo lista"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> usa saída separada por tabulações. A primeira coluna de cada linha possui uma string que indica o tipo de informação encontrada naquela linha:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Esta é sempre a primeira linha ao começar a listar um arquivo. A segunda coluna na linha é o nome do arquivo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Esta linha contém informações gerais sobre o arquivo B<.xz>. Esta linha é sempre impressa após a linha B<name>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Este tipo de linha é usado somente quando B<--verbose> foi especificado. Existem tantas linhas de B<stream> quanto fluxos no arquivo B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Este tipo de linha é usado somente quando B<--verbose> foi especificado. Existem tantas linhas B<block> quanto blocos no arquivo B<.xz>. As linhas B<block> são mostradas após todas as linhas B<stream>; diferentes tipos de linha não são intercalados."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Este tipo de linha é usado apenas quando B<--verbose> foi especificado duas vezes. Esta linha é impressa após todas as linhas de B<block>. Assim como a linha B<arquivo>, a linha B<summary> contém informações gerais sobre o arquivo B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Esta linha é sempre a última linha da saída da lista. Ele mostra as contagens totais e tamanhos."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "As colunas das linhas B<file>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Número de fluxos no arquivo"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Número total de blocos no(s) fluxo(s)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Tamanho compactado do arquivo"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Uncompressed size of the file"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Taxa de compactação, por exemplo, B<0.123>. Se a proporção for superior a 9.999, serão exibidos três traços (B<--->) em vez da proporção."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Lista separada por vírgulas de nomes de verificação de integridade. As seguintes strings são usadas para os tipos de verificação conhecidos: B<None>, B<CRC32>, B<CRC64> e B<SHA-256>. Para tipos de verificações desconhecidos, B<Unknown->I<N> é usado, onde I<N> é o ID do cheque como um número decimal (um ou dois dígitos)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Tamanho total do preenchimento de fluxo no arquivo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "As colunas das linhas B<stream>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Número do fluxo (o primeiro fluxo é 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Número de blocos no fluxo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Deslocamento inicial compactado"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Deslocamento inicial descompactado"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Tamanho compactado (não inclui preenchimento de fluxo)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Tamanho descompactado"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Taxa de compactação"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Nome da verificação de integridade"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Tamanho do preenchimento do fluxo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "As colunas das linhas B<block>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Número do fluxo que contém este bloco"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Número do bloco relativo ao início do fluxo (o primeiro bloco é 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Número do bloco relativo ao início do arquivo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Deslocamento inicial compactado em relação ao início do arquivo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Deslocamento inicial descompactado em relação ao início do arquivo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Tamanho total compactado do bloco (inclui cabeçalhos)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Se B<--verbose> for especificado duas vezes, colunas adicionais serão incluídas nas linhas B<block>. Eles não são exibidos com um único B<--verbose>, porque obter essas informações requer muitas buscas e, portanto, pode ser lento:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Valor da verificação de integridade em hexadecimal"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Tamanho do cabeçalho do bloco"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Sinalizadores de bloco: B<c> indica que o tamanho compactado está presente e B<u> indica que o tamanho não compactado está presente. Se o sinalizador não estiver definido, um traço (B<->) será exibido para manter o comprimento da string fixo. Novos sinalizadores podem ser adicionados ao final da string no futuro."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Tamanho dos dados reais compactados no bloco (isso exclui o cabeçalho do bloco, o preenchimento do bloco e os campos de verificação)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Quantidade de memória (em bytes) necessária para descompactar este bloco com esta versão B<xz>"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Cadeia de filtro. Observe que a maioria das opções usadas no momento da compactação não pode ser conhecida, pois apenas as opções necessárias para a descompactação são armazenadas nos cabeçalhos B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "As colunas das linhas B<summary>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Quantidade de memória (em bytes) necessária para descompactar este arquivo com esta versão do B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> ou B<no> indicando se todos os cabeçalhos de bloco têm tamanho compactado e tamanho não compactado armazenados neles"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Desde> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Versão mínima do B<xz> necessária para descompactar o arquivo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "As colunas da linha B<totals>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Número de fluxos"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Número de blocos"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Tamanho compactado"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Taxa de compactação média"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Lista separada por vírgulas de nomes de verificação de integridade que estavam presentes nos arquivos"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Tamanho do preenchimento do fluxo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Número de arquivos. Isso está aqui para manter a ordem das colunas anteriores a mesma das linhas B<file>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Se B<--verbose> for especificado duas vezes, colunas adicionais serão incluídas na linha B<totals>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Quantidade máxima de memória (em bytes) necessária para descompactar os arquivos com esta versão do B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Versões futuras podem adicionar novos tipos de linha e novas colunas podem ser adicionadas aos tipos de linha existentes, mas as colunas existentes não serão alteradas."

#. type: SS
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "Filter"
msgid "Filters help"
msgstr "Filtro"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<file>"
msgid "I<filter>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "Name of the integrity check"
msgid "Name of the filter"
msgstr "Nome da verificação de integridade"

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "Supported I<options>:"
msgid "I<option>"
msgstr "I<Opções> suportadas:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Informações de limite de memória"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr ""

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Quantidade total de memória física (RAM) em bytes."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limite de uso de memória para compactação em bytes (B<--memlimit-compress>). Um valor especial de B<0> indica a configuração padrão que para o modo de thread única é o mesmo que sem limite."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limite de uso de memória para descompactação em bytes (B<--memlimit-decompress>). Um valor especial de B<0> indica a configuração padrão que para o modo de thread única é o mesmo que sem limite."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "Desde B<xz> 5.3.4alpha: Uso de memória para descompactação com várias thread em bytes (B<--memlimit-mt-decompress>). Isso nunca é zero porque um valor padrão específico do sistema mostrado na coluna 5 é usado se nenhum limite for especificado explicitamente. Isso também nunca é maior que o valor na coluna 3, mesmo que um valor maior tenha sido especificado com B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "Desde B<xz> 5.3.4alpha: Um limite de uso de memória padrão específico do sistema que é usado para limitar o número de threads ao compactar com um número automático de threads (B<--threads=0>) e nenhum limite de uso de memória foi especificado (B<--memlimit-compress>). Isso também é usado como o valor padrão para B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "Desde B<xz> 5.3.4alpha: Número de threads de processador disponíveis."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "No futuro, a saída de B<xz --robot --info-memory> pode ter mais colunas, mas nunca mais do que uma única linha."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Versão"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr ""

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Versão principal."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Versão menor. Números pares são estáveis. Os números ímpares são versões alfa ou beta."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Nível de patch para versões estáveis ou apenas um contador para versões de desenvolvimento."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Estabilidade. 0 é alfa, 1 é beta e 2 é estável. I<S> deve ser sempre 2 quando I<YYY> for par."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> são iguais em ambas as linhas se B<xz> e liblzma forem da mesma versão do XZ Utils."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Exemplos: 4.999.9beta é B<49990091> e 5.0.0 é B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "STATUS DE SAÍDA"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Está tudo bem."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "Ocorreu um erro."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Algo digno de um aviso ocorreu, mas ocorreu nenhum erro real."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Observações (não avisos ou erros) impressas no erro padrão não afetam o status de saída."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "AMBIENTE"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> analisa listas de opções separadas por espaços das variáveis de ambiente B<XZ_DEFAULTS> e B<XZ_OPT>, nesta ordem, antes de analisar as opções da linha de comando. Observe que apenas as opções são analisadas a partir das variáveis de ambiente; todas as não opções são silenciosamente ignoradas. A análise é feita com B<getopt_long>(3) que também é usado para os argumentos da linha de comando."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default.  Excluding shell initialization scripts and similar special cases, scripts must never set or unset B<XZ_DEFAULTS>."
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Opções padrão específicas do usuário ou de todo o sistema. Normalmente, isso é definido em um script de inicialização do shell para habilitar o limitador de uso de memória do B<xz> por padrão. Excluindo scripts de inicialização de shell e casos especiais semelhantes, os scripts nunca devem definir ou remover a definição de B<XZ_DEFAULTS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Isso é para passar opções para B<xz> quando não é possível definir as opções diretamente na linha de comando B<xz>. Este é o caso quando B<xz> é executado por um script ou ferramenta, por exemplo, GNU B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<XZ_OPT=-2v tar caf foo.tar.xz foo>\n"
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "CW<XZ_OPT=-2v tar caf foo.tar.xz foo>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Os scripts podem usar B<XZ_OPT>, por exemplo, para definir opções de compactação padrão específicas do script. Ainda é recomendável permitir que os usuários substituam B<XZ_OPT> se isso for razoável. Por exemplo, em scripts B<sh>(1) pode-se usar algo assim:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<XZ_OPT=${XZ_OPT-\"-7e\"}\n"
#| "export XZ_OPT>\n"
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"CW<XZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT>\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "COMPATIBILIDADE COM LZMA UTILS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "A sintaxe da linha de comando do B<xz> é praticamente um superconjunto de B<lzma>, B<unlzma> e B<lzcat> conforme encontrado no LZMA Utils 4.32.x. Na maioria dos casos, é possível substituir LZMA Utils por XZ Utils sem interromper os scripts existentes. Existem algumas incompatibilidades, porém, que às vezes podem causar problemas."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Níveis de predefinição de compactação"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "A numeração das predefinições de nível de compactação não é idêntica em B<xz> e LZMA Utils. A diferença mais importante é como os tamanhos dos dicionários são mapeados para diferentes predefinições. O tamanho do dicionário é aproximadamente igual ao uso de memória do descompactador."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Nível"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "N/D"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "As diferenças de tamanho do dicionário também afetam o uso da memória do compressor, mas existem algumas outras diferenças entre LZMA Utils e XZ Utils, que tornam a diferença ainda maior:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "O nível de predefinição padrão no LZMA Utils é B<-7> enquanto no XZ Utils é B<-6>, então ambos usam um dicionário de 8 MiB por padrão."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Arquivos .lzma em um fluxo versus sem ser em um fluxo"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "O tamanho descompactado do arquivo pode ser armazenado no cabeçalho de B<.lzma>. O LZMA Utils faz isso ao compactar arquivos comuns. A alternativa é marcar que o tamanho não compactado é desconhecido e usar o marcador de fim de carga útil para indicar onde o descompactador deve parar. O LZMA Utils usa este método quando o tamanho não compactado não é conhecido, como é o caso, por exemplo, de encadeamentos (pipes)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> oferece suporte à descompactação de arquivos B<.lzma> com ou sem marcador de fim de carga útil, mas todos os arquivos B<.lzma> criados por B<xz> usarão marcador de fim de carga útil e terão o tamanho descompactado marcado como desconhecido no cabeçalho de B<.lzma>. Isso pode ser um problema em algumas situações incomuns. Por exemplo, um descompactador de B<.lzma> em um dispositivo embarcado pode funcionar apenas com arquivos que tenham tamanho descompactado conhecido. Se você encontrar esse problema, precisará usar o LZMA Utils ou o LZMA SDK para criar arquivos B<.lzma> com tamanho descompactado conhecido."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Arquivos .lzma não suportados"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "O formato B<.lzma> permite valores I<lc> até 8 e valores I<lp> até 4. LZMA Utils pode descompactar arquivos com qualquer I<lc> e I<lp>, mas sempre cria arquivos com B<lc=3> e B<lp=0>. Criar arquivos com outros I<lc> e I<lp> é possível com B<xz> e com LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "A implementação do filtro LZMA1 em liblzma requer que a soma de I<lc> e I<lp> não exceda 4. Assim, arquivos B<.lzma>, que excedam esta limitação, não podem ser descompactados com B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "LZMA Utils cria apenas arquivos B<.lzma> que possuem um tamanho de dicionário de 2^I<n> (uma potência de 2), mas aceita arquivos com qualquer tamanho de dicionário. liblzma aceita apenas arquivos B<.lzma> que tenham um tamanho de dicionário de 2^I<n> ou 2^I<n> + 2^(I<n>-1). Isso é para diminuir os falsos positivos ao detectar arquivos B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Essas limitações não devem ser um problema na prática, já que praticamente todos os arquivos B<.lzma> foram compactados com configurações que o liblzma aceitará."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Lixo à direita"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Ao descompactar, o LZMA Utils silenciosamente ignora tudo após o primeiro fluxo B<.lzma>. Na maioria das situações, isso é um bug. Isso também significa que o LZMA Utils não oferece suporte a descompactação de arquivos B<.lzma> concatenados."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Se houver dados restantes após o primeiro fluxo de B<.lzma>, B<xz> considera o arquivo corrompido, a menos que B<--single-stream> tenha sido usado. Isso pode quebrar scripts obscuros que presumiram que o lixo à direita é ignorado."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "NOTAS"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "A saída compactada pode variar"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "A saída compactada exata produzida a partir do mesmo arquivo de entrada não compactado pode variar entre as versões do XZ Utils, mesmo se as opções de compactação forem idênticas. Isso ocorre porque o codificador pode ser aprimorado (compactação mais rápida ou melhor) sem afetar o formato do arquivo. A saída pode variar mesmo entre diferentes compilações da mesma versão do XZ Utils, se diferentes opções de compilação forem usadas."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "A informação acima significa que, uma vez que B<--rsyncable> tenha sido implementado, os arquivos resultantes não serão necessariamente \"rsyncáveis\", a menos que os arquivos antigos e novos tenham sido compactados com a mesma versão xz. Esse problema pode ser corrigido se uma parte da implementação do codificador for congelada para manter a saída de rsyncable estável nas versões do xz."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Descompactadores .xz embarcados"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "As implementações do descompactador B<.xz> embarcados, como o XZ Embedded, não oferecem necessariamente suporte a arquivos criados com tipos de I<verificações> de integridade diferentes de B<none> e B<crc32>. Como o padrão é B<--check=crc64>, você deve usar B<--check=none> ou B<--check=crc32> ao criar arquivos para sistemas embarcados."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "Fora dos sistemas embarcados, todos os descompactadores de formato B<.xz> oferecem suporte a todos os tipos de I<verificação> ou, pelo menos, são capazes de descompactar o arquivo sem verificar a verificação de integridade se a I<verificação> específica não for suportada."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded oferece suporte a filtros BCJ, mas apenas com o deslocamento inicial padrão."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "EXEMPLOS"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Básico"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Compactar o arquivo I<foo> em I<foo.xz> usando o nível de compactação padrão (B<-6>) e remover I<foo> se a compactação for bem-sucedida:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz foo>\n"
msgid "\\f(CRxz foo\\fR\n"
msgstr "CW<xz foo>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Descompactar I<bar.xz> em I<bar> e não remover I<bar.xz> mesmo se a descompactação for bem-sucedida:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -dk bar.xz>\n"
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "CW<xz -dk bar.xz>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "Criar I<baz.tar.xz> com a predefinição B<-4e> (B<-4 --extreme>), que é mais lenta que o padrão B<-6>, mas precisa de menos memória para compactação e descompactação (48 \\ MiB e 5\\ MiB, respectivamente):"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<tar cf - baz | xz -4e E<gt> baz.tar.xz>\n"
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "CW<tar cf - baz | xz -4e E<gt> baz.tar.xz>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Uma mistura de arquivos compactados e descompactados pode ser descompactada para a saída padrão com um único comando:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt>\n"
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "CW<xz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt>\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Compactação paralela de muitos arquivos"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "No GNU e *BSD, B<find>(1) e B<xargs>(1) podem ser usados para paralelizar a compactação de muitos arquivos:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<find . -type f \\e! -name '*.xz' -print0 \\e\n"
#| "    | xargs -0r -P4 -n16 xz -T1>\n"
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"CW<find . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "A opção B<-P> para B<xargs>(1) define o número de processos paralelos do B<xz>. O melhor valor para a opção B<-n> depende de quantos arquivos devem ser compactados. Se houver apenas alguns arquivos, o valor provavelmente deve ser 1; com dezenas de milhares de arquivos, 100 ou até mais podem ser apropriados para reduzir o número de processos de B<xz> que B<xargs>(1) eventualmente criará."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "A opção B<-T1> para B<xz> existe para forçá-lo ao modo de thread única, porque B<xargs>(1) é usado para controlar a quantidade de paralelização."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Modo robô"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Calcular quantos bytes foram salvos no total depois de compactar vários arquivos:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --robot --list *.xz | awk '/^totals/{print $5-$4}'>\n"
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "CW<xz --robot --list *.xz | awk '/^totals/{print $5-$4}'>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Um script pode querer saber que está usando B<xz> novo o suficiente. O seguinte script B<sh>(1) verifica se o número da versão da ferramenta B<xz> é pelo menos 5.0.0. Este método é compatível com versões beta antigas, que não suportavam a opção B<--robot>:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<if ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
#| "        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
#| "    echo \"Your xz is too old.\"\n"
#| "fi\n"
#| "unset XZ_VERSION LIBLZMA_VERSION>\n"
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"CW<if ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Definir um limite de uso de memória para descompactação usando B<XZ_OPT>, mas se um limite já tiver sido definido, não o aumentar:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<NEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
#| "OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
#| "if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
#| "    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
#| "    export XZ_OPT\n"
#| "fi>\n"
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""
"CW<NEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "O uso mais simples para cadeias de filtro personalizadas é personalizar uma predefinição LZMA2. Isso pode ser útil, porque as predefinições abrangem apenas um subconjunto das combinações potencialmente úteis de configurações de compactação."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "As colunas CompCPU das tabelas das descrições das opções B<-0> ... B<-9> e B<--extreme> são úteis ao personalizar as predefinições LZMA2. Aqui estão as partes relevantes coletadas dessas duas tabelas:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Se você sabe que um arquivo requer um dicionário um tanto grande (por exemplo, 32\\ MiB) para compactar bem, mas deseja comprimi-lo mais rapidamente do que B<xz -8> faria, uma predefinição com um valor CompCPU baixo (por exemplo, 1) pode ser modificado para usar um dicionário maior:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --lzma2=preset=1,dict=32MiB foo.tar>\n"
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "CW<xz --lzma2=preset=1,dict=32MiB foo.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Com certos arquivos, o comando acima pode ser mais rápido que B<xz -6> enquanto compacta significativamente melhor. No entanto, deve-se enfatizar que apenas alguns arquivos se beneficiam de um grande dicionário, mantendo o valor CompCPU baixo. A situação mais óbvia, onde um grande dicionário pode ajudar muito, é um arquivo contendo arquivos muito semelhantes de pelo menos alguns megabytes cada. O tamanho do dicionário deve ser significativamente maior do que qualquer arquivo individual para permitir que o LZMA2 aproveite ao máximo as semelhanças entre arquivos consecutivos."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Se o uso muito alto de memória do compactador e do descompactador for bom e o arquivo que está sendo compactado tiver pelo menos várias centenas de megabytes, pode ser útil usar um dicionário ainda maior do que os 64 MiB que o B<xz -9> usaria:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -vv --lzma2=dict=192MiB big_foo.tar>\n"
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "CW<xz -vv --lzma2=dict=192MiB big_foo.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Usar B<-vv> (B<--verbose --verbose>) como no exemplo acima pode ser útil para ver os requisitos de memória do compactador e do descompactador. Lembre-se que usar um dicionário maior que o tamanho do arquivo descompactado é desperdício de memória, então o comando acima não é útil para arquivos pequenos."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "Às vezes, o tempo de compactação não importa, mas o uso de memória do descompactador deve ser mantido baixo, por exemplo, para possibilitar a descompactação do arquivo em um sistema embarcado. O comando a seguir usa B<-6e> (B<-6 --extreme>) como base e define o dicionário como apenas 64\\ KiB. O arquivo resultante pode ser descompactado com XZ Embedded (é por isso que existe B<--check=crc32>) usando cerca de 100\\ KiB de memória."

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --check=crc32 --lzma2=preset=6e,dict=64KiB foo>\n"
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "CW<xz --check=crc32 --lzma2=preset=6e,dict=64KiB foo>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Se você deseja espremer o máximo de bytes possível, ajustar o número de bits de contexto literal (I<lc>) e o número de bits de posição (I<pb>) às vezes pode ajudar. Ajustar o número de bits de posição literal (I<lp>) também pode ajudar, mas geralmente I<lc> e I<pb> são mais importantes. Por exemplo, um arquivo de código-fonte contém principalmente texto US-ASCII, então algo como o seguinte pode fornecer um arquivo ligeiramente (como 0,1\\ %) menor que B<xz -6e> (tente também sem B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --lzma2=preset=6e,pb=0,lc=4 source_code.tar>\n"
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "CW<xz --lzma2=preset=6e,pb=0,lc=4 source_code.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "O uso de outro filtro junto com o LZMA2 pode melhorar a compactação com determinados tipos de arquivo. Por exemplo, para compactar uma biblioteca compartilhada x86-32 ou x86-64 usando o filtro x86 BCJ:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --x86 --lzma2 libfoo.so>\n"
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "CW<xz --x86 --lzma2 libfoo.so>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Observe que a ordem das opções de filtro é significativa. Se B<--x86> for especificado após B<--lzma2>, B<xz> dará um erro, porque não pode haver nenhum filtro após LZMA2 e também porque o filtro x86 BCJ não pode ser usado como o último filtro em a corrente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "O filtro Delta junto com LZMA2 pode dar bons resultados com imagens bitmap. Ele geralmente deve superar o PNG, que possui alguns filtros mais avançados do que o delta simples, mas usa Deflate para a compactação real."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "A imagem deve ser salva em formato não compactado, por exemplo, como TIFF não compactado. O parâmetro de distância do filtro Delta é definido para corresponder ao número de bytes por pixel na imagem. Por exemplo, bitmap RGB de 24 bits precisa de B<dist=3>, e também é bom passar B<pb=0> para LZMA2 para acomodar o alinhamento de três bytes:"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --delta=dist=3 --lzma2=pb=0 foo.tiff>\n"
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "CW<xz --delta=dist=3 --lzma2=pb=0 foo.tiff>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Se várias imagens foram colocadas em um único arquivo (por exemplo, B<.tar>), o filtro Delta também funcionará, desde que todas as imagens tenham o mesmo número de bytes por pixel."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "VEJA TAMBÉM"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr ""

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr ""

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec - Pequenos descompactadores .xz e .lzma"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<opção...>] [I<arquivo...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<opção...>] [I<arquivo...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> é uma ferramenta de descompactação baseada em liblzma somente para arquivos B<.xz> (e somente B<.xz>). B<xzdec> destina-se a funcionar como um substituto para B<xz>(1) nas situações mais comuns em que um script foi escrito para usar B<xz --decompress --stdout> (e possivelmente alguns outras opções comumente usadas) para descompactar arquivos B<.xz>. B<lzmadec> é idêntico a B<xzdec> exceto que B<lzmadec> tem suporte a arquivos B<.lzma> em vez de arquivos B<.xz>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Para reduzir o tamanho do executável, B<xzdec> não tem suporte ao uso de várias threads ou a localização, e não lê opções de variáveis de ambiente B<XZ_DEFAULTS> e B<XZ_OPT>. B<xzdec> não tem suporte à exibição de informações de progresso intermediárias: enviar B<SIGINFO> para B<xzdec> não faz nada, mas enviar B<SIGUSR1> encerra o processo em vez de exibir informações de progresso."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Ignorada para compatibilidade B<xz>(1). B<xzdec> tem suporte a apenas descompactação."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Ignorada para compatibilidade B<xz>(1). B<xzdec> nunca cria ou remove quaisquer arquivos."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Ignorada para compatibilidade B<xz>(1). B<xzdec> sempre grava os dados descompactados para a saída padrão."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Especificar isso uma vez não faz nada, pois B<xzdec> nunca exibe nenhum aviso ou notificação. Especifique isso duas vezes para suprimir erros."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Ignorada para compatibilidade B<xz>(1). B<xzdec> nunca usa o status de saída 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Exibe uma mensagem de ajuda e sai com sucesso."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Mostra o número da versão do B<xzdec> e liblzma."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Correu tudo bem."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> não possui nenhuma mensagem de aviso como o B<xz>(1), portanto, o status de saída 2 não é usado por B<xzdec>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Use B<xz>(1) em vez de B<xzdec> ou B<lzmadec> para uso diário normal. B<xzdec> ou B<lzmadec> destinam-se apenas a situações em que é importante ter um descompactador menor do que o B<xz>(1) completo."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> e B<lzmadec> não são realmente tão pequenos. O tamanho pode ser reduzido ainda mais eliminando recursos do liblzma no tempo de compilação, mas isso geralmente não deve ser feito para executáveis distribuídos em distribuições típicas de sistemas operacionais não embarcados. Se você precisa de um descompactador B<.xz> realmente pequeno, considere usar o XZ Embedded."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "2013-06-30"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo - mostra informações armazenadas no cabeçalho do arquivo .lzma"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<arquivo...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> mostra as informações armazenadas no cabeçalho do arquivo B<.lzma>. Ele lê os primeiros 13 bytes do I<arquivo> especificado, decodifica o cabeçalho e o imprime na saída padrão em formato legível por humanos. Se nenhum I<arquivo> for fornecido ou I<arquivo> for B<->, a entrada padrão será lida."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "Normalmente, as informações mais interessantes são o tamanho descompactado e o tamanho do dicionário. O tamanho não compactado pode ser mostrado apenas se o arquivo estiver na variante de formato B<.lzma> não transmitido. A quantidade de memória necessária para descompactar o arquivo é de algumas dezenas de kilobytes mais o tamanho do dicionário."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> está incluído no XZ Utils principalmente para compatibilidade com versões anteriores do LZMA Utils."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "BUGS"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> usa B<MB> enquanto o sufixo correto seria B<MiB> (2^20 bytes). Isso é para manter a saída compatível com LZMA Utils."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, fuzzy, no-wrap
#| msgid "2013-06-30"
msgid "2025-03-06"
msgstr "2013-06-30"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff - compara arquivos compactados"

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<xz> [I<option...>] [I<file...>]"
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xz> [I<opção...>] [I<arquivo...>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<xzfgrep> \\&..."
msgid "B<xzdiff> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<lzgrep> \\&..."
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<lzfgrep> \\&..."
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "O comando denominado B<lzless> é fornecido para compatibilidade com versões anteriores do LZMA Utils."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<xz> [I<option...>] [I<file...>]"
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xz> [I<opção...>] [I<arquivo...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<lzgrep> \\&..."
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<lzegrep> \\&..."
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<lzfgrep> \\&..."
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-v>, B<--verbose>"
msgid "B<-r>, B<--recursive>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-f>, B<--force>"
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-V>, B<--version>"
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-l>, B<--list>"
msgid "B<-Z>, B<--null>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-l>, B<--list>"
msgid "B<-z>, B<--null-data>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--include=>I<glob>"
msgstr "B<--files>[B<=>I<arquivo>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--exclude-from=>I<file>"
msgstr "B<--files>[B<=>I<arquivo>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "O comando denominado B<lzless> é fornecido para compatibilidade com versões anteriores do LZMA Utils."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "Pelo menos uma correspondência foi encontrada em pelo menos um dos arquivos de entrada. Nenhum erro ocorreu."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "Nenhuma correspondência foi encontrada em nenhum dos arquivos de entrada. Nenhum erro ocorreu."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "Ocorreu um ou mais erros. Não se sabe se foram encontradas correspondências."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless - visualiza arquivos (texto) compactados em xz ou lzma"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<arquivo>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
#, fuzzy
#| msgid "B<lzless> [I<file>...]"
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<arquivo>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> usa B<less>(1) para apresentar sua saída. Ao contrário de B<xzmore>, sua escolha de pager não pode ser alterada pela configuração de uma variável de ambiente. Os comandos são baseados em B<more>(1) e B<vi>(1) e permitem movimento e busca para frente e para trás. Consulte o manual B<less>(1) para obter mais informações."

#. type: Plain text
#: ../src/scripts/xzless.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "O comando denominado B<lzless> é fornecido para compatibilidade com versões anteriores do LZMA Utils."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Uma lista de caracteres especiais para o shell. Definido por B<xzless> a menos que já esteja definido no ambiente."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Defina como uma linha de comando para invocar o descompactador B<xz>(1) para pré-processar os arquivos de entrada para B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore - visualiza arquivos (texto) compactados em xz ou lzma"

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "B<xzless> [I<file>...]"
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzless> [I<arquivo>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "B<lzless> [I<file>...]"
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<arquivo>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "O comando denominado B<lzless> é fornecido para compatibilidade com versões anteriores do LZMA Utils."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
