# SPDX-License-Identifier: 0BSD
#
# Vietnamese translations for xz package
# <PERSON><PERSON>n dịch tiếng Việt cho gói xz.
# This file is published under the BSD Zero Clause License.
# Tr<PERSON><PERSON> <<EMAIL>>, 2014-2024.
#
msgid ""
msgstr ""
"Project-Id-Version: xz 5.6.0-pre2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2024-02-24 10:23+0700\n"
"Last-Translator: Tr<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Vietnamese <<EMAIL>>\n"
"Language: vi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Gtranslator 42.0\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Đối số cho --block-list không hợp lệ"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Quá nhiều đối số cho --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "Trong --block-list, cỡ khối thiếu sau số mắt xích bộ lọc '%c:'"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 chỉ có thể dùng như là phần tử cuối trong --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Không hiểu kiểu định dạng tập tin"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Không hỗ trợ kiểu kiểm tra toàn vẹn"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Chỉ được đưa ra một tập tin cho “--files” hay “--files0”."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Biến môi trường %s chứa quá nhiều đối số"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Hỗ trợ nén đã bị vô hiệu hóa tại thời điểm biên dịch"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Hỗ trợ giải nén đã bị vô hiệu hóa tại thời điểm biên dịch"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Nén tệp lzip (.lz) không được hỗ trợ"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list bị bỏ qua trừ khi nén theo định dạng .xz"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Với --format=raw, --suffix=.SUF được yêu cầu trừ trường hợp ghi ra đầu ra tiêu chuẩn"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Số lượng bộ lọc tối đa là bốn"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Có lỗi trong tùy chọn --filters%s=FILTERS:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Mức giới hạn dùng bộ nhớ là quá thấp cho việc cài đặt bộ lọc đã cho."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "móc xích lọc %u được sử dụng bởi --block-list nhưng lại chưa đưa ra gì với --filters%u="

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Dùng hiện tại trong chế độ thô là ngớ ngẩn."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "Các tùy chọn trích xuất của chỉnh trước có thể biến đổi phụ thuộc vào phiên bản."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Định dạng .lzma chỉ hỗ trợ bộ lọc LZMA1"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 không thể được dùng với định dạng .xz"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "Móc xích lọc %u là không tương thích với --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Chuyển sang chế độ đơn tuyến trình bởi vì --flush-timeout"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Các tùy chọn không được hỗ trợ trong chuỗi móc xích %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Dùng đến %<PRIu32> tuyến trình."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Không hỗ trợ lọc móc xích hay tùy chọn lọc"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Giải nén sẽ cần %s MiB bộ nhớ."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Đã giảm số lượng tuyến trình từ %s xuống %s để không vượt quá giới hạn sử dụng bộ nhớ là %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Đã giảm số lượng tuyến trình từ %s xuống còn một. Giới hạn sử dụng bộ nhớ tự động %s MiB vẫn đang bị vượt quá. Cần có %s MiB bộ nhớ. Vẫn tiếp tục."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Chuyển sang chế độ đơn tuyến trình để không vượt quá giới hạn sử dụng bộ nhớ là %sMiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Chỉnh cỡ từ điển LZMA%c từ %s MiB thành %s MiB để không vượt quá giới hạn tiêu dùng bộ nhớ là %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Chỉnh cỡ từ điển LZMA%c cho --filters%u từ %s MiB thành %s MiB để không vượt quá giới hạn tiêu dùng bộ nhớ là %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Gặp lỗi thay đổi chuỗi móc xích lọc %u: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Gặp lỗi khi tạo một ống dẫn: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: hàm poll() bị lỗi: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Tập tin có lẽ đã bị di chuyển, không phải gỡ bỏ"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Không thể gỡ bỏ: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Không thể đặt chủ sở hữu tập tin: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Không thể đặt nhóm tập tin: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Không thể đặt chế độ đọc ghi cho tập tin: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Gặp lỗi khi đóng tập tin: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Gặp lỗi khi đóng tập tin: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Gặp lỗi khi lấy các cờ trạng thái tập tin từ đầu vào tiêu chuẩn: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Là một liên kết mềm nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Không phải là một thư mục nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Không phải là tập tin thường nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Tập tin có đặt bít setuid hoặc setgid nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Tập tin có bít sticky nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Tập tin đầu vào có nhiều hơn một liên kết cứng nên bỏ qua"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Tên tập tin trống rỗng nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Gặp lỗi khi phục hồi các cờ trạng thái tới đầu vào tiêu chuẩn: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Gặp lỗi khi lấy các cờ trạng thái tập tin từ đầu vào tiêu chuẩn: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Gặp lỗi khi đóng tập tin: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s: Không phải là tập tin thường nên bỏ qua"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Gặp lỗi khi phục hồi cờ O_APPEND trên đầu ra tiêu chuẩn: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Gặp lỗi khi đóng tập tin: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Gặp lỗi khi di chuyển vị trí đọc khi cố tạo một tập tin rải rác: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Lỗi đọc: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Gặp lỗi khi di chuyển vị trí đọc tập tin: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Kết thúc tập tin bất ngờ"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Lỗi ghi: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Bị tắt"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Tổng dung lượng bộ nhớ vật lý (RAM):"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Số luồng bộ xử lý:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Nén:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Giải nén:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Giải nén đa luồng:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Mặc định cho -T0:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Thông tin phần cứng:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Đã chạm mốc giới hạn sử dụng bộ nhớ:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Các luồng dữ liệu:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Khối:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Kích cỡ đã nén:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Kích cỡ đã giải nén:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Tỉ lệ:"

#: src/xz/list.c
msgid "Check:"
msgstr "Kiểm tra:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Đệm luồng dữ liệu:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Bộ nhớ cần:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Kích cỡ phần đầu:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Số lượng tập tin:"

#: src/xz/list.c
msgid "Stream"
msgstr "Luồng dữ liệu"

#: src/xz/list.c
msgid "Block"
msgstr "Khối"

#: src/xz/list.c
msgid "Blocks"
msgstr "Khối"

#: src/xz/list.c
msgid "CompOffset"
msgstr "BùNén"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "BùGiảiNén"

#: src/xz/list.c
msgid "CompSize"
msgstr "CỡNén"

#: src/xz/list.c
msgid "UncompSize"
msgstr "CỡGiảiNén"

#: src/xz/list.c
msgid "TotalSize"
msgstr "CỡTổng"

#: src/xz/list.c
msgid "Ratio"
msgstr "Tỷ lệ"

#: src/xz/list.c
msgid "Check"
msgstr "Kiểm tra"

#: src/xz/list.c
msgid "CheckVal"
msgstr "Gtr.Kiểm"

#: src/xz/list.c
msgid "Padding"
msgstr "Đệm"

#: src/xz/list.c
msgid "Header"
msgstr "Phần đầu"

#: src/xz/list.c
msgid "Flags"
msgstr "Cờ"

#: src/xz/list.c
msgid "MemUsage"
msgstr "DùngBộNhớ"

#: src/xz/list.c
msgid "Filters"
msgstr "Bộ lọc"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Không"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "ChưaBiết2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "ChưaBiết3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "ChưaBiết5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "ChưaBiết6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "ChưaBiết7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "ChưaBiết8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "ChưaBiết9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "ChưaBiết11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "ChưaBiết12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "ChưaBiết13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "ChưaBiết14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "ChưaBiết15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Tập tin trống rỗng"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Là quá nhỏ đối với tập tin .xz hợp lệ"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Luồng    Khối          Nén     Giải nén  Tỷ lệ  Ktra    Tập tin"

#: src/xz/list.c
msgid "Yes"
msgstr "Có"

#: src/xz/list.c
msgid "No"
msgstr "Không"

#: src/xz/list.c
#, fuzzy
#| msgid "  Minimum XZ Utils version: %s\n"
msgid "Minimum XZ Utils version:"
msgstr "  Phiên bản “XZ Utils” tối thiểu: %s\n"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s tập tin\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Tổng cộng:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list chỉ hoạt động trên các tập tin .xz (--format=xz hay --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Thử 'lzmainfo' với các tập tin .lzma."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list không hỗ trợ đọc từ đầu vào tiêu chuẩn"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Gặp lỗi khi đọc tên tập tin: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Gặp kết thúc đầu vào bất ngờ khi đọc các tên tập tin"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Gặp ký hiệu null khi đọc tên tập tin; có lẽ ý bạn muốn là dùng “--files0” chứ không phải “--files'?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Nén và giải nén với --robot vẫn chưa được hỗ trợ."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Không thể đọc dữ liệu từ đầu vào tiêu chuẩn khi đọc tập tin từ đầu vào tiêu chuẩn"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Lỗi nội bộ (lỗi)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Không thể thiết lập bộ xử lý tín hiệu"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Không có kiểm tra toàn vẹn nên không thể thẩm tra tính toàn vẹn của tập tin"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Kiểu kiểm tra toàn vẹn chưa được hỗ trợ; nên không thể thẩm tra tính toàn vẹn của tập tin"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Đã chạm mốc giới hạn sử dụng bộ nhớ"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Không nhận ra định dạng tập tin"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Tùy chọn không được hỗ trợ"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Dữ liệu đã nén bị hỏng"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Gặp kết thúc đầu vào bất ngờ"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "Yêu cầu cần có %s MiB bộ nhớ. Nhưng giới hạn bị tắt."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "Yêu cầu cần có %s MiB bộ nhớ. Nhưng giới hạn là %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Móc xích lọc: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "Hãy chạy lệnh “%s --help” để xem thông tin thêm."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "Usage: %s [OPTION]... [FILE]...\n"
#| "Compress or decompress FILEs in the .xz format.\n"
#| "\n"
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""
"Cách dùng: %s [TÙY CHỌN]... [TẬP TIN]...\n"
"Nén hoặc giải nén các TẬP TIN có định dạng .xz.\n"
"\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "Các tùy chọn dài bắt buộc phải có đối số thì với tùy chọn ngắn cũng vậy.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr " Chế độ thao tác:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force compression"
msgstr "Giải nén:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force decompression"
msgstr "Giải nén:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
" Bộ chỉnh sửa thao tác:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "Gặp lỗi khi ghi dữ liệu vào đầu ra tiêu chuẩn"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --single-stream decompress only the first stream, and silently\n"
#| "                      ignore possible remaining input data"
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr ""
"      --single-stream chỉ giải nén luồng dữ liệu đầu, và bỏ qua\n"
"                      dữ liệu đầu vào còn lại có thể"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Basic file format and compression options:\n"
msgid "Basic file format and compression options:"
msgstr ""
"\n"
" Các tùy chọn về định dạng và nén cơ bản:\n"

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --ignore-check  don't verify the integrity check when decompressing"
msgid "don't verify the integrity check when decompressing"
msgstr "      --ignore-check  không thẩm tra tính toàn vẹn khi giải nén"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -0 ... -9           compression preset; default is 6; take compressor *and*\n"
#| "                      decompressor memory usage into account before using 7-9!"
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""
"  -0 ... -9           đặt mức nén; mặc định là 6; tiêu dùng nhiều bộ nhớ khi nén\n"
"                      và giải nén, nên tính toán trước khi dùng 7-9!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -e, --extreme       try to improve compression ratio by using more CPU time;\n"
#| "                      does not affect decompressor memory requirements"
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""
"  -e, --extreme       cố gắng nâng cao mức nén bằng cách dùng nhiều CPU hơn;\n"
"                      nhưng không yêu cần nhiều bộ nhớ khi giải nén"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -T, --threads=NUM   use at most NUM threads; the default is 0 which uses\n"
#| "                      as many threads as there are processor cores"
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""
"  -T, --threads=SỐ    dùng tối đa là SỐ tuyến trình; mặc định là 0 ý là\n"
"                      dùng số lượng bằng số lõi vi xử lý"

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-size=SIZE\n"
#| "                      start a new .xz block after every SIZE bytes of input;\n"
#| "                      use this to set the block size for threaded compression"
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""
"      --block-size=CỠ\n"
"                      bắt đầu một khối .xz mới sau mỗi CỠ byte của đầu vào;\n"
"                      dùng tùy chọn này để đặt cỡ khối cho nén tuyến trình"

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-list=BLOCKS\n"
#| "                      start a new .xz block after the given comma-separated\n"
#| "                      intervals of uncompressed data; optionally, specify a\n"
#| "                      filter chain number (0-9) followed by a ':' before the\n"
#| "                      uncompressed data size"
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""
"      --block-list=CỠ\n"
"                      bắt đầu một khối .xz mới sau một danh sách ngăn\n"
"                      cách bằng dấu phẩy nhịp dữ của dữ liệu chưa nén; tùy chọn\n"
"                      chỉ định số móc xích bộ lọc (0-9) theo sau là ':' trước\n"
"                      kích cỡ dữ liệu chưa nén"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --flush-timeout=TIMEOUT\n"
#| "                      when compressing, if more than TIMEOUT milliseconds has\n"
#| "                      passed since the previous flush and reading more input\n"
#| "                      would block, all pending data is flushed out"
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""
"      --flush-timeout=THỜI_GIAN_CHỜ\n"
"                      khi đang nén, nếu đã trải qua hơn THỜI_GIAN_CHỜ milli-giây\n"
"                      kể từ lần đẩy dữ liệu lên đĩa trước đó và đang đọc thêm\n"
"                      khối nữa, mọi dữ liệu đang chờ sẽ được ghi lên đĩa"

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --no-adjust     if compression settings exceed the memory usage limit,\n"
#| "                      give an error instead of adjusting the settings downwards"
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr ""
"      --no-adjust     nếu các cài đặt nén vượt quá giới hạn dùng bộ nhớ,\n"
"                      đưa ra một lỗi thay vì sửa đổi các cài đặt xuống"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Custom filter chain for compression (alternative for using presets):"
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""
"\n"
" Móc xích lọc tùy chỉnh cho nén (thay cho việc dùng chỉnh trước):"

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| "  --filters=FILTERS   set the filter chain using the liblzma filter string\n"
#| "                      syntax; use --filters-help for more information"
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""
"\n"
"  --filters=CÁC_BỘ_LỌC  đặt móc xích bộ lọc sử dụng cú pháp chuỗi bộ lọc\n"
"                      liblzma dùng để biết thêm chi tiết"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters1=FILTERS ... --filters9=FILTERS\n"
#| "                      set additional filter chains using the liblzma filter\n"
#| "                      string syntax to use with --block-list"
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""
"  --filters1=CÁC_BỘ_LỌC ... --filters9=CÁC_BỘ_LỌC\n"
"                      đặt móc xích lọc thêm sử dụng cú pháp chuỗi bộ lọc\n"
"                      liblzma để dùng với --block-list"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters-help      display more information about the liblzma filter string\n"
#| "                      syntax and exit."
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""
"  --filters-help      hiển thị thêm thông tin về cú pháp bộ lọc liblzma\n"
"                      rồi thoát."

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Number of processor threads:"
msgid "number of position bits"
msgstr "Số luồng bộ xử lý:"

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "compression mode"
msgstr "Giải nén:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
" Tùy chọn khác:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -Q, --no-warn       make warnings not affect the exit status"
msgid "make warnings not affect the exit status"
msgstr ""
"  -Q, --no-warn       làm cho các cảnh báo không ảnh hưởng đến\n"
"                        trạng thái thoát"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr ""
"      --robot         dùng các thông báo mà máy có thể phân tích\n"
"                        (hữu dụng với scripts)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --info-memory   display the total amount of RAM and the currently active\n"
#| "                      memory usage limits, and exit"
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr ""
"      --info-memory   hiển thị tổng lượng RAM và mức giới hạn tiêu dùng\n"
"                        bộ nhớ hiện tại, rồi thoát"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr "  -V, --version       hiển thị số phiên bản và thoát"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"Không có TẬP_TIN, hoặc TẬP_TIN là “-”, thì đọc đầu vào tiêu chuẩn.\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr ""
"Hãy báo cáo lỗi cho <%s> (bằng tiếng Anh hoặc Phần Lan).\n"
"Thông báo lỗi dịch cho: <https://translationproject.org/team/vi.html>.\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "Trang chủ %s: <%s>.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "ĐÂY LÀ PHIÊN BẢN PHÁT TRIỂN VÀ NÓ KHÔNG PHÙ HỢP VỚI MỤC ĐÍCH SẢN XUẤT."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy, c-format
#| msgid ""
#| "Filter chains are set using the --filters=FILTERS or\n"
#| "--filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain\n"
#| "can be separated by spaces or '--'. Alternatively a preset <0-9>[e] can be\n"
#| "specified instead of a filter chain.\n"
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""
"Các móc xích lọc được đặt bằng --filters=CÁC_BỘ_LỌC hoặc các tùy chọn\n"
"--filters1=CÁC_BỘ_LỌC ... --filters9=CÁC_BỘ_LỌC. Mỗi bộ lọc trong chuỗi\n"
"có thể được ngăn cách bằng khoảng trắng hoặc '--'. Cách khác là dùng <0-9>[e]\n"
"để chỉ định thay thay cho móc xích lọc.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "Các bộ lọc và các tùy chọn của chúng được hỗ trợ là:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be 'name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s: Các tùy chọn phải là các cặp “tên=giá_trị” ngăn cách nhau bằng dấu phẩy"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Tên tùy chọn không hợp lệ"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s: Giá trị của tùy chọn không hợp lệ"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "Hiện nay chưa hỗ trợ LZMA1/LZMA2: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Tổng số lượng lc và lp không được vượt quá 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Tên tập tin có phần hậu tố chưa biết nên bỏ qua"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Tập tin đã sẵn có hậu tố “%s” nên bỏ qua"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Hậu tố tên tập tin không hợp lệ"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s: Giá trị không phải là số thập phân nguyên không âm"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Hậu tố (đơn vị) nhân tố không hợp lệ"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Các hậu tố (đơn vị) hợp lệ là “KiB” (2^10), “MiB” (2^20), và “GiB” (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Giá trị của tùy chọn “%s” phải nằm trong vùng [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Dữ liệu đã nén không thể đọc từ thiết bị cuối"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Dữ liệu đã nén không thể ghi ra thiết bị cuối"

#: src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""
"Cách dùng: %s [--help] [--version] [TẬP TIN]...\n"
"Hiển thị thông tin được lưu trong phần đầu của tập tin .lzma"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Show information stored in the .lzma file header."
msgstr ""
"Cách dùng: %s [--help] [--version] [TẬP TIN]...\n"
"Hiển thị thông tin được lưu trong phần đầu của tập tin .lzma"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "Tập tin quá nhỏ để có thể là một tin .lzma"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Không phải là một tập tin .lzma"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Gặp lỗi khi ghi dữ liệu vào đầu ra tiêu chuẩn"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Lỗi chưa biết"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "Tùy chọn không được hỗ trợ"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "Không hỗ trợ lọc móc xích hay tùy chọn lọc"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s: Tên tùy chọn không hợp lệ"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s: Hậu tố (đơn vị) nhân tố không hợp lệ"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s: Không hiểu kiểu định dạng tập tin"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "LZMA1 không thể được dùng với định dạng .xz"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "Số lượng bộ lọc tối đa là bốn"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -z, --compress      force compression\n"
#~ "  -d, --decompress    force decompression\n"
#~ "  -t, --test          test compressed file integrity\n"
#~ "  -l, --list          list information about .xz files"
#~ msgstr ""
#~ "  -z, --compress      ép buộc nén\n"
#~ "  -d, --decompress    ép buộc giải nén\n"
#~ "  -t, --test          kiểm tra tính toàn vẹn của tập tin nén\n"
#~ "  -l, --list          liệt kê các thông tin về tập tin .xz"

#~ msgid ""
#~ "  -k, --keep          keep (don't delete) input files\n"
#~ "  -f, --force         force overwrite of output file and (de)compress links\n"
#~ "  -c, --stdout        write to standard output and don't delete input files"
#~ msgstr ""
#~ "  -k, --keep          giữ lại (đừng xóa) tập tin đầu vào\n"
#~ "  -f, --force         buộc ghi đè tập tin đầu ra và (giải) nén các liên kết\n"
#~ "  -c, --stdout        ghi ra đầu ra tiêu chuẩn và không xóa tập tin đầu vào"

#~ msgid ""
#~ "      --no-sparse     do not create sparse files when decompressing\n"
#~ "  -S, --suffix=.SUF   use the suffix '.SUF' on compressed files\n"
#~ "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~ "                      omitted, filenames are read from the standard input;\n"
#~ "                      filenames must be terminated with the newline character\n"
#~ "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgstr ""
#~ "      --no-sparse     đừng tạo các tập tin rải rác khi giải nén\n"
#~ "  -S, --suffix=.ĐUÔI  dùng hậu tố “.ĐUÔI” trên các tập tin nén\n"
#~ "      --files[=TẬP-TIN]  đọc các tập tin cần xử lý từ TẬP-TIN; nếu không có\n"
#~ "                      TẬP-TIN thì tên tập tin sẽ được đọc vào từ đầu vào tiêu\n"
#~ "                      chuẩn; chúng phải được kết thúc bằng ký tự dòng mới\n"
#~ "      --files0[=TẬP-TIN] giống --files nhưng ký tự kết thúc là null"

#~ msgid ""
#~ "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~ "                      'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'\n"
#~ "  -C, --check=CHECK   integrity check type: 'none' (use with caution),\n"
#~ "                      'crc32', 'crc64' (default), or 'sha256'"
#~ msgstr ""
#~ "  -F, --format=ĐDạng  định dạng tập tin cần mã hóa hoặc giải mã; giá trị có thể\n"
#~ "                      là “auto” (mặc định), “xz”, “lzma”, “lzip”, và “raw”\n"
#~ "  -C, --check=KIỂM    kiểu kiểm tra toàn vẹn: “none” (thận trọng khi dùng),\n"
#~ "                      “crc32”, “crc64” (mặc định), hay “sha256”"

#, no-c-format
#~ msgid ""
#~ "      --memlimit-compress=LIMIT\n"
#~ "      --memlimit-decompress=LIMIT\n"
#~ "      --memlimit-mt-decompress=LIMIT\n"
#~ "  -M, --memlimit=LIMIT\n"
#~ "                      set memory usage limit for compression, decompression,\n"
#~ "                      threaded decompression, or all of these; LIMIT is in\n"
#~ "                      bytes, % of RAM, or 0 for defaults"
#~ msgstr ""
#~ "      --memlimit-compress=GIỚI_HẠN\n"
#~ "      --memlimit-decompress=GIỚI_HẠN\n"
#~ "      --memlimit-mt-decompress=GIỚI_HẠN\n"
#~ "  -M, --memlimit=GIỚI_HẠN\n"
#~ "                      đặt mức giới hạn dùng bộ nhớ cho việc nén, giải nén,\n"
#~ "                      giải nén tuyến trình, hoặc tất cả; GIỚI_HẠN có đơn vị là\n"
#~ "                      byte, % của RAM, hay 0 cho mặc định"

#~ msgid ""
#~ "\n"
#~ "  --lzma1[=OPTS]      LZMA1 or LZMA2; OPTS is a comma-separated list of zero or\n"
#~ "  --lzma2[=OPTS]      more of the following options (valid values; default):\n"
#~ "                        preset=PRE reset options to a preset (0-9[e])\n"
#~ "                        dict=NUM   dictionary size (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=NUM     number of literal context bits (0-4; 3)\n"
#~ "                        lp=NUM     number of literal position bits (0-4; 0)\n"
#~ "                        pb=NUM     number of position bits (0-4; 2)\n"
#~ "                        mode=MODE  compression mode (fast, normal; normal)\n"
#~ "                        nice=NUM   nice length of a match (2-273; 64)\n"
#~ "                        mf=NAME    match finder (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=NUM  maximum search depth; 0=automatic (default)"
#~ msgstr ""
#~ "\n"
#~ "  --lzma1[=CTC]       LZMA1 hay LZMA2; CÁC-TÙY-CHỌN là danh sách của không hoặc\n"
#~ "  --lzma2[=CTC]       hơn các tùy chọn sau đây (giá trị hợp lệ; mặc định):\n"
#~ "                        preset=PRE các tùy chọn tối ưu nén (0-9[e])\n"
#~ "                        dict=SỐ    cỡ từ điển (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=SỐ      số bít ngữ cảnh văn bản (0-4; 3)\n"
#~ "                        lp=SỐ      số bít vị trí văn bản (0-4; 0)\n"
#~ "                        pb=SỐ      số bít vị trí (0-4; 2)\n"
#~ "                        mode=CHẾ_ĐỘ  chế độ nén (fast, normal; normal)\n"
#~ "                        nice=SỐ    chiều dài “tốt” của khớp (2-273; 64)\n"
#~ "                        mf=TÊN     bộ tìm khớp (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=SỐ   mức sâu tìm kiếm tối đa; 0=tự động (mặc định)"

#~ msgid ""
#~ "\n"
#~ "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~ "  --arm[=OPTS]        ARM BCJ filter\n"
#~ "  --armthumb[=OPTS]   ARM-Thumb BCJ filter\n"
#~ "  --arm64[=OPTS]      ARM64 BCJ filter\n"
#~ "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~ "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~ "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~ "  --riscv[=OPTS]      RISC-V BCJ filter\n"
#~ "                      Valid OPTS for all BCJ filters:\n"
#~ "                        start=NUM  start offset for conversions (default=0)"
#~ msgstr ""
#~ "\n"
#~ "  --x86[=OPTS]        bộ lọc x86 BCJ (32-bit và 64-bit)\n"
#~ "  --arm[=OPTS]        bộ lọc ARM BCJ\n"
#~ "  --armthumb[=OPTS]   bộ lọc ARM-Thumb BCJ\n"
#~ "  --arm64[=OPTS]      bộ lọc ARM64 BCJ\n"
#~ "  --powerpc[=OPTS]    bộ lọc PowerPC BCJ (chỉ big endian)\n"
#~ "  --ia64[=OPTS]       bộ lọc IA-64 (Itanium) BCJ\n"
#~ "  --sparc[=OPTS]      bộ lọc SPARC BCJ\n"
#~ "  --riscv[=OPTS]      bộ lọc RISC-V BCJ\n"
#~ "                      các tùy chọn hợp lệ cho mọi bộ lọc BCJ:\n"
#~ "                        start=SỐ khoảng bù khởi đầu cho chuyển đổi (mặc định=0)"

#~ msgid ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta filter; valid OPTS (valid values; default):\n"
#~ "                        dist=NUM   distance between bytes being subtracted\n"
#~ "                                   from each other (1-256; 1)"
#~ msgstr ""
#~ "\n"
#~ "  --delta[=OPTS]      bộ lọc Delta;\n"
#~ "                      CÁC-TÙY-CHỌN hợp lệ (giá trị hợp lệ; mặc định):\n"
#~ "                        dist=SỐ    khoảng cách giữa các byte được trừ từ\n"
#~ "                                   những cái khác (1-256; 1)"

#~ msgid ""
#~ "  -q, --quiet         suppress warnings; specify twice to suppress errors too\n"
#~ "  -v, --verbose       be verbose; specify twice for even more verbose"
#~ msgstr ""
#~ "  -q, --quiet         không xuất các cảnh báo;\n"
#~ "                        chỉ định hai lần nến bạn muốn chặn cả báo lỗi\n"
#~ "  -v, --verbose       thông báo chi tiết; dùng hai lần nếu muốn chi tiết hơn"

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ "  -h, --help          hiển thị trợ giúp dạng ngắn gọn\n"
#~ "                        (chỉ liệt kê các tùy chọn cơ bản)\n"
#~ "  -H, --long-help     hiển thị trợ giúp đầy đủ rồi thoát"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ "  -h, --help          hiển thị trợ giúp dạng ngắn gọn rồi thoát\n"
#~ "  -H, --long-help     hiển thị trợ giúp đầy đủ\n"
#~ "                        (liệt kê cả những tùy chọn cấp cao)"

#~ msgid "Failed to enable the sandbox"
#~ msgstr "Không bật được sandbox"

#~ msgid "Memory usage limit for compression:    "
#~ msgstr "Mức giới hạn dùng bộ nhớ cho nén:     "

#~ msgid "  Streams:            %s\n"
#~ msgstr "  Luồng dữ liệu:      %s\n"

#~ msgid "  Blocks:             %s\n"
#~ msgstr "  Khối:               %s\n"

#~ msgid "  Ratio:              %s\n"
#~ msgstr "  Tỷ lệ nén:          %s\n"

#~ msgid "  Check:              %s\n"
#~ msgstr "  Kiểm tra:           %s\n"

#~ msgid ""
#~ "  Streams:\n"
#~ "    Stream    Blocks      CompOffset    UncompOffset        CompSize      UncompSize  Ratio  Check      Padding"
#~ msgstr ""
#~ "  Luồng dữ liệu:\n"
#~ "     Luồng      Khối           BùNén       BùGiảiNén           CỡNén       CỡGiảiNén   TỷLệ  Ktra           Đệm"

#~ msgid ""
#~ "  Blocks:\n"
#~ "    Stream     Block      CompOffset    UncompOffset       TotalSize      UncompSize  Ratio  Check"
#~ msgstr ""
#~ "  Khối:\n"
#~ "     Luồng      Khối           BùNén       BùGiảiNén          CỡTổng       CỡGiảiNén   TỷLệ  Ktra"

#~ msgid "      CheckVal %*s Header  Flags        CompSize    MemUsage  Filters"
#~ msgstr "       GTrịKiểm %*s    Đầu  Cờ              CỡNén     DùngRAM  BộLọc"

#~ msgid "The selected match finder requires at least nice=%<PRIu32>"
#~ msgstr "Bộ tìm khớp đã chọn yêu cầu mức “tốt” ít nhất là nice=%<PRIu32>"

#~ msgid "Error setting O_NONBLOCK on standard input: %s"
#~ msgstr "Lỗi cài đặt O_NONBLOCK trên đầu vào tiêu chuẩn: %s"

#~ msgid "Error setting O_NONBLOCK on standard output: %s"
#~ msgstr "Lỗi cài đặt O_NONBLOCK trên đầu ra tiêu chuẩn: %s"

#~ msgid ""
#~ "      --block-size=SIZE\n"
#~ "                      when compressing to the .xz format, start a new block\n"
#~ "                      after every SIZE bytes of input; 0=disabled (default)"
#~ msgstr ""
#~ "      --block-size=CỠ\n"
#~ "                      khi nén thành định dạng .xz, bắt đầu khối mới\n"
#~ "                      sau mỗi SỐ byte đầu vào; 0=tắt (mặc định)"
