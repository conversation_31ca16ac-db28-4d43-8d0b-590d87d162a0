# french translation of XZ Utils man
# <AUTHOR> <EMAIL>
# This file is put in the public domain.
# This file is distributed under the same license as the XZ Utils package.
#
#  Translator
# <AUTHOR> <EMAIL>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: XZ Utils 5.2.5\n"
"POT-Creation-Date: 2025-03-25 12:28+0200\n"
"PO-Revision-Date: 2021-12-01 15:17+0100\n"
"Last-Translator: bubu <<EMAIL>> \n"
"Language-Team: French <<EMAIL>> \n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.0\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr ""

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "Utilitaires XZ"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "NOM"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat - Compresser ou décompresser des fichiers .xz et .lzma"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "SYNOPSIS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<option...>] [I<fichier...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "ALIAS DES COMMANDES"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> est équivalent à B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> est équivalent à B<xz --decompress --stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> est équivalent à B<xz --format=lzma>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> est équivalent à B<xz --format=lzma --decompress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> est équivalent à B<xz --format=lzma --decompress -- stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Lors de l'écriture de scripts qui nécessitent de décompresser des fichiers, il est recommandé de toujours utiliser la commande B<xz> avec les arguments appropriés (B<xz -d> ou B<xz -dc>) au lieu des commandes B<unxz> et B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "DESCRIPTION"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> compresse ou décompresse chaque I<fichier> en fonction du mode d'opération choisi. Si aucun I<fichier> n'est donné ou I<fichier> est B<->, B<xz> lit depuis l'entrée standard et écrit les données traitées sur la sortie standard. B<xz> refusera (affichera une erreur et ignorera le I<fichier>) d'écrire les données compressées sur la sortie standard si c'est un terminal. De même, B<xz> refusera de lire des données compressées depuis l'entrée standard si c'est un terminal."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "A moins que B<--sdout> ne soit indiqué, les I<fichiers> autres que B<-> sont écrits dans un nouveau fichier dont le nom est dérivé du nom de I<fichier> source :"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "Lors de la compression, le suffixe du format de fichier cible (B<.xz> ou B<.lzma>) est ajouté au nom de fichier source pour obtenir le nom de fichier cible."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Si le fichier cible existe déjà, une erreur est affichée et le I<fichier> est ignoré."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "Sauf s'il écrit dans la sortie standard, B<xz> affichera un avertissement et ignorera le I<fichier> dans les cas suivants :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<fichier> n'est pas un fichier normal. Les liens symboliques ne sont pas suivis et donc ne sont pas considérés comme des fichiers normaux."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "I<fichier> a plusieurs liens physiques."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "I<fichier> a un setuid, setgid ou sticky bit positionné."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "Le mode d'opération est défini pour compresser et le I<fichier> a déjà un suffixe du format de fichier cible (B<.xz> ou B<.txz> lors d'une compression en format B<.xz>, et B<.lzma> ou B<.tlz> lors d'une compression en format B<.lzma>)."

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "Le mode d'opération est défini pour compresser et le I<fichier> a déjà un suffixe du format de fichier cible (B<.xz> ou B<.txz> lors d'une compression en format B<.xz>, et B<.lzma> ou B<.tlz> lors d'une compression en format B<.lzma>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Après la compression ou la décompression réussie du I<fichier>, B<xz> copie les permissions du propriétaire, du groupe, la date d'accès, et les modifications d'heure depuis le I<fichier> source du fichier cible. Si la copie du groupe échoue, les permissions sont modifiées pour que le fichier cible ne soit pas accessible aux utilisateurs qui n'ont pas les droits d'accès au I<fichier> source. B<xz> ne prend actuellement pas en charge la copie d'autres métadonnées telles que les listes de contrôle d'accès ou les attributs étendus."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "Envoyer B<SIGINFO> ou B<SIGURSR1> au processus B<xz>, lui fait afficher l'information de progression sur l'erreur standard. Cela a un intérêt limité car lorsque l'erreur standard est un terminal, utiliser B<--verbose> affichera automatiquement un indicateur de progression du processus."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Utilisation de la mémoire"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "L'utilisation de la mémoire par B<xz> varie de quelques centaines de kilo-octets à plusieurs gigaoctects en fonction des paramètres de compression. Les réglages utilisés lors de la compression d'un fichier déterminent les besoins en mémoire pour la décompression. Habituellement la décompression nécessite 5\\% à 20\\% de la quantité de mémoire utilisée pour la compression du fichier. Par exemple, décompresser un fichier créé avec B<xz-9> recquiert habituellement 65\\ Mio de mémoire. Bien qu'il soit possible d'avoir des fichiers B<.xz> nécessitant plusieurs gigaoctets de mémoire pour être décompressés."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr ""

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Concaténation et remplissage avec des fichiers .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "Il est possible de concaténer les fichiers B<.xz> tels quel. B<xz> décompressera de tels fichiers comme s'ils étaient un unique fichier B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "La concaténation et le remplissage ne sont pas autorisés avec les fichiers B<.lzma> ou les flux bruts."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "OPTIONS"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Suffixes entiers et valeurs spéciales."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "Dans la plupart des endroits où un argument entier est attendu, un suffixe optionel permet d'indiquer facilement les grands entiers. Il ne doit pas y avoir d'espace entre l'entier et le suffixe."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Multiplier l'entier par 1024 (2^10). B<Ki>, B<k>, B<kB>, B<K> et B<KB> sont acceptés comme synonymes de B<KiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Multiplier l'entier par 1 048 576 (2^20). B<Mi>, B<m>, B<M> et B<MB> sont acceptés comme synonymes de B<MiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Multiplier l'entier par 1 073 741 824 (2^30). B<Gi>, B<g>, B<G> et B<GB> sont acceptés comme synonymes de B<GiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "La valeur spéciale B<max> peut être utilisée pour indiquer la valeur maximale de l'entier prise en charge par l'option."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Mode d'opération"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Si plusieurs options de mode d'opération sont données, la dernière prend effet."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Compresser. C'est le mode d'opération par défaut lorsque aucune option de mode opératoire n'est spécifiée ou qu'aucun autre mode d'opération n'est sous-entendu par le nom de la commande (par exemple B<unxz> sous-entend B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr ""

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Tester l'intégrité des I<fichiers> compressés. Cette option est équivalente à B<--decompress --stdout> sauf que les données décompressées sont rejetées au lieu d'être écrites sur la sortie standard. Aucun fichier n'est créé ou supprimé."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Afficher l'information sur les I<fichiers> compressés. Aucune sortie non-compressée n'est produite et aucun fichier n'est créé ou supprimé. En mode liste, le programme ne peut pas lire les données compressées depuis l'entrée standard ou depuis d'autres sources non adressables."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "La sortie exacte peut varier suivant les versions de B<xz> et les différents paramètres régionaux. Pour une sortie lisible par la machine, utiliser B<--robot --list>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Modificateurs d'opération"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Ne pas effacer les fichiers d'entrée."

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Compresser ou décompresser même si l'entrée est un lien symbolique vers un fichier normal, a plus qu'un lien physique, ou a le bit setuid, setgid ou sticky défini. Les bits setuid, setgid et sticky bits ne sont pas copiés dans le fichier cible."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Cette option a plusieurs effets :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Si le fichier cible existe déjà, l'effacer avant de compresser ou décompresser."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Compresser ou décompresser même si l'entrée est un lien symbolique vers un fichier normal, a plus qu'un lien physique, ou a le bit setuid, setgid ou sticky défini. Les bits setuid, setgid et sticky bits ne sont pas copiés dans le fichier cible."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Lorsque B<xz> est utilisé avec B<--decompress> B<--stdout> et qu'il ne peut pas reconnaitre le type du fichier source, copier le fichier source tel quel dans la sortie standard. Celà permet à B<xzcat> B<--force> d'être utilisé comme B<cat>(1) pour les fichiers qui n'ont pas été compressé avec B<xz>. Remarquez que dans le futur, B<xz> devrait prendre en charge de nouveaux formats de fichiers compressés, ce qui permettra à B<xz> de décompresser plus de types de fichiers au lieu de les copier tels quels dans la sortie standard. B<--format=>I<format> peut être utilisé pour contraindre B<xz> à décompresser seulement un format de fichier."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Écrire les données compressées ou décompressées sur la sortie standard plutôt que dans un fichier. Cela necessite B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Décompresser seulement le premier flux B<.xz> et ignorer silencieusement les possibles données d'entrée résiduelles qui suivent le flux. Normalement ces déchets excédentaires provoquent l'affichage d'une erreur par B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> ne décompresse jamais plus d'un flux à partir de fichiers B<.lzma> ou de flux bruts, mais cette option fait aussi que B<xz> ignorera les données résiduelles après le fichier B<.lzma> ou le flux brut."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Cette option n'a aucun effet si le mode d'opération n'est pas B<--decompress> ou B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Désactiver la création de fichiers peu denses. Par défaut, lors de la décompression en un fichier normal, B<xz> essaie d'en faire un fichier creux si les données décompressées contiennent de longues séquences de zéros binaires. Cela fonctionne aussi lors de l'écriture sur la sortie standard aussi longtemps que la sortie standard est connectée à un fichier normal et que certaines conditions supplémentaires sont satisfaites pour le faire de manière sécurisée. Créer des fichiers creux peut épargner de l'espace disque et accélérer la décompression en réduisant la quantité d'entrées/sorties sur le disque."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "Lors de la compression, utiliser B<.suf> comme suffixe du fichier cible au lieu de B<.xz> ou B<.lzma>. Si B<xz> n'écrit pas sur la sortie standard et si le fichier source a déja le suffixe B<.suf>, un avertissement est affiché et le fichier est ignoré."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "Lors de la compression ou décompression de flux bruts (B<--fomat=raw>), le suffixe doit toujours être spécifié à moins d'écrire sur la sortie standard, car il n'y a pas de suffixe par défaut pour les flux bruts."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<fichier>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Lire les noms de fichier à traiter depuis I<fichier> ; si I<fichier> est omis , les noms de fichier sont lus sur l'entrée standard. Les noms de fichier doivent se terminer avec le caractère de nouvelle ligne. Un tiret (B<->) est considéré comme un nom de fichier normal ; ce qui ne signifie pas entrée standard. Si les noms de fichier sont aussi donnés comme arguments de ligne de commande, ils sont traités avant les noms de fichier lus depuis I<fichier>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<fichier>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Cela est identique à B<--files>[B<=>I<fichier>] sauf que chaque nom de fichier doit se terminer par le caractère null."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Format de fichier basique et options de compression"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<format>, B<--format=>I<format>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Indiquer le I<format> de fichier à compresser ou décompresser :"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "C'est celui par défaut. Lors de la compression, B<auto> est équivalent à B<xz>. Lors de la décompression, le format du fichier en entrée est détecté automatiquement. Notez que les flux bruts (créés avec B<--format=raw>) ne peuvent pas être détectés automatiquement."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Compresser dans le format de fichier B<.xz> ou n'accepter que les fichiers B<.xz> à décompresser."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Compresser au format de fichier B<.lzma> historique, ou n'accepter que les fichiers B<.lzma> lors de la décompression. Le nom alternatif B<alone> est fourni pour la rétrocompatibilité avec les utilitaires LZMA."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Compresser ou décompresser un flux brut (sans en-têtes). Cela est réservé seulement aux utilisateurs aguerris. Pour décoder des flux bruts, vous devez utiliser B<--format=raw> et spécifier explicitement la chaîne de filtre, qui normalement aurait du être stockée dans les en-têtes du conteneur."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<vérif.>, B<--check=>I<vérif.>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Spécifier le type d'intégrité à vérifier. La vérification est calculée à partir des données non-compressées et stockées dans le fichier B<.xz>. Cette option n'a effet que si la compression a été faite dans le format B<.xz> ; le format B<.lzma> ne gère pas les vérifications d'intégrité. Le contrôle d'intégrité (s'il y en a) est vérifié lorsque le fichier B<.xz> est décompressé."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Types de I<vérification> pris en charge :"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Ne pas calculer de vérification d'intégrité du tout. C'est généralement une mauvaise idée. Cela peut être utile lorsque l'intégrité des données est vérifiée de toute façon par d'autres manières."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Calculer CRC32 en utilisant le polynôme de IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Calculer CRC64 en utilisant le polynôme de ECMA-182. C'est la manière utilisée par défaut, car c'est légèrement mieux que CRC32 pour détecter les fichiers endommagés et la différence de vitesse est négligeable."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Calculer SHA-256. C'est quelque peu plus lent que CRC32 et CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "L'intégrité des en-têtes B<.xz> est toujours vérifiée avec CRC32. Il n'est pas possible de le changer ou de le désactiver."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Ne pas contrôler la vérification d'intégrité des données lors de la décompression. Les valeurs CRC32 dans les en-têtes B<.xz> seront normalement toujours vérifiées."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<N'utilisez pas cette option à moins de savoir ce que vous faites.> Les raisons possibles pour utiliser cette option :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Essayer de récupérer des données d'un fichier .xz corrompu."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Accélérer la décompression. Cela importe surtout avec SHA-256 ou avec les fichiers qui ont été compressés extrêmement bien. Il est recommandé de ne pas utiliser cette option dans ce but à moins que l'intégrité du fichier ne soit vérifiée extérieurement d'une autre manière."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Choisir un niveau de compression prédéfini. La valeur par défaut est B<6>. Si plusieurs niveaux de préréglage sont spécifiés, c'est le dernier qui sera pris en compte. Si une chaîne de filtres personnalisée a déjà été choisie, définir un niveau de compression préréglé efface la chaîne de filtres personnalisée."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Les différences entre les préréglages sont plus significatives qu'avec B<gzip>(1) et B<bzip2>(1). les réglages de compression sélectionnés déterminent les exigences en mémoire pour la décompression, ainsi, utiliser un niveau de préréglage trop élevé peut rendre difficile à décompresser un fichier sur un vieux système avec peu de RAM. Clairement, B<ce n'est pas une bonne idée d'utiliser -9 aveuglément pour tout> comme ça l'est souvent avec B<gzip>(1) et B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Ce sont des préréglages relativement rapides. B<0> est parfois plus rapide que B<gzip -9> tout en compressant bien mieux. Les réglages plus élevés ont souvent une rapidité comparable à celle de B<bzip2>(1) avec un taux de compression comparable ou meilleur, même si les résultats dépendent beaucoup du genre de données compressées."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "C'est comme B<-6> mais avec des besoins en mémoire plus élevés pour la compression et la décompression. Ce n'est utile que lorsque les fichiers sont plus gros que 8\\ Mio, 16\\ Mio et 32\\ Mio respectivement."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "Sur le même matériel, la vitesse de décompression est sensiblement un nombre constant d'octets de données compressées par seconde. En d'autres termes, meilleure est la compression, plus rapide sera en général la décompression. Cela signifie aussi que la quantité de sortie non compressée produite par seconde peut varier beaucoup."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "Le tableau suivant résume les caractéristiques des préréglages :"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Préréglage"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "DictSize"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "CompCPU"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "CompMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "DecMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Descriptions des colonnes :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "DictSize est la taille du dictionnaire de LZMA2. Utiliser un dictionnaire plus gros que la taille du fichier non compressé est un gaspillage de mémoire. C'est pourquoi il est bon d'éviter d'utiliser les préréglages de B<-7> à B<-9> lorsqu'il n'y en a pas vraiment besoin. A B<-6> et plus bas, la quantité de mémoire gaspillée est généralement assez basse pour ne pas être un problème."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "CompCPU est une représentation des préréglages de LZMA2 qui affectent la vitesse de compression. La taille du dictionnaire aussi affecte la vitesse, alors comme CompCPU est le même pour les niveaux de B<-6> à B<-9>, les plus haut niveaux tendent à être un peu moins rapides. Pour être encore moins rapide et du coup obtenir peut être une meilleure compression, consultez B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "DecMem contient les besoins en mémoire du décompresseur. Ce sont les réglages de la compression qui déterminent les besoins en mémoire de la décompression. L'exacte utilisation de la mémoire est légèrement supérieure à la taille du dictionnaire LZMA2, mais les valeurs dans la table ont été arrondies au prochain Mio supérieur."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Utilisez un variant plus lent que les préréglages (B<-0> à B<-9>) pour espérer avoir un taux de compression légèrement meilleur, mais en cas de malchance cela peut être pire. L'utilisation mémoire du décompresseur n'est pas affectée, mais l'utilisation mémoire du compresseur augmente un peu aux niveaux de préréglages de B<-0> à B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Depuis qu'il y a deux préréglages avec des tailles de dictionnaire de 4\\ Mio et 8 \\Mio, les préréglages B<-3e> et B<-5e> utilisent des réglages légèrement plus rapides que B<-4e> et B<-6e>, respectivement. De cette manière, il n'y a pas deux préréglages identiques."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "Par exemple, il y a un total de quatre préréglages qui utilisent un dictionnaire de 8 Mio et qui sont dans l'ordre du plus rapide au plus lent : B<-5>, B<-6>, B<-5e> et B<-6e>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Il y a néanmoins des alias trompeurs pour B<-0> et B<-9>, respectivement. Ils ne sont fournis que pour des besoins de rétro-compatibilité avec les utilitaires LZMA. Evitez d'utiliser ces options."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<taille>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "Lors de la compression dans le format B<.xz>, les données de l'entrée sont réparties en blocs de I<taille> octets. Les blocs sont compressés indépendamment les un des autres, ce qui aide avec le mode multithread (multi-threading) et rend possible la décompression à accès aléatoire limité. Cette option est typiquement utilisée pour outrepasser la taille de bloc en mode multithread, mais cette option peut aussi être utilisée en mode single-thread."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--block-size=>I<size>"
msgid "B<--block-list=>I<items>"
msgstr "B<--block-size=>I<taille>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "En mode multi-threadé les tailles de blocs sont stockées dans les en-têtes du bloc. Cela ne se fait pas en mode mono-threadé, la sortie encodée ne sera donc pas identique à celle faite en mode multi-threadé."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<temps_d'attente>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "Lors de la compression, si plus que I<temps_d'attente> millisecondes (un entier positif) se sont écoulées depuis le précédent vidage et que lire plus de données bloquerait, toutes les données d'entrée en attente sont vidées de l'encodeur et mises à disposition dans le flux de sortie. Cela peut être utile si B<xz>  est utilisé pour compresser les données qui sont diffusées sur un réseau. Des petites valeurs de I<temps_d'attente> rendent les données disponibles à l'extrémité réceptrice avec un léger retard, mais les grandes valeurs de I<temps_d'attente> donnent un meilleur taux de compression."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Cette option est désactivée par défaut. Si cette option est indiquée plus d'une fois, la dernière prend effet. La valeur spéciale de I<temps_d'attente> de B<0> peut être utilisée pour explicitement désactiver cette option."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Cette option n'est pas disponible sur les systèmes qui ne sont pas POSIX."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Cette option est encore expérimentale.>  Actuellement, B<xz> ne convient pas pour décompresser le flux en temps réel en raison de la façon dont B<xz> effectue la mise en mémoire tampon."

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--no-sparse>"
msgid "B<--no-sync>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Indiquer une limite d'utilisation de la mémoire pour la compression. Si cette option est indiquée plusieurs fois, c'est la dernière qui est prise en compte."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "La I<limite> peut être indiquée de plusieurs façons :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "La I<limite> peut être une valeur absolue en octets. Utiliser un suffixe d'entier comme B<MiB> peut être utile. Exemple : B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "La I<limite> peut être indiquée sous forme d'un pourcentage de la mémoire physique totale (RAM). Cela peut être particulièrement utile quand la variable d'environnement  B<XZ_DEFAULTS> est indiquée dans un script d'initialisation de l'interpréteur partagé entre différents ordinateurs. De cette façon la limite est automatiquement plus grande sur les systèmes avec plus de mémoire. Exemple : B<--memlimit=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Voir aussi la section B<utilisation de la mémoire>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Régler une limite d'utilisation de la mémoire pour la décompression. Cela a un effet sur le mode B<--list>. Si l'opération n'est pas possible sans dépasser la I<limite>, B<xz> affichera une erreur et la décompression échouera. Voir B<--memlimit-compress=>I<limite> pour les manières possibles d'indiquer la I<limite>."

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--memlimit-decompress=>I<limit>"
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<limite>, B<--memlimit=>I<limite>, B<--memory=>I<limite>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<threads>, B<--threads=>I<threads>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "Actuellement, la seule méthode de gestion avec des threads consiste à séparer l'entrée en blocs et de les compresser indépendamment les uns des autres. La taille par défaut des blocs dépend du niveau de compression et peut-être remplacée avec l'option B<--block-size=>I<taille>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Chaînes de filtres de compresseur personnalisées"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Une chaîne de filtre est comparable à une redirection (pipe) sur la ligne de commande. Lors de la compression, les entrées non compressées vont au premier filtre, dont la sortie va au prochain filtre (s'il y en a). La sortie du dernier filtre est écrite sur le fichier compressé. Le nombre maximal de filtres dans la chaîne est quatre, mais habituellement, un chaîne de filtre n'a qu'un ou deux filtres."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Beaucoup de filtres ont des limitations sur l'endroit où ils peuvent se placer dans la chaîne de filtre : quelques filtres ne peuvent fonctionner qu'en tant que dernier filtre dans la chaîne, quelques uns en tant que non dernier filtre, et d'autres à n'importe quelle position dans la chaîne. Suivant le filtre, cette limitation est soit inhérente au profil du filtre, soit existe pour des raisons de sécurité. "

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Pour voir l'entièreté de la chaîne de filtres et ses I<options>, utilisez B<xz -vv> (ce qui est comme utiliser B<--verbose> deux fois). Cela fonctionne aussi pour voir les options de chaîne de filtres utilisées par les préréglages."

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--filters=>I<filters>"
msgstr "B<--files>[B<=>I<fichier>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<-h>, B<--help>"
msgid "B<--filters-help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<options>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Ajouter le filtre LZMA1 ou LZMA2 à la chaîne de filtres. Ces filtres ne peuvent être utilisés que comme dernier filtre dans la chaîne."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 est un filtre historique, qui n'est pris en charge presque uniquement à cause de l'ancien format de fichier B<.lzma>, qui ne prend en charge que LZMA1. LZMA2 est une version mise à jour de LZMA1 pour régler certains problèmes pratiques de LZMA1. Le format B<xz> utilise LZMA2 et ne prend pas du tout en charge LZMA1. Les taux et vitesses de compression de LZMA1 et LZMA2 sont pratiquement identiques."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 et LZMA2 partagent le même ensemble d'I<options> :"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<préréglage>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<taille>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "La I<taille> du dictionnaire (historique du tampon) indique combien d'octets des données récement décompressées sont gardés en mémoire. L'algorithme essaie de trouver les séquences d'octets répétées (identiques) dans les données décompressées et les remplace par les données actuellement dans le dictionnaire. Plus gros est le dictionnaire, plus grande est la chance de trouver une correspondance. Ainsi, l'augmentation de la I<taille> du dictionnaire augmente habituellement le taux de compression, mais un dictionnaire plus gros que le fichier non compressé est un gachis de mémoire."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "Généralement la I<taille> du dictionnaire est entre 64\\ Kio et 64\\ Mio. Le minimum étant 4\\ Kio. La I<taille> maximale pour la compression est habituellement 1,5\\ Gio (1536\\ Mio). Le décompresseur prend en charge les dictionnaires jusqu'à un octet de moins que 4\\ Gio, ce qui est le maximum pour les formats de flux LZMA1 et LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "La I<taille> du dictionnaire et le chercheur de correspondance (match finder) (I<mf>) déterminent ensemble l'utilisation de la mémoire de l'encodeur LZMA1 ou LZMA2. La même (ou une plus grosse) I<taille> de dictionnaire est requise pour décompresser que ce qui a été utilisé pour la compression, ainsi l'utilisation de la mémoire du décodeur est déterminée par la taille du dictionnaire utilisée lors de la compression. Les en-têtes de B<.xz> stockent la I<taille> de dictionnaire sous la forme 2^I<n> ou 2^I<n> + 2^(I<n>-1), de sorte que ces I<tailles> sont quelque peu préférées pour la compression. Les autres I<tailles> seront arrondies à la hausse lorsque stockées dans les en-têtes de B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Spécifiez le nombre d'octets de contexte littéraux. Le minimum est B<0> et le maximum est B<4>. La valeur par défaut est B<3>. En plus,  la somme de I<lc> et I<lp> ne doit pas excéder B<4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Tous les octets qui ne peuvent pas être codés comme des correspondances sont codés comme des littéraux. C'est à dire que les littéraux sont simplement des octets 8 bits encodés un à la fois."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Indiquer le nombre de bits de position littérale. Le minimum est B<0> et le maximum B<4>; par défaut c'est B<0>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> affecte le type d'alignement dans les données décompressées qui est présumé lors de l'encodage des littéraux. Voir I<pb> ci dessous pour plus d'information sur l'alignement."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Indiquer le nombre de bits de position. Le minimum est B<0> et le maximum B<4>; par défaut B<2>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> affecte quel genre d'alignement est présumé en général dans les données non compressées. Par défaut c'est un alignement de quatre octets (2^I<pb>=2^2=4), ce qui est généralement un bon choix lorsqu'il n'y a pas de meilleure estimation."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Même si l'alignement présumé peut être ajusté avec I<pb> et I<lp>, LZMA1 et LZMA2 favorisent toujours légèrement l'alignement sur 16 octets. Il peut être utile d'en tenir compte lors de la conception de formats de fichiers susceptibles d'être souvent compressés avec LZMA1 ou LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<mf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Les chercheurs de correspondance suivants sont pris en charge. Les formules d'utilisation de la mémoire ci-dessous sont des approximations grossières qui sont les plus proches de la réalité lorsque I<dict> est une puissance de deux."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Chaîne de hachage avec hachage de 2 et 3 octets"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Valeur minimale pour I<nice> : B<3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Utilisation de la mémoire :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 7.5 (if I<dict> E<lt>= 16 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 5.5 + 64 MiB (si I<dict> E<gt> 16 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Chaîne de hachage avec hachage de 2, 3 et 4 octets"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Valeur minimale pour I<nice> : B<4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 7.5 (si I<dict> E<lt>= 32 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 6.5 (si I<dict> E<gt> 32 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Arbre binaire avec hachage de 2 octets"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Valeur minimale pour I<nice> : B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Utilisation de la mémoire : I<dict> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Arbre binaire avec hachage de 2 et 3 octets"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 11.5 (si I<dict> E<lt>= 16 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 9.5 + 64 MiB (si I<dict> E<gt> 16 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Arbre binaire avec hachage 2, 3 et 4 octets"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 11.5 (si I<dict> E<lt>= 32 Mio);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 10.5 (si I<dict> E<gt> 32 Mio)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<mode>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Habituellement, B<fast> est utilisé avec les chercheurs de correspondance de chaîne de hachage et B<normal> avec les chercheurs de correspondance d'arbre binaire. C'est aussi ce que font les I<préréglages>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<nice>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Spécifier ce qui est considéré comme une bonne longueur pour une correspondance. Une fois que la correspondance d'au moins I<nice> octets est trouvée, l'algorithme arrête de chercher de meilleures correspondances possibles."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<profondeur>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Spécifier la profondeur de recherche maximale dans l'outil de recherche de correspondances. La valeur par défaut est B<0>, ce qui fait que le compresseur détermine une I<profondeur> raisonnable en fonction de I<mf> et I<nice>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "Lors du décodage des flux bruts (B<--format=raw>), LZMA2 nécessite seulement la I<taille> du dictionnaire. LZMA1 nécessite aussi I<lc>, I<lp> et I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<--arm>[B<=>I<options>]"
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<options>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<options>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Ajouter un filtre branch/call/jump (BCJ) à la chaîne de filtres. Ces filtres ne peuvent être utilisés que s'ils ne sont pas le dernier filtre de la chaîne de filtrage."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Ces filtres BCJ présentent des problèmes connus liés au taux de compression :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr ""

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Filtre"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Alignement"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Notes"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "32 bits ou 64 bits x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr ""

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr ""

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Grand boutiste seulement"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr ""

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Tous les filtres BCJ prennent en charge les mêmes I<options> :"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<décalage>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Spécifier le I<décalage> de départ qui est utilisé lors de la conversion entre les adresses relatives et absolues. Le I<décalage> doit être un multiple de l'alignement du filtre (voir la table ci-dessus). Sa valeur par défaut est zéro. En pratique, cette dernière convient ; indiquer un I<décalage> personnalisé est la plupart du temps inutile."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<options>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Ajouter le filtre Delta à la chaîne de filtres. Le filtre Delta ne peut être utilisé que s'il n'est pas le dernier filtre dans la chaîne."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "I<options> prises en charge :"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<distance>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "Par exemple, avec B<dist=2> et une entrée huit octets A1 B1 A2 B3 A3 B5 A4 B7, la sortie sera A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Autres options"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Supprimer les avertissements et les notifications. Indiquer cela deux fois supprimera aussi les erreurs. Cette option n'a aucun effet sur le statut de sortie. Cela dit, même si un avertissement était supprimé, le statut de sortie indiquant un avertissement sera encore utilisé."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Être bavard. Si l'erreur standard est connectée à un terminal, B<xz> affichera une barre de progression. Indiquer B<--verbose> deux fois donnera une sortie encore plus bavarde."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "La barre de progression montre l'information suivante :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "Le pourcentage de complétion est montré si la taille du fichier en entrée est connue. Néanmoins, le pourcentage ne peut pas être montré en cas de redirection."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Quantité de données compressées produites (compression) ou consommées (décompression)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Quantité de données non compressées consommées (compression) ou produites (décompression)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Le taux de compression, calculé en divisant la quantité de données compréssées déjà traitées par la quantité de données décompressées déjà traitées."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Vitesse de compression ou de décompression. Elle correspond à la quantité de données non compressées consommées (compression) ou produites (décompression) par seconde. Elle apparait quelques secondes après le début du traitement du fichier par B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Temps écoulé dans le format M:SS ou H:MM:SS."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr ""

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Ne pas mettre l'état de sortie à B<2> même si une condition méritant un avertissement a été détectée. Cette option n'affecte pas le niveau de verbosité, néanmoins, les deux options B<--quiet> et B<--no-warn> doivent être utilisées pour ne pas afficher d'avertissements, ni altérer le statut de sortie."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Afficher les messages dans un format analysable par une machine.  Ceci est destiné à faciliter l'écriture des frontaux qui voudraient utiliser B<xz> plutôt que liblzma, ce qui pourrait être le cas pour différents scripts. La sortie avec cette option activée est destinée à rester stable sur les différentes versions de B<xz>. Consulter le paragraphe B<ROBOT MODE> pour les détails."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr ""

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Afficher un message d'aide décrivant les options les plus couramment utilisées et quitter."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Afficher un message d'aide décrivant toutes les options de B<xz> et quitter."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Afficher le numéro de version de B<xz> et de liblzma dans un format lisible par un humain. Pour obtenir une sortie analysable par la machine, spécifiez B<--robot> avant B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "MODE ROBOT"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Mode liste"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> utilise une sortie séparée par des tabulations. La première colonne de toutes les lignes possède une chaîne qui indique le type d'information trouvée sur cette ligne :"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "C'est toujours la première ligne au début de la liste d'un fichier. La seconde colonne de la ligne est le nom de fichier."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Cette ligne contient l'information globale sur le fichier B<.xz>. Cette ligne est toujours écrite après la ligne B<name>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Ce type de ligne n'est utilisée que lorsque B< --verbose> a été indiquée. Il y a autant de lignes B<stream> qu'il y a de flux dans le fichier B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Ce type de ligne n'est utilisé seulement lorsque B<--verbose> a été indiquée. Il y a autant de lignes B<block> qu'il y a de blocs dans le fichier B<.xz>. Les lignes B<block> sont affichées après toutes les lignes B<stream> ; les différents types de lignes ne sont pas imbriqués."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Ce type de ligne n'est utilisé que lorsque B<--verbose> a été indiqué deux fois. Cette ligne est affichée après toutes les lignes B<block>. Comme la ligne B<file>, la ligne B<summary> contient l'information globale sur le fichier B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Cette ligne est toujours la toute dernière ligne de la sortie. Elle affiche les comptes et les tailles totaux."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Les colonnes des lignes B<file> :"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Nombre de flux dans le fichier"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Nombre total de blocs dans le ou les flux."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Taille compressée du fichier"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Taille décompressée du fichier"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr ""

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Liste de noms de contrôles d'intégrité séparés par des virgules. Les chaînes suivantes sont utilisées pour les types de vérification connus : B<None>, B<CRC32>, B<CRC64> et B<SHA256>. Pour le types de vérification inconnus, B<Unknown->I<N> est utilisé, où I<N> est un identifiant de vérification sous la forme d'un nombre décimal (un ou deux chiffres)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Taille totale du remplissage du flux dans le fichier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Les colonnes des lignes B<stream> :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Numéro de flux (le premier flux a le numéro 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Nombre de blocs dans le flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Décalage de départ compressé"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Décalage de départ décompressé"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Taille compressée (ne comprend pas le remplissage du flux)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Taille décompressée"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Taux de compression"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Nom de la vérification d'intégrité"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Taille du remplissage de flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Les colonnes des lignes B<block> :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Numéro du flux qui contient ce bloc"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Numéro du bloc relatif au commencement du flux (le premier bloc a pour numéro 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Numéro du bloc relatif au début du fichier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Décalage de départ compressé relatif au début du fichier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Décalage de départ décompressé relatif au début du fichier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Taille compressée totale du bloc (en-têtes inclus)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Si B<--verbose> a été indiqué deux fois, les colonnes additionnelles sont inclues sur les lignes B<block>. Elles ne sont pas affichées avec un seul B<--verbose>, car l'obtention de ces informations nécessite de nombreuses recherches et peut donc être lente :"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Valeur de la vérification d'intégrité en hexadécimal"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Taille d'en-tête de bloc"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Drapeaux du bloc : B<c> indique que la taille compressée est présente, et B<u> indique que la taille décompréssée est présente. Si le drapeau n'est pas indiqué, un tiret (B<->) est affiché à la place pour que la longueur de la chaîne reste fixe. De nouveaux drapeaux pourraient être ajoutés à la fin de la chaîne dans le futur."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Taille des données effectivement compressées dans le bloc (en excluant l'en-tête de bloc, le remplissage de bloc et les champs de vérification)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Quantité de mémoire (en octets) nécessaire pour décompresser ce bloc avec cette version de B<xz>."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Chaîne de filtrage. Remarquez que la plupart des options utilisées au moment de la compression ne peuvent pas être connues, car seules les options nécessaires pour la décompression sont stockées dans les en-têtes B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Les colonnes des lignes B<summary> :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Quantité de mémoire (en octets) nécessaire pour décompresser ce fichier avec cette version de B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> ou B<no> indique si tous les en-têtes de bloc stockent à la fois la taille compressée et la taille décompressée."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Depuis> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Version minimale de B<xz> nécessaire pour décompresser le fichier."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Les colonnes de la ligne B<totals> :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Nombre de flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Nombre de blocs"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Taille compressée"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Taux de compression moyen"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Liste séparée par des virgules des noms de vérification d'intégrité qui étaient présents dans les fichiers"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Taille de remplissage de flux"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Nombre de fichiers. Permet de garder l'ordre des colonnes précédentes comme sur les lignes B<file>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Si B<--verbose> a été indiqué deux fois, des colonnes supplémentaires sont incluses sur la ligne B<totals> :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Quantité maximale de mémoire (en octets) nécessaire pour décompresser les fichiers avec cette version de B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Les versions futures pourront ajouter de nouveaux types de lignes et de nouvelles colonnes pourront être ajoutées aux types de lignes existants, mais les colonnes existantes ne seront pas modifiées."

#. type: SS
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "Filter"
msgid "Filters help"
msgstr "Filtre"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "B<file>"
msgid "I<filter>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "Name of the integrity check"
msgid "Name of the filter"
msgstr "Nom de la vérification d'intégrité"

#. type: TP
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "Supported I<options>:"
msgid "I<option>"
msgstr "I<options> prises en charge :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Information de limite de mémoire"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr ""

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "Dans le futur, la sortie de B<xz --robot --info-memory> pourrait avoir plus de colonnes, mais jamais plus qu'une ligne unique."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Version"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr ""

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Version majeure."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Version mineure. Les numéros pairs sont stables. Les numéros impairs sont des versions alpha ou beta."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Niveau de correctif pour les options stables ou juste un compteur pour les options de développement."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Stabilité. 0 est alpha, 1 est bêta et 2 est stable. I<S> devrait toujours être 2 quand I<YYY> est pair."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> sont identiques sur les deux lignes si B<xz> et liblzma sont issus de la même version d'utilitaires XZ."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Exemples : 4.999.9beta est B<49990091> et 5.0.0 est B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "STATUT DE SORTIE"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Tout est bon."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "Une erreur est survenue."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Quelquechose méritant un avertissement s'est produit, mais aucune erreur véritable n'est survenue."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Les notifications (pas les avertissements ou les erreurs) affichées sur l'erreur standard n'affectent pas le statut de sortie."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "ENVIRONNEMENT"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> analyse les listes d'options séparées par des espaces à partir des variables d'environnement B<XZ_DEFAULTS> et B<XZ_OPT>, dans cet ordre, avant d'analyser les options de la ligne de commandes. Remarquez que seules les options sont analysées depuis l'environnement des variables ; toutes les non-options sont ignorées silencieusement. L'analyse est faite avec B<getopt_long>(3) qui est aussi utilisé pour les arguments de la ligne de commandes."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr ""

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy
#| msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default.  Excluding shell initialization scripts and similar special cases, scripts must never set or unset B<XZ_DEFAULTS>."
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Options par défaut propres à l'utilisateur ou pour tout le système. Elles sont le plus souvent définies dans un script d'initialisation de l'interpréteur pour activer le limiteur d'utilisation de la mémoire de B<xz> par défaut. A part pour les scripts d'initialisation de l'interpréteur ou des cas similaires, les sripts ne doivent jamais définir ou désactiver B<XZ_DEFAULTS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<XZ_OPT=-2v tar caf foo.tar.xz foo>\n"
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "CW<XZ_OPT=-2v tar caf foo.tar.xz foo>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<XZ_OPT=${XZ_OPT-\"-7e\"}\n"
#| "export XZ_OPT>\n"
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"CW<XZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT>\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "Compatibilité des utilitaires LZMA"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "La syntaxe de la ligne de commande de B<xz> est quasimment un sur-ensemble de B<lzma>, B<unlzma> et B<lzcat> comme ils sont trouvés dans les utilitaires LZMA 4.32.x . Dans la pluspart des cas, il est possible de remplacer les outils LZMA par les outils XZ sans casser les scripts existants. Il existe cependant certaines incompatibilités qui peuvent parfois poser des problèmes."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Niveaux de préréglage de la compression"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "La numérotation des préréglages de niveau de compression est différente entre les outils B<xz> et LZMA. La différence la plus importante est la manière dont les tailles de dictionnaire sont affectées aux différents préréglages. La taille de dictionnaire est à peu près égale à celle d'utilisation de la mémoire de la décompression."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Niveau"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "Utilitaires LZMA"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "N/A"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Les différences de tailles des dictionnaires affectent aussi l'utilisation de la mémoire du compresseur, mais il y a quelques autres différences entre les outils LZMA et les outils XZ, qui rendent la différence encore plus grande :"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "Utilitaires LZMA 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Le niveau de préréglage par défaut dans les outils LZMA est B<-7> alors que pour les outils XZ c'est B<-6>, les deux utilisent ainsi un dictionnaire de 8 Mio par défaut."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Fichiers .lzma en flux ou non"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> prend en charge la décompression des fichiers B<.lzma> avec ou sans marqueur de fin de charge utile, mais tous les fichiers B<.lzma> créés par B<xz> utiliseront un marqueur de fin de charge utile et ont la taille non compréssée marquée comme inconnue dans l'en-tête B<.lzma>. Cela peut être un problème dans quelques situations inhabituelles. Par exemple, un décompresseur B<.lzma> dans un périphérique embarqué pourrait ne fonctionner qu'avec des fichiers dont la taille non comprimée est connue. Si vous vous heurtez à ce problème, vous devez utiliser les utilitaires LZMA ou LZMA SDK pour créer des fichiers B<.lzma> avec une taille non compressée connue."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Fichiers .lzma non pris en charge"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "Le format B<.lzma> autorise des valeurs I<lc> jusqu'à 8, et des valeurs I<lp> jusqu'à 4. Les outils LZMA peuvent décompresser des fichiers avec tous les I<lc> et I<lp>, mais créez toujours les fichiers avec B<lc=3> et B<lp=0>. Créer des fichiers avec d'autres valeurs I<lc> et I<lp> est possible avec B<xz> et avec LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "L'implémentation du filtre LZMA1 dans liblzma nécessite que la somme de I<lc> et I<lp> soit inférieure ou égale à 4. Ainsi, les fichiers B<.lzma> qui excèdent cette limitation ne peuvent pas être décompressés avec B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "Les outils LZMA créent seulement des fichiers B<.lzma> qui ont une taille de dictionnaire de 2^I<n> (une puissance de 2) mais acceptent les fichiers avec toutes les tailles de dictionnaire. Libzlma n'accepte que les fichiers B<.lzma> qui ont une taille dictionnaire de 2^I<n> ou 2^I<n> + 2^(I<n>-1). Cela afin de diminuer les faux positifs lors de la détection des fichiers B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Ces limitations ne devraient pas poser problème en pratique, car pratiquement tous les fichiers B<.lzma> ont été compressés avec des réglages que liblzma accepte."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Déchets excédentaires"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Lors de la décompession, l'utilitaire LZMA ignore silencieusement tout ce qui est après le premier flux B<.lzma>. Dans la majorité des situations, c'est un bogue. Cela veut dire aussi que les outils LZMA ne gèrent pas la décompression de fichiers B<.lzma> concaténés."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "S'il reste des données après le premier flux B<.lzma>, B<xz> considère que le fichier est corrompu sauf si B<--single-stream> a été utilisé. Cela peut casser des scripts obscurs qui ont supposé que les déchets de fin de ligne sont ignorés."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "NOTES"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "La sortie compressée peut varier"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "La sortie compressée exacte produite par les même fichiers non compressés en entrée peut varier en fonction des différentes versions de l'utilitaire XZ, même si les options de compression sont identiques. En effet, il est possible d'améliorer l'encodeur (compression plus rapide ou meilleure) sans affecter le format du fichier. La sortie peut même varier entre différentes compilations de la même version d'utilitaire XZ, si des options de construction différentes sont utilisées."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Cela signifie qu'une fois que B<--rsyncable> a été implémenté, les fichiers résultants ne seront pas nécessairement synchronisables avec rsync à moins que les nouveaux et anciens fichiers n'aient été compressés avec la même version de xz. Ce problème peut être résolu si une partie de l'implémentation est gelée pour garantir la stabilité de la sortie rsyncable à travers les versions de xz."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Décompresseurs .xz embarqués"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "Les implémentations de décompresseur embarqué comme XZ Embedded ne gèrent pas nécessairement les fichiers créés avec d'autres types de I<vérification> d'intégrité que B<none> et B<CRC32>. Comme la valeur par défaut est B<--check=crc64>, vous devez utiliser B<--check=none> ou B<--check=crc32> lors de la création de fichiers pour les systèmes embarqués."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "En dehors des systèmes embarqués, tous les décompresseurs de format B<.xz> gèrent tous les types de I<vérification> ou sont au moins capables de décompresser le fichier sans effectuer la vérification d'intégrité si ce type de I<vérification> particulière n'est pas pris en charge."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded prend en charge les filtres BCJ, mais seulement avec le décalage de départ par défaut."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "EXEMPLES"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Bases"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Compresser le fichier I<toto> en I<toto.xz> en utilisant le niveau de compression par défaut (B<-6>) et supprimer I<toto> si la compression réussit :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz foo>\n"
msgid "\\f(CRxz foo\\fR\n"
msgstr "CW<xz toto>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Décompresser I<bidule.xz> en I<bidule> et ne pas supprimer I<bidule.xz> même si la compression réussit :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -dk bar.xz>\n"
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "CW<xz -dk bidule.xz>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<tar cf - baz | xz -4e E<gt> baz.tar.xz>\n"
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "CW<tar cf - truc | xz -4e E<gt> truc.tar.xz>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Un mélange de fichiers compressés et non compressés peuvent être décompressés vers la sortie standard avec une simple commande :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt>\n"
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "CW<xz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt>\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Compression en parallèle de plusieurs fichiers"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "Sur GNU et *BSD, B<find>(1) et B<xargs>(1) peuvent être utilisés pour mettre en parallèle la compression de plusieurs fichiers :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<find . -type f \\e! -name '*.xz' -print0 \\e\n"
#| "    | xargs -0r -P4 -n16 xz -T1>\n"
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"CW<find . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "L'option B<P> passée à B<xargs>(1) fixe le nombre de processus B<xz> en parallèles. La meilleure valeur pour l'option B<n> dépend du nombre de fichiers à compresser. S-il n'y a que quelques fichiers, la valeur sera probablement 1 ; avec des dizaines de milliers de fichiers, 100 ou même plus serait approprié pour réduire le nombre de processus B<xz> que B<xargs>(1) créera éventuellement."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "L'option B<-T1> de B<xz> est là pour le forcer en mode mono-thread, car B<xargs>(1) est utilisé pour contrôler la quantité de mise en parallèle."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Mode robot"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Calculer combien d'octets ont été économisés au total après avoir compressé plusieurs fichiers :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --robot --list *.xz | awk '/^totals/{print $5-$4}'>\n"
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "CW<xz --robot --list *.xz | awk '/^totals/{print $5-$4}'>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Un script peut vouloir savoir qu'il utilise une version suffisamment récente de B<xz>. Le script B<sh>(1) suivant vérifie que le numéro de version de l'outil B<xz> soit au minimum 5.0.0. Cette méthode est compatible avec les vieilles versions bêta, qui ne gèrent pas l'option B<--robot> :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid ""
#| "CW<if ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
#| "        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
#| "    echo \"Your xz is too old.\"\n"
#| "fi\n"
#| "unset XZ_VERSION LIBLZMA_VERSION>\n"
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"CW<if ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Votre version de xz est trop ancienne.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Régler une limite d'utilisation de la mémoire pour la décompression en utilisant B<XZ_OPT>, mais si une limite a déjà été définie, ne pas l'augmenter : "

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "L'utilisation la plus simple des chaînes de filtres personnalisées est la personnalisation d'un préréglage LZMA2. Cela peut être utile, car les préréglages ne couvrent qu'un sous-ensemble des réglages de compression potentiellement utiles."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "Les colonnes CompCPU des tableaux des descriptions des options B<-0> à B<-9> et B<--extreme> sont utiles lors de la personnalisation des préréglages LZMA2. Voici les parties pertinentes recueillies à partir de ces deux tableaux :"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --lzma2=preset=1,dict=32MiB foo.tar>\n"
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "CW<xz --lzma2=preset=1,dict=32MiB toto.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Avec certains fichiers, la commande ci-dessus peut être plus rapide que B<xz-6> tout en compressant bien mieux. Cependant, il faut souligner que seuls certains fichiers bénéficient d'un grand dictionnaire tout en gardant la valeur de CompCPU faible. La siutation la plus évidente où un gros dictionnaire peut baucoup aider, est une archive contenant des fichiers très similaires de quelques megaoctets chacun. La taille de dictionnaire doit être significativement plus grosse que tout fichier individuel pour permettre à LZMA2 de tirer pleinement partie des similarités entre des fichiers consécutifs."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Si une utilisation de la mémoire élevée pour la compression et décompression convient, et que le fichier à compresser a une taille de plusieurs centaines de megaoctets, il peut être utile d'utiliser un plus gros dictionnaire que celui fourni par B<xz-9> (64 Mio) :"

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz -vv --lzma2=dict=192MiB big_foo.tar>\n"
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "CW<xz -vv --lzma2=dict=192MiB gros_toto.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Utiliser B<-vv> (B<--verbose--verbose>) comme dans l'exemple ci-dessus peut être utile pour voir les besoins en mémoire du compresseur et du décompresseur. Rappelez-vous qu'utiliser un dictionnaire plus gros que la taille du fichier non compressé est un gachis de mémoire, donc la commande ci-dessus n'est pas utile pour les petits fichiers."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --check=crc32 --lzma2=preset=6e,dict=64KiB foo>\n"
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "CW<xz --check=crc32 --lzma2=preset=6e,dict=64KiB toto>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --lzma2=preset=6e,pb=0,lc=4 source_code.tar>\n"
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "CW<xz --lzma2=preset=6e,pb=0,lc=4 code_source.tar>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --x86 --lzma2 libfoo.so>\n"
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "CW<xz --x86 --lzma2 libtoto.so>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Notez que l'ordre des options de filtre est significatif. Si B<--x86> est indiqué après B<--lzma2>, B<xz> donnera une erreur, car il ne peut y avoir aucun filtre après LZMA2, et aussi parce que le filtre BCJ x86 ne peut pas être utilisé comme dernier filtre dans la chaîne."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Le filtre Delta associé à LZMA2 peut donner de bons résultats avec les images bitmap. Cela devrait habituellement battre PNG, qui a quelques filtres avancés supplémentaires qu'un simple delta, mais qui utilise Deflate pour la compression effective."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
#, fuzzy, no-wrap
#| msgid "CW<xz --delta=dist=3 --lzma2=pb=0 foo.tiff>\n"
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "CW<xz --delta=dist=3 --lzma2=pb=0 toto.tiff>\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr ""

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "VOIR AUSSI"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr ""

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr ""

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec - Small .xz et .lzma decompresseurs"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<option...>] [I<fichier...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<option...>] [I<fichier...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> est un outil uniquement de décompression, basé sur liblzma pour les fichiers B<.xz> (et seulement B<.xz>). B<xzdec> est destiné à remplacer B<xz>(1) dans les situations les plus courantes où un script a été écrit pour utiliser B<xz --decompress --stdout> (et possiblement quelques autres options courantes) pour décompresser des fichiers B<.xz>. B<lzmadec> est identique à B<xzdec>, sauf que B<lzmadec> prend en charge les fichiers B<.lzma> au lieu des fichiers B<.xz>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Pour réduire la taille de l'exécutable, B<xzdec> ne prend en charge ni le multithreading ni la localisation et ne lit pas les options des variables d'environnement B<XZ_DEFAULTS> et B<XZ_OPT>. B<xzdec> ne gère pas l'affichage d'information sur la progression du traitement : envoyer B<SIGINFO> à B<xzdec> ne fait rien, mais envoyer B<SIGUSR1> termine le processus au lieu d'afficher de l'information sur la progression."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Ignoré pour la compatibilité avec B<xz>(1), B<xzdec> ne gère que la décompression."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Ignoré pour la compatibilité avec B<xz>(1), B<xzdec> ne crée ni ne supprime jamais aucun fichier."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Ignoré pour la compatibilité avec B<xz>(1), B<xzdec> écrit toujours les données décompressées sur la sortie standard."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Spécifier cela une fois ne fait rien, car B<xzdec> n'affiche jamais aucun avertissement ou notification. Spécifier cela deux fois pour supprimer les erreurs."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Ignoré pour la compatibilité avec B<xz>(1), B<xzdec> n'utilise jamais le satut de sortie 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Afficher un message d'aide et quitter."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Afficher le numéro de version de B<xzdec> et liblzma."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Tout s'est bien passé."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "A la différence de B<xz>(1),B<xzdec> n'a pas de messages d'avertissement, et donc le statut de sortie 2 n'est pas utilisé par B<xzdec>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Utilisez B<xz>(1) au lieu de B<xzdec> ou B<lzmadec> pour un usage normal de tous les jours. B<xzdec> ou B<lzmadec> ne sont utiles que pour les situations où il est important d'avoir un plus petit décompresseur que le B<xz>(1) complet."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> et B<lzmadec> ne sont en fait pas vraiment si petits. La taille peut être encore réduite en abandonnant des fonctionnalités de liblzma au moment de la compilation, mais cela ne devrait pas être fait pour des exécutables distribués sur des systèmes d'exploitation classique non embarqués. Si vous avez besoin d'un décompresseur vraiment petit, pensez à utiliser XZ Embedded."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30-06-2013"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo - Afficher l'information stockée dans l'en-tête du fichier .lzma"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<fichier...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> affiche l'information stockée dans l'en-tête du fichier B<.lzma>. Il lit les 13 premiers octets du I<fichier> indiqué, décode l'en-tête, et l'écrit sur la sortie standard dans un format lisible par un humain. Si aucun I<fichier> n'est spécifié ou si I<fichier> est B<->, l'entrée standard est lue."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "Habituellement l'information la plus interressante est la taille non compressée et la taille du dictionnaire. La taille ne peut être affichée que si le fichier est dans la variante du format B<.lzma> qui n'est pas en flux. La quantité de mémoire requise pour décompresser le fichier est de quelques douzaines de kilooctets en plus de la taille du dictionnaire."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> est inclus dans les utilitaires XZ essentiellement pour des besoins de rétrocompatibilité avec les utilitaires LZMA."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "BOGUES"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> utilise B<MB> alors que le suffixe correct devrait être B<MiB>(2^20 octets). Cela pour garder la sortie compatible avec les utilitaires LZMA."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, fuzzy, no-wrap
#| msgid "2013-06-30"
msgid "2025-03-06"
msgstr "30-06-2013"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff - Comparer des fichiers compressés."

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<xz> [I<option...>] [I<file...>]"
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xz> [I<option...>] [I<fichier...>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "La commande nommée B<lzless> est fournie pour la rétrocompatibilité avec les utilitaires LZMA."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzdiff.1
#, fuzzy
#| msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<xz> [I<option...>] [I<file...>]"
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xz> [I<option...>] [I<fichier...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-v>, B<--verbose>"
msgid "B<-r>, B<--recursive>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-f>, B<--force>"
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-V>, B<--version>"
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-l>, B<--list>"
msgid "B<-Z>, B<--null>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<-l>, B<--list>"
msgid "B<-z>, B<--null-data>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--include=>I<glob>"
msgstr "B<--files>[B<=>I<fichier>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<--files>[B<=>I<file>]"
msgid "B<--exclude-from=>I<file>"
msgstr "B<--files>[B<=>I<fichier>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "La commande nommée B<lzless> est fournie pour la rétrocompatibilité avec les utilitaires LZMA."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr ""

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr ""

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzgrep.1
#, fuzzy
#| msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless - Voir le contenu des fichiers (texte) compressés xz ou lzma"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<fichier>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
#, fuzzy
#| msgid "B<lzless> [I<file>...]"
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<fichier>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> utilise B<less>(1) pour afficher sa sortie. Contrairement à B<xzmore>, son choix d'afficheur ne peut pas être modifié en indiquant une variable d'environnement. Les commandes sont basées sur B<more>(1) et B<vi>(1) et permettent des déplacements en avant et en arrière et des recherches. Voir le manuel de B<less>(1) pour plus d'information."

#. type: Plain text
#: ../src/scripts/xzless.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "La commande nommée B<lzless> est fournie pour la rétrocompatibilité avec les utilitaires LZMA."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Une liste de caractères spéciaux pour l'interpréteur. Définis par B<xzless> à moins qu'ils ne l'aient déjà été dans l'environnement."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Définir en ligne de commande le décompresseur B<xz>(1) à invoquer pour préparer les fichiers en entrée pour B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore - Voir le contenu des fichiers (texte) compressés xz ou lzma"

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "B<xzless> [I<file>...]"
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzless> [I<fichier>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "B<lzless> [I<file>...]"
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<fichier>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
#, fuzzy
#| msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils."
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "La commande nommée B<lzless> est fournie pour la rétrocompatibilité avec les utilitaires LZMA."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr ""

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
