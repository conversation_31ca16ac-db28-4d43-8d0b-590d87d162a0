# SPDX-License-Identifier: 0BSD
#
# Ukrainian translation for xz-man.
# This file is published under the BSD Zero Clause License.
# Copyright (C) The XZ Utils authors and contributors
#
# <PERSON> <<EMAIL>>, 2019, 2022, 2023, 2024, 2025.
msgid ""
msgstr ""
"Project-Id-Version: xz-man-5.8.0-pre1\n"
"POT-Creation-Date: 2025-03-08 14:50+0200\n"
"PO-Revision-Date: 2025-03-09 20:04+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian <<EMAIL>>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Lokalize 23.04.3\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr "8 березня 2025 року"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "XZ Utils"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "НАЗВА"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat — стискання та розпаковування файлів .xz і .lzma"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "КОРОТКИЙ ОПИС"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<параметр...>] [I<файл...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "СКОРОЧЕННЯ КОМАНД"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> є рівноцінним до B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> є рівноцінним до B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> є рівноцінним до B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> є рівноцінним до B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> є рівноцінним до B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "При написанні скриптів, де потрібно розпаковувати файли, рекомендуємо завжди використовувати B<xz> із відповідними аргументами (B<xz -d> або B<xz -dc>), замість B<unxz> і B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "ОПИС"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> інструмент загального призначення для стискання даних із синтаксисом командного рядка, подібним для B<gzip>(1) і B<bzip2>(1). Власним форматом файлів є B<.xz>, але передбачено підтримку застарілого формату B<.lzma>, який було використано у LZMA Utils, та необроблених потоків стиснених даних без заголовків формату контейнера. Крім того, передбачено підтримку розпаковування формату B<.lz>, який використано у B<lzip>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> стискає або розпаковує кожен I<файл> відповідно до вибраного режиму дій. Якщо I<файли> не задано або якщо I<файлом> є B<->, B<xz> читатиме дані зі стандартного джерела вхідних даних і записуватиме оброблені дані до стандартного виведення. B<xz> відмовить (покаже повідомлення про помилку і пропустить I<файл>) у записів стиснених даних до стандартного виведення, якщо це термінал. Так само, B<xz> відмовить у читанні стиснених даних зі стандартного джерела вхідних даних, якщо це термінал."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "Якщо не вказано B<--stdout>, I<файли>, відмінні від B<->, буде записано до нового файла, чию назву буде визначено з назви початкового I<файла>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "При стисканні суфікс формату файла призначення (B<.xz> або B<.lzma>) буде дописано до назви початкового файла для отримання назви файла призначення."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "При розпаковуванні суфікс B<.xz>, B<.lzma> або B<.lz> буде вилучено з назви файла для отримання назви файла призначення. Крім того, B<xz> розпізнає суфікси B<.txz> і B<.tlz> і замінює їх на суфікс B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Якщо файл призначення вже існує, буде показано повідомлення про помилку, а I<файл> буде пропущено."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "Окрім випадку запису до стандартного виведення, B<xz> покаже попередження і пропустить обробку I<файла>, якщо буде виконано будь-яку з таких умов:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<Файл> не є звичайним файлом. Програма не переходитиме за символічними посиланнями, а отже, не вважатиме їх звичайними файлами."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "На I<файл> існує декілька жорстких посилань."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "Для I<файла> встановлено setuid, setgid або «липкий» біт."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "Режим дій встановлено у значення «стискання», і I<файл> вже має суфікс назви формату файла призначення (B<.xz> або B<.txz> при стисканні до формату B<.xz>, і B<.lzma> або B<.tlz> при стисканні до формату B<.lzma>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "Режим дій встановлено у значення «розпаковування», і I<файл> не має суфікса назви жодного з підтримуваних форматів (B<.xz>, B<.txz>, B<.lzma>, B<.tlz> або B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Після успішного стискання або розпаковування I<файла>, B<xz> копіює дані щодо власника, групи, прав доступу, часу доступу та моменту внесення змін з початкового I<файла> до файла призначення. Якщо копіювання даних щодо групи зазнає невдачі, права доступу буде змінено так, що файл призначення стане недоступним для користувачів, які не мають права доступу до початкового I<файла>. В B<xz> ще не передбачено підтримки копіювання інших метаданих, зокрема списків керування доступом або розширених атрибутів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Щойно файл призначення буде успішно закрито, початковий I<файл> буде вилучено, якщо не вказано параметра B<--keep>. Початковий I<файл> ніколи не буде вилучено, якщо виведені дані буде записано до стандартного виведення або якщо станеться помилка."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "Надсилання B<SIGINFO> або B<SIGUSR1> до процесу B<xz> призводить до виведення даних щодо поступу до стандартного виведення помилок. Це має лише обмежене використання, оскільки якщо стандартним виведенням помилок є термінал, використання B<--verbose> призведе до показу автоматично оновлюваного індикатора поступу."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Використання пам'яті"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "Використання B<xz> пам'яті може бути різним: від декількох сотень кілобайтів до декількох гігабайтів, залежно від параметрів стискання. Параметри, які використано при стисканні файла, визначають вимоги до об'єму пам'яті при розпакуванні. Типово, засобу розпаковування потрібно від 5\\ % до 20\\ % об'єму пам'яті, якого засіб стискання потребує при створенні файла. Наприклад, розпаковування файла, який створено з використанням B<xz -9>, у поточній версії потребує 65\\ МіБ пам'яті. Втім, можливе створення файлів B<.xz>, які потребуватимуть для розпаковування декількох гігабайтів пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "Ймовірність високого рівня використання пам'яті може бути особливо дошкульною для користувачів застарілих комп'ютерів. Щоб запобігти прикрим несподіванкам, у B<xz> передбачено вбудований обмежувач пам'яті, який типово вимкнено. Хоча у деяких операційних системах передбачено спосіб обмежити використання пам'яті процесами, сподівання на його ефективність не є аж надто гнучким (наприклад, використання B<ulimit>(1) для обмеження віртуальної пам'яті призводить до викривлення даних B<mmap>(2))."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "Обмежувач пам'яті можна увімкнути за допомогою параметра командного рядка B<--memlimit=>I<обмеження>. Часто, зручніше увімкнути обмежувач на типовому рівні, встановивши значення для змінної середовища B<XZ_DEFAULTS>, наприклад, B<XZ_DEFAULTS=--memlimit=150MiB>. Можна встановити обмеження окремо для стискання і розпакування за допомогою B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<обмеження>. Використання цих двох параметрів поза B<XZ_DEFAULTS> не таке вже і корисне, оскільки одноразовий запуск B<xz> не може одночасно призводити до стискання та розпаковування, а набрати у командному рядку B<--memlimit=>I<обмеження> (або B<-M> I<обмеження>) набагато швидше."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Якщо під час розпаковування вказане обмеження буде перевищено, B<xz> покаже повідомлення про помилку, а розпаковування файла зазнає невдачі. Якщо обмеження буде перевищено при стисканні, B<xz> спробує масштабувати параметри так, щоб не перевищувати обмеження (окрім випадків використання B<--format=raw> або B<--no-adjust>). Отже, дію буде виконано, якщо обмеження не є надто жорстким. Масштабування параметрів буде виконано кроками, які не збігаються із рівнями шаблонів стискання. Наприклад, якщо обмеження лише трохи не вкладається у об'єм потрібний для B<xz -9>, параметри буде змінено лише трохи, не до рівня B<xz -8>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Поєднання і заповнення з файлами .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "Можна поєднати файли B<.xz> без додаткової обробки. B<xz> розпакує такі файли так, наче вони є єдиним файлом B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "Можна додати доповнення між з'єднаними частинами або після останньої частини. Доповнення має складатися із нульових байтів і мати розмір, який є кратним до чотирьох байтів. Це може бути корисним, наприклад, якщо файл B<.xz> зберігається на носії даних, де розміри файла вимірюються у 512-байтових блоках."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Поєднання та заповнення не можна використовувати для файлів B<.lzma> або потоків необроблених даних."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "ПАРАМЕТРИ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Цілочисельні суфікси і спеціальні значення"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "У більшості місць, де потрібен цілочисельний аргумент, передбачено підтримку необов'язкового суфікса для простого визначення великих цілих чисел. Між цілим числом і суфіксом не повинно бути пробілів."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Помножити ціле число на 1024 (2^10). Синонімами B<KiB> є B<Ki>, B<k>, B<kB>, B<K> та B<KB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Помножити ціле число на 1048576 (2^20). Синонімами B<MiB> є B, B<Mi>, B<m>, B<M> та B<MB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Помножити ціле число на 1073741824 (2^30). Синонімами B<GiB> є B, B<Gi>, B<g>, B<G> та B<GB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "Можна скористатися особливим значенням B<max> для позначення максимального цілого значення, підтримку якого передбачено для параметра."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Режим операції"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Якщо вказано декілька параметрів режиму дій, буде використано лише останній з них."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Стиснути. Це типовий режим дій, якщо не вказано параметр режиму дій, а назва команди неявним чином не визначає іншого режиму дій (наприклад, B<unxz> неявно визначає B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Після успішного пакування початковий файл буде вилучено, якщо виведення не відбувається до стандартного виведення або не вказано параметра B<--keep>."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Розпакувати. Після успішного розпаковування початковий файл буде вилучено, якщо виведення не відбувається до стандартного виведення або не вказано параметра B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Перевірити цілісність стиснених файлів I<файли>. Цей параметр еквівалентний до B<--decompress --stdout>, але розпаковані дані буде відкинуто, замість запису до стандартного виведення. Жодних файлів не буде створено або вилучено."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Вивести відомості щодо стиснених файлів I<файли>. Розпакування даних не виконуватиметься, жодних файлів не буде створено або вилучено. У режимі списку програма не може читати дані зі стандартного введення або з інших джерел, де неможливе позиціювання."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "У типовому списку буде показано базові відомості щодо файлів I<файли>, по одному файлу на рядок. Щоб отримати докладніші відомості, скористайтеся параметром B<--verbose>. Щоб розширити спектр відомостей, скористайтеся параметром B<--verbose> двічі, але зауважте, що це може призвести до значного уповільнення роботи, оскільки отримання додаткових відомостей потребує великої кількості позиціювань. Ширина області докладного виведення даних перевищує 80 символів, тому передавання конвеєром виведених даних, наприклад, до B<less\\ -S>, може бути зручним способом перегляду даних, якщо термінал недостатньо широкий."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "Виведені дані залежать від версії B<xz> та використаної локалі. Для отримання даних, які будуть придатні до обробки комп'ютером, слід скористатися параметрами B<--robot --list>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Модифікатори режиму роботи"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Не вилучати вхідні файли."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Починаючи з версії B<xz> 5.2.6, використання цього параметра також наказує B<xz> виконувати стискання або розпаковування, навіть якщо вхідними даними є символічне посилання на звичайний файл, файл, який має декілька жорстких посилань, або файл, для якого встановлено  setuid, setgid або липкий біт. setuid, setgid та липкий біт не буде скопійовано до файла-результату. У попередніх версіях, ці дії виконувалися, лише якщо було використано параметр B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Результатів використання цього параметра буде декілька:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Якщо файл-результат вже існує, вилучити його до стискання або розпаковування."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Виконувати стискання або розпаковування, навіть якщо вхідними даними є символічне посилання на звичайний файл, файл, який має декілька жорстких посилань, або файл, для якого встановлено  setuid, setgid або липкий біт setuid, setgid та липкий біт не буде скопійовано до файла-результату."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Якщо використано разом із B<--decompress>, B<--stdout>, і B<xz> не зможе розпізнати тип початкового файла, копіювати початковий файл без змін до стандартного виведення. Це надає змогу користуватися B<xzcat> B<--force> подібно до B<cat>(1) для файлів, які не було стиснено за допомогою B<xz>. Зауважте, що у майбутньому у B<xz> може бути реалізовано підтримку нових форматів стиснених файлів, замість копіювання їх без змін до стандартного виведення. Можна скористатися B<--format=>I<формат> для обмеження стискання у B<xz> єдиним форматом файлів."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Записати стиснені або розпаковані дані до стандартного виведення, а не до файла. Неявним чином встановлює B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Розпакувати лише перший потік даних B<.xz> і без повідомлень проігнорувати решту вхідних даних, які слідують за цим потоком. Зазвичай, такі зайві дані наприкінці файла призводять до показу B<xz> повідомлення про помилку."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> ніколи не виконуватиме спроби видобути декілька потоків даних з файлів B<.lzma> або необроблених потоків даних, але використання цього параметра все одно наказує B<xz> ігнорувати можливі кінцеві дані після файла B<.lzma> або необробленого потоку даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Цей параметр нічого не змінює, якщо режимом дій не є B<--decompress> або B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr "З B<xz> 5.7.1alpha, B<--single-stream> неявно визначає B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Вимкнути створення розріджених файлів. Типово, якщо видобування виконується до звичайного файла, B<xz> намагається створити розріджений файл, якщо розпаковані дані містять довгі послідовності двійкових нулів. Це також працює, коли виконується запис до стандартного виведення, доки стандартне виведення з'єднано зі звичайним файлом і виконуються певні додаткові умови, які убезпечують роботу. Створення розріджених файлів може заощадити місце на диску і пришвидшити розпаковування шляхом зменшення кількості дій введення та виведення даних на диску."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "При стисканні використати суфікс I<.suf> для файлів призначення, замість суфікса B<.xz> або B<.lzma>. Якщо записування виконується не до стандартного виведення і початковий файл вже має суфікс назви I<.suf>, буде показано попередження, а файл буде пропущено під час обробки."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "При розпаковуванні розпізнавати файли із суфіксом назви I<.suf>, окрім файлів із суфіксами назв B<.xz>, B<.txz>, B<.lzma>, B<.tlz> або B<.lz>. Якщо початковий файл мав суфікс назви I<.suf>, для отримання назви файла призначення цей суфікс буде вилучено."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "При стисканні або розпакуванні необроблених потоків даних (B<--format=raw>) суфікс слід вказувати завжди, якщо запис не виконується до стандартного виведення, оскільки типового суфікса назви для необроблених потоків даних не передбачено."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<файл>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Прочитати назви файлів для обробки з файла I<файл>; якщо I<file> не вказано, назви файлів буде прочитано зі стандартного потоку вхідних даних. Назви файлів має бути відокремлено символом нового рядка. Символ дефіса (B<->) буде оброблено як звичайну назву файла; він не позначатиме стандартного джерела вхідних даних. Якщо також буде вказано назви файлів у аргументах рядка команди, файли з цими назвами буде оброблено до обробки файлів, назви яких було прочитано з файла I<файл>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<файл>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Те саме, що і B<--files>[B<=>I<файл>], але файли у списку має бути відокремлено нульовим символом."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Параметри базового формату файлів та стискання"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<format>, B<--format=>I<формат>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Вказати файл I<формат> для стискання або розпакування:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Типовий варіант. При стисканні B<auto> є еквівалентом B<xz>. При розпакуванні формат файла вхідних даних буде виявлено автоматично. Зауважте, що автоматичне виявлення необроблених потоків даних (створених за допомогою B<--format=raw>) неможливе."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Стиснути до формату B<.xz> або приймати лише файли B<.xz> при розпаковуванні."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Стиснути дані до застарілого формату файлів B<.lzma> або приймати лише файли B<.lzma> при розпаковуванні. Альтернативну назву B<alone> може бути використано для зворотної сумісності із LZMA Utils."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Приймати лише файли B<.lz> при розпакуванні. Підтримки стискання не передбачено."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "Передбачено підтримку версії формату B<.lz> 0 та нерозширеної версії 1. Файли версії 0 було створено B<lzip> 1.3 та старішими версіями. Такі файли не є поширеними, але їх можна знайти у файлових архівах, оскільки певну незначну кількість пакунків із початковим кодом було випущено у цьому форматі. Також можуть існувати особисті файли у цьому форматі. Підтримку розпаковування для формату версії 0 було вилучено у B<lzip> 1.18."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip> 1.4 і пізніші версії створюють файли у форматі версії 1. Розширення синхронізації позначки витирання до формату версії 1 було додано у B<lzip> 1.6. Це розширення використовують не часто, його підтримки у B<xz> не передбачено (програма повідомлятиме про пошкоджені вхідні дані)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Стиснути або розпакувати потік необроблених даних (лез заголовків). Цей параметр призначено лише для досвідчених користувачів. Для розпаковування необроблених потоків даних слід користуватися параметром  B<--format=raw> і явно вказати ланцюжок фільтрування, який за звичайних умов мало б бути збережено у заголовках контейнера."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<перевірка>, B<--check=>I<перевірка>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Вказати тип перевірки цілісності. Контрольну суму буде обчислено на основі нестиснених даних і збережено у файлі B<.xz>. Цей параметр працюватиме, лише якщо дані стиснено до файла у форматі B<.xz>; для формату файлів B<.lzma> підтримки перевірки цілісності не передбачено. Перевірку контрольної суми (якщо така є) буде виконано під час розпаковування файла B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Підтримувані типи I<перевірок>:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Не обчислювати контрольну суму взагалі. Зазвичай, не варто цього робити. Цим варіантом слід скористатися, якщо цілісність даних буде перевірено в інший спосіб."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Обчислити CRC32 за допомогою полінома з IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Обчислити CRC64 за допомогою полінома з ECMA-182. Це типовий варіант, оскільки він дещо кращий за CRC32 при виявленні пошкоджених файлів, а різниця у швидкості є незрачною."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Обчислити SHA-256. Цей варіант дещо повільніший за CRC32 і CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "Цілісність заголовків B<.xz> завжди перевіряють за допомогою CRC32. Таку перевірку не можна змінити або скасувати."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Не перевіряти цілісність стиснених даних при розпаковуванні. Значення CRC32 у заголовках B<.xz> буде у звичайний спосіб перевірено попри цей параметр."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Не користуйтеся цим параметром, якщо ви не усвідомлюєте наслідків ваших дій.> Можливі причини скористатися цим параметром:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Спроба отримання даних з пошкодженого файла .xz."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Пришвидшення розпакування. Це, здебільшого, стосується SHA-256 або файлів із надзвичайно високим рівнем пакування. Не рекомендуємо користуватися цим параметром з цією метою, якщо цілісність файлів не буде перевірено у якийсь інший спосіб."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Вибрати рівень стискання. Типовим є B<-6>. Якщо буде вказано декілька рівнів стискання, програма використає останній вказаний. Якщо вже було вказано нетиповий ланцюжок фільтрів, встановлення рівня стискання призведе до нехтування цим нетиповим ланцюжком фільтрів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Різниця між рівнями є суттєвішою, ніж у B<gzip>(1) і B<bzip2>(1). Вибрані параметри стискання визначають вимоги до пам'яті під час розпаковування, отже використання надто високого рівня стискання може призвести до проблем під час розпаковування файла на застарілих комп'ютерах із невеликим обсягом оперативної пам'яті. Зокрема, B<не варто використовувати -9 для усього>, як це часто буває для B<gzip>(1) і B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Це дещо швидші набори налаштувань. B<-0> іноді є швидшим за B<gzip -9>, забезпечуючи набагато більший коефіцієнт стискання. Вищі рівні часто мають швидкість, яку можна порівняти з B<bzip2>(1) із подібним або кращим коефіцієнтом стискання, хоча результати значно залежать від типу даних, які стискають."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Стискання від доброго до дуже доброго рівня із одночасним підтриманням помірного рівня споживання пам'яті засобом розпаковування, навіть для застарілих системи. Типовим є значення B<-6>, яке є добрим варіантом для поширення файлів, які мають бути придатними до розпаковування навіть у системах із лише 16\\ МіБ оперативної пам'яті. (Також можна розглянути варіанти B<-5e> і B<-6e>. Див. B<--extreme>.)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Ці варіанти подібні до B<-6>, але із вищими вимогами щодо пам'яті для стискання і розпаковування. Можуть бути корисними лише для стискання файлів з розміром, що перевищує 8\\ МіБ, 16\\ МіБ та 32\\ МіБ, відповідно."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "На однаковому обладнанні швидкість розпакування є приблизно сталою кількістю байтів стиснених даних за секунду. Іншими словами, чим кращим є стискання, тим швидшим буде, зазвичай, розпаковування. Це також означає, що об'єм розпакованих виведених даних, які видає програма за секунду, може коливатися у широкому діапазоні."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "У наведеній нижче таблиці підсумовано можливості шаблонів:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Шаблон"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "DictSize"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "CompCPU"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "CompMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "DecMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 КіБ"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Описи стовпчиків:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "DictSize є розміром словника LZMA2. Використання словника, розмір якого перевищує розмір нестисненого файла, — проста витрата пам'яті. Ось чому не варто використовувати шаблони B<-7> ... B<-9>, якщо у них немає реальної потреби. Для B<-6> та нижчих рівнів об'єм витраченої пам'яті, зазвичай, такий низький, що цей фактор ні на що не впливає."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "CompCPU є спрощеним представленням параметрів LZMA2, які впливають на швидкість стискання. Розмір словника також впливає на швидкість, тому, хоча значення CompCPU є однаковим для рівнів B<-6> ... B<-9>, обробка на вищих рівнях все одно є трошки повільнішою. Що отримати повільніше і, ймовірно, краще стискання, див. B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr "CompMem містить вимоги до пам'яті засобу стискання у однопотоковому режимі. Значення можуть бути дещо різними для різних версій B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "У DecMem містяться вимоги до пам'яті при розпаковуванні. Тобто параметри засобу стискання визначають вимоги до пам'яті при розпаковуванні. Точний об'єм пам'яті, яка потрібна для розпаковування, дещо перевищує розмір словника LZMA2, але значення у таблиці було округлено до наступного цілого значення МіБ."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr "Вимоги до пам'яті у багатопотоковому режимі є значно вищими, ніж у однопотоковому. З типовим значенням B<--block-size> для кожного потоку треба 3*3*DictSize плюс CompMem або DecMem. Наприклад, для чотирьох потоків з шаблоном B<-6> потрібно 660\\(en670\\ МіБ пам'яті."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Використати повільніший варіант вибраного рівня стискання (B<-0> ... B<-9>) у сподіванні отримати трохи кращий коефіцієнт стискання, але, якщо не поталанить, можна його і погіршити. Не впливає на використання пам'яті при розпаковуванні, але використання пам'яті при стисканні дещо збільшиться на рівнях B<-0> ... B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Оскільки існує два набори налаштувань із розмірами словників 4\\ МіБ та 8\\ МіБ, у наборах B<-3e> і B<-5e> використано трошки швидші параметри (нижче CompCPU), ніж у наборах B<-4e> і B<-6e>, відповідно. Тому двох однакових наборів у списку немає."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "Наприклад, передбачено загалом чотири набори налаштувань із використанням словника у 8\\ МіБ, порядок яких від найшвидшого до найповільнішого є таким: B<-5>, B<-6>, B<-5e> і B<-6e>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Це дещо оманливі альтернативні варіанти для B<-0> і B<-9>, відповідно. Реалізовано лише для забезпечення зворотної сумісності із LZMA Utils. Намагайтеся не користуватися цими варіантами параметрів."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<розмір>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "При стисканні до формату B<.xz> поділити вхідні дані на блоки у I<розмір> байтів. Ці блоки буде стиснуто незалежно один від одного, що допоможе у багатопотоковій обробці і зробить можливим обмежене розпакування для доступу до будь-яких даних. Цим параметром слід типово користуватися для перевизначення типового розміру блоку у багатопотоковому режимі обробки, але цим параметром можна також скористатися в однопотоковому режимі обробки."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr "У багатопотоковому режимі для кожного потоку буде отримано для буферів вхідних і вихідних даних майже утричі більше за I<розмір> байтів. Типовий I<розмір> утричі більший за розмір словника LZMA2 або дорівнює 1 МіБ, буде вибрано більше значення. Типовим добрим значенням буде значення, яке у 2\\(en4 рази перевищує розмір словника LZMA2 або дорівнює принаймні 1 МіБ. Використання значення I<розмір>, яке є меншим за розмір словника LZMA2, має наслідком марну витрату оперативної пам'яті, оскільки його використання призводить до того, що буфер словника LZMA2 ніколи не буде використано повністю. У багатопотоковому режимі розміри блоків зберігатимуться у заголовках блоків. Ці дані потрібні для багатопотокового розпаковування."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr "У однопотоковому режимі поділ на блоки типово не виконуватиметься. Встановлення значення для цього параметра не впливатиме на використання пам'яті. У заголовках блоків не зберігатимуться дані щодо розміру, отже файли, які створено в однопотоковому режимі не будуть ідентичними до файлів, які створено у багатопотоковому режимі. Те, що у заголовках блоків не зберігатимуться дані щодо розміру також означає, що B<xz> не зможе розпаковувати такі файли у багатопотоковому режимі."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-list=>I<items>"
msgstr "B<--block-list=>I<записи>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr "При стисканні у форматі B<.xz> починати новий блок із необов'язковим ланцюжком фільтрів після вказаної кількості інтервалів нестиснених даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr "I<записи> є списком відокремлених комами значень. Кожен запис складається з необов'язкового номера ланцюжка фільтрів від 0 до 9, після якого йде двокрапка (B<:>) і необхідний розмір нестиснутих даних. Пропущення запису (дві або більше послідовних ком) є скороченим варіантом визначення використання розміру та фільтрів попереднього запису."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr "Якщо файл вхідних даних є більшим за розміром за суму розмірів I<записів>, останнє значення у I<розмірах> буде повторено до кінця файла. Особливе значення B<0> може бути використано як останній розмір, щоб позначити, що решту файла має бути закодовано як єдиний блок."

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr "Альтернативний ланцюжок фільтрів для кожного блоку можна вказати в поєднанні з параметрами B<--filters1=>I<фільтри> \\&...\\& B<--filters9=>I<фільтри>. Ці параметри визначають ланцюжки фільтрів з ідентифікатором у діапазоні 1\\(en9. Ланцюжок фільтрів 0 можна використовувати для посилання на типовий ланцюжок фільтрів — це те саме, що не вказувати ланцюжок фільтрів. Ідентифікатор ланцюжка фільтрів можна використовувати перед нестисненим розміром, після якого йде двокрапка (B<:>). Наприклад, якщо вказати B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB>, блоки будуть створені так:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr "Ланцюжок фільтрів задано B<--filters1> із вхідними даними у 2 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr "Ланцюжок фільтрів задано B<--filters3> із вхідними даними у 2 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr "Ланцюжок фільтрів задано B<--filters2> із вхідними даними у 4 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr "Типовий ланцюжок даних і вхідні дані у 2 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr "Типовий ланцюжок фільтрів та вхідні дані у 4 МіБ для кожного блоку до кінця вхідних даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr "Якщо вказати розмір, який перевищує розмір блоку кодувальника (або типове значення у режимі із потоками обробки, або значення, яке встановлено за допомогою B<--block-size=>I<розмір>), засіб кодування створить додаткові блоки, зберігаючи межі, які вказано у I<записах>. Наприклад, якщо вказати B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB>, а файл вхідних даних має розмір 80 МіБ, буде отримано такі 11 блоків: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10 і 1 МіБ."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "У багатопотоковому режимі розмір блоків буде збережено у заголовках блоків. Програма не зберігатиме ці дані у однопотоковому режимі, отже закодований результат не буде ідентичним до отриманого у багатопотоковому режимі."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<час_очікування>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "При стискання, якщо з моменту попереднього витирання мине понад I<час_очікування> мілісекунд (додатне ціле значення) і читання додаткових даних буде заблоковано, усі вхідні дані у черзі обробки буде витерто з кодувальника і зроблено доступним у потоці вихідних даних. Це може бути корисним, якщо B<xz> використовують для стискання даних, які передають потоком мережею. Невеликі значення аргументу I<час_очікування> зроблять дані доступними на боці отримання із малою затримкою, а великі значення аргумент I<час_очікування> уможливлять кращий коефіцієнт стискання."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Типово, цю можливість вимкнено. Якщо цей параметр вказано декілька разів, буде використано лише останнє вказане значення. Особливим значенням аргументу I<час_очікування>, рівним B<0>, можна скористатися для вимикання цієї можливості явним чином."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Ця можливість недоступна у системах, які не є системами POSIX."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Ця можливість усе ще є експериментальною.> У поточній версії, B<xz> не може розпаковувати потік даних у режимі реального часу через те, у який спосіб B<xz> виконує буферизацію."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sync>"
msgstr "B<--no-sync>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr "Не синхронізувати файл призначення та його каталог із пристроєм зберігання даних до вилучення початкового файла. Це може підвищити швидкодію, якщо виконується стискання або розпаковування багатьох малих файлів. Втім, якщо система аварійно завершує роботу невдовзі після вилучення, можлива ситуація, коли файл призначення не буде записано на пристрій зберігання даних, а дію з вилучення буде записано. У цьому випадку буде знищено дані як початкового файла, так і файла призначення."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr "Цей параметр матиме хоч який вплив, лише якщо B<xz> вилучає початковий файл. В інших випадках синхронізація не виконується."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr "Синхронізацію і B<--no-sync> було додано у версії B<xz> 5.7.1alpha."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<обмеження>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Встановити обмеження на використання пам'яті при стисканні. Якщо цей параметр вказано декілька разів, враховано буде лише останнє вказане значення."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Якщо параметри стискання перевищують I<обмеження>, B<xz> спробує скоригувати параметри так, щоб обмеження не було перевищено, і покаже повідомлення про те, що було виконано автоматичне коригування. Коригування буде виконано у такому порядку: зменшення кількості потоків обробки, перемикання у однопотоковий режим, якщо хоч в одному потоці багатопотокового режиму буде перевищено I<обмеження>, і нарешті, зменшення розміру словника LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "При стисканні з використанням B<--format=raw>, або якщо було вказано B<--no-adjust>, може бути зменшена лише кількість потоків обробки, оскільки це може бути зроблено без впливу на стиснені виведені дані."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Якщо I<обмеження> не може бути виконано за допомогою коригувань, які описано вище, буде показано повідомлення про помилку, а B<xz> завершить роботу зі станом виходу 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "Аргумент I<обмеження> можна вказати у декілька способів:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "Значенням I<обмеження> може бути додатне ціле значення у байтах. Можна скористатися цілочисельним суфіксом, подібним до B<MiB>. Приклад: B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "Аргумент I<обмеження> може бути задано у відсотках від загальної фізичної пам'яті системи (RAM). Це може бути корисним особливо при встановленні змінної середовища B<XZ_DEFAULTS> у скрипті ініціалізації системи, який є спільним для різних комп'ютерів. У такий спосіб можна вказати вищий рівень обмеження для систем із більшим об'ємом пам'яті. Приклад: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "Аргументу I<обмеження> може бути повернуто типове значення встановленням значення B<0>. У поточній версії це еквівалентно до встановлення значення аргументу I<обмеження> B<max> (без обмеження на використання пам'яті)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "Для 32-бітової версії B<xz> передбачено особливий випадок: якщо I<обмеження> перевищуватиме B<4020\\ МіБ>, для I<обмеження> буде встановлено значення B<4020\\ MiB>. На MIPS32 замість цього буде використано B<2000\\ MiB>. (Це не стосується значень B<0> і B<max>. Подібної можливості для розпаковування не існує.) Це може бути корисним, коли 32-бітовий виконуваний файл має доступ до простору адрес у 4\\ ГіБ (2 GiB на MIPS32), хоча, сподіваємося, не зашкодить і в інших випадках."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Див. також розділ B<Використання пам'яті>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<обмеження>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Встановити обмеження пам'яті на розпаковування. це також вплине на режим B<--list>. Якщо дія є неможливою без перевищення I<обмеження>, B<xz> покаже повідомлення про помилку і розпаковування файла не відбудеться. Див. B<--memlimit-compress=>I<обмеження>, щоб дізнатися більше про те, як можна задати I<обмеження>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<обмеження>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "Встановити обмеження використання пам'яті для багатопотокового розпаковування. Це може вплинути лише на кількість потоків обробки; це ніколи не призводитиме до відмови B<xz> у розпаковуванні файла. Якщо I<обмеження є надто низьким>, щоб уможливити будь-яку багатопотокову обробку, I<обмеження> буде проігноровано, і B<xz> продовжить обробку в однопотоковому режимі. Зауважте, що якщо використано також B<--memlimit-decompress>, цей параметр буде застосовано до обох режимів, однопотокового та багатопотокового, а отже, задіяне I<обмеження> для багатопотокового режиму ніколи не перевищуватиме обмеження, яке встановлено за допомогою B<--memlimit-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "На відміну від інших параметрів обмеження використання пам'яті, B<--memlimit-mt-decompress=>I<обмеження> містить специфічне для системи типове значення I<обмеження>. Можна скористатися B<xz --info-memory> для перегляду поточного значення."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Цей параметр і його типове значення існують, оскільки без будь-яких обмежень засіб розпакування зі підтримкою потокової обробки міг би намагатися отримати величезний об'єм пам'яті для деяких файлів вхідних даних. Якщо типове I<обмеження> є надто низьким для вашої системи, не вагайтеся і збільшуйте I<обмеження>, але ніколи не встановлюйте для нього значення, яке є більшим за придатний до користування об'єм оперативної пам'яті, оскільки за відповідних файлів вхідних даних B<xz> спробує скористатися цим об'ємом пам'яті, навіть із низькою кількістю потоків обробки. Вичерпання об'єму оперативної пам'яті або використання резервної пам'яті на диску не покращить швидкодію системи під час розпаковування."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Див. B<--memlimit-compress=>I<обмеження>, щоб ознайомитися із можливими способами визначення I<обмеження>. Встановлення для I<обмеження> значення B<0> відновлює типове специфічне для системи значення I<обмеження>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<обмеження>, B<--memlimit=>I<обмеження>, B<--memory=>I<обмеження>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Є еквівалентом визначення B<--memlimit-compress=>I<обмеження> B<--memlimit-decompress=>I<обмеження> B<--memlimit-mt-decompress=>I<обмеження>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "Показати повідомлення про помилку і завершити роботу, якщо не вдасться виконати умови щодо обмеження використання пам'яті без коригування параметрів, які впливають на стиснених виведених даних. Тобто це забороняє B<xz> перемикати кодувальник з багатопотокового режиму на однопотоковий режим і зменшувати розмір словника LZMA2. Навіть якщо використано цей параметр, кількість потоків може бути зменшено для виконання обмеження на використання пам'яті, оскільки це не вплине на результати стискання."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "Автоматичне коригування завжди буде вимкнено при створенні потоків необроблених даних (B<--format=raw>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<потоки>, B<--threads=>I<потоки>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "Вказати кількість потоків обробки, якими слід скористатися. Встановлення для аргументу I<потоки> особливого значення B<0> наказує B<xz> використати не більше потоків обробки, ніж передбачено підтримку у процесорах системи. Справжня кількість потоків може бути меншою за значення I<потоки>, якщо файл вхідних даних не є достатньо великим для поділу на потоки обробки при заданих параметрах або якщо використання додаткових потоків призведе до перевищення обмеження на використання пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "Засоби стискання в однопотоковому та багатопотоковому режимі дають різні результати. Однопотоковий засіб стискання дасть найменший розмір файла, але лише результати роботи багатопотокового засобу стискання може бути розпаковано з використанням декількох потоків. Встановлення для аргументу I<потоки> значення B<1> призведе до використання однопотокового режиму. Встановлення для аргументу I<потоки> будь-якого іншого значення, включно з B<0>, призведе до використання багатопотокового засобу стискання, навіть якщо у системі передбачено підтримки лише одного апаратного потоку обробки даних. (Версія B<xz> 5.2.x у цьому випадку використовувала однопотоковий режим.)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Щоб скористатися багатопотоковим режимом із лише одним потоком обробки, встановіть для аргументу I<потоки> значення B<+1>. Префікс B<+> не впливає на значення, окрім B<1>. Обмеження на використання пам'яті можуть перемкнути B<xz> в однопотоковий режим, якщо не використано параметр B<--no-adjust>. Підтримку B<+> prefix було додано у версії B<xz> 5.4.0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr "Якщо було вказано автоматичне визначення кількості потоків і не вказано обмеження на використання пам'яті, буде використано специфічне для системи типове м'яке обмеження для можливого обмеження кількості потоків обробки. Це обмеження є м'яким у сенсі того, що його буде проігноровано, якщо кількість потоків зрівняється з одиницею, а отже, м'яке обмеження ніколи не запобігатиму у B<xz> стисканню або розпаковуванню. Це типове м'яке обмеження не перемкне B<xz> з багатопотокового режиму на однопотоковий режим. Активні обмеження можна переглянути за допомогою команди B<xz --info-memory>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "У поточній версії єдиним способом поділу на потоки обробки є поділ вхідних даних на блоки і стискання цих блоків незалежно один від одного. Типовий розмір блоку залежить від рівня стискання. Його може бути перевизначено за допомогою параметра B<--block-size=>I<розмір>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "Розпакування з потоками обробки працює лише для файлів, які містять декілька блоків із даними щодо розміру у заголовках блоків. Цю умову задовольняють усі достатньо великі файли, які стиснено у багатопотоковому режимі, але не задовольняють будь-які файли, які було стиснуто у однопотоковому режимі, навіть якщо було використано параметр B<--block-size=>I<розмір>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr "Типовим значенням для I<потоків> є B<0>.  У B<xz> 5.4.x та старіших версіях типовим значенням є B<1>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Нетипові ланцюжки фільтрів засобу стискання"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Нетиповий ланцюжок фільтрування уможливлює докладне визначення параметрів стискання замість використання параметрів, які пов'язано із наперед визначеними рівнями стискання. Якщо вказано нетиповий ланцюжок фільтрів, параметри рівнів стискання (B<-0> \\&...\\& B<-9> і B<--extreme>), які передують їм у рядку команди, буде знехтувано. Якщо параметр рівня стискання вказано після одного або декількох параметрів нетипового ланцюжка фільтрів, буде використано рівень стискання, а попередніми параметрами ланцюжка фільтрування буде знехтувано."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Ланцюжок фільтрів можна порівняти із конвеєром у командному рядку. При стисканні нестиснені вхідні дані потрапляють до першого фільтра, виведені ним дані йдуть до наступного фільтра (якщо такий є). Виведені останнім фільтром дані буде записано до стисненого файла. Максимальна кількість фільтрів у ланцюжку дорівнює чотирьом, але у типовому ланцюжку фільтрів використовують один або два фільтри."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "У багатьох фільтрів є обмеження на місце перебування у ланцюжку фільтрів: деякі фільтри можуть працювати, лише якщо вони є останніми у ланцюжку, деякі, лише якщо не останніми, а деякі працюють у будь-якій позиції ланцюжка. Залежно від фільтра, це обмеження є наслідком структури фільтра або існує для запобігання проблем із захистом."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr "Нетиповий ланцюжок фільтрів можна вказати двома різними способами. Параметри B<--filters=>I<фільтри> і B<--filters1=>I<фільтри> \\&...\\& B<--filters9=>I<фільтри> надають змогу вказати цілий ланцюжок фільтрів в одному варіанті з використанням синтаксису рядків фільтрів liblzma. Крім того, ланцюжок фільтрів можна вказати за допомогою одного або кількох окремих параметрів фільтрування у тому порядку, в якому їх слід використати у ланцюжку фільтрів. Тобто порядок окремих параметрів фільтра є важливим! Під час декодування необроблених потоків (B<--format=raw>) ланцюжок фільтрів має бути вказано у тому ж порядку, що й під час стиснення. Будь-який окремий фільтр або параметри попереднього налаштування, вказані перед параметром повного ланцюжка (B<--filters=>I<фільтри>), буде відкинуто. Окремі фільтри, указані після параметра повного ланцюжка, відновлять типовий ланцюжок фільтрів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr "І параметр повного і параметр окремого фільтра приймають специфічні для фільтрів I<параметри> у форматі списку значень, які відокремлено комами. Зайві коми у I<параметрах> буде проігноровано. У кожного параметра є типове значення, отже, вам слід вказати ті параметри, значення яких ви хочете змінити."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Щоб переглянути увесь ланцюжок фільтрів та I<параметри>, скористайтеся командою B<xz -vv> (тобто, скористайтеся B<--verbose> двічі). Це працює також для перегляду параметрів ланцюжка фільтрів, який використано у рівнях стискання."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters=>I<filters>"
msgstr "B<--filters=>I<фільтри>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr "Визначає повний ланцюжок фільтрів або шаблон у форматі одного параметра. Кожен фільтр можна відокремити від інших пробілами або двома дефісами (B<-->). Можливо, I<фільтри> доведеться взяти в лапки в командному рядку оболонки, щоб їх було оброблено як один параметр. Для позначення I<параметрів> скористайтеся B<:> або B<=>. До шаблона можна додати префікс B<-> і завершити без прапорців або декількома прапорцями. Єдиним підтримуваним прапорцем є B<e> для застосування тих самих параметрів, що й B<--extreme>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr "B<--filters1>=I<фільтри> ... B<--filters9>=I<фільтри>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr "Вказати до дев'яти додаткових ланцюжків фільтрів, якими можна скористатися за допомогою B<--block-list>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr "Наприклад, якщо виконується стискання архіву із виконуваними файлами, за якими йдуть текстові файли, для виконуваної частини може бути використано ланцюжок фільтрів з фільтром BCJ, а для текстової частини — лише фільтр LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters-help>"
msgstr "B<--filters-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr "Вивести довідкове повідомлення з описом того, як вказати шаблони та нетипові ланцюжки фільтрів у параметри B<--filters> і B<--filters1=>I<фільтри> \\&...\\& B<--filters9=>I<фільтри> і завершити роботу із кодом успіху."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<параметри>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Додати фільтр LZMA1 або LZMA2 до ланцюжка фільтрів. Ці фільтри може бути використано лише як останній фільтр у ланцюжку."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 є застарілим фільтром, підтримку якого збережено майже лише через використання формату файлів B<.lzma>, у яких передбачено підтримку лише LZMA1. LZMA2 є оновленою версією LZMA1, у якій виправлено деякі практичні вади LZMA1. У форматі B<.xz> використано LZMA2 і взагалі не передбачено підтримки LZMA1. Швидкість стискання та коефіцієнт стискання для LZMA1 і LZMA2 є практично однаковими."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 і LZMA2 спільно використовують той самий набір I<параметрів>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<шаблон>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "Скинути усі I<параметри> LZMA1 або LZMA2 до параметрів I<шаблона>. Аргумент I<шаблон> складається з цілого числа, після якого може бути однолітерний модифікатор шаблона. Ціле число може належати лише діапазону від B<0> до B<9>, що відповідає параметрам командного рядка B<-0> \\&...\\& B<-9>. Єдиним підтримуваним модифікатором у поточній версії є B<e>, щоб відповідає параметру B<--extreme>. Якщо аргумент B<шаблон> не вказано, типові значення I<параметрів> LZMA1 або LZMA2 буде взято із шаблона B<6>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<розмір>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "Параметр I<розміру> словника (буфера журналу) визначає, скільки байтів нещодавно оброблених нестиснених даних слід зберігати у пам'яті. Алгоритм намагається знайти повторювані послідовності байтів (відповідники) у нестиснених даних і замінити їх на посилання на дані зі словника. Чим більшим є словник, тим вищою є ймовірність відшукати відповідник. Отже, збільшення I<розміру> словника, зазвичай, покращує коефіцієнт стискання, але використання словника, розмір якого перевищу є розмір нестисненого файла є простоюю витратою пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "I<Розмір> типового словника складає від 64\\ КіБ до 64\\ МіБ. Мінімальним є розмір 4\\ КіБ. Максимальним розміром для стискання у поточній версії 1.5\\ ГіБ (1536\\ МіБ). У засобі розпаковування вже передбачено підтримку словників на один байт менших за 4\\ ГіБ, що є максимальним значенням для форматів потоків даних LZMA1 і LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "Аргумент I<розміру> словника і засіб пошуку відповідників (I<mf>) разом визначають параметри використання пам'яті для кодувальника LZMA1 або LZMA2. Для розпаковування потрібен такий самий (або більший) I<розмір> словника, що і для стискання, отже, використання пам'яті для засобу розпаковування буде визначено за розміром словника, який було використано для стискання. У заголовках B<.xz> зберігається I<розмір> словника або як 2^I<n>, або як 2^I<n> + 2^(I<n>-1), отже, ці I<розміри> є дещо пріоритетними для стискання. Інші I<розміри> буде отримано округленням при зберіганні у заголовках B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Визначає кількість буквальних контекстних бітів. Мінімальною кількістю є 0, а максимальною — 4. Типовою кількістю є 3. Крім того, сума I<lc> і I<lp> має не перевищувати 4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Усі байти, які не може бути закодовано як відповідності, буде закодовано як літерали. Тобто літерали є просто 8-бітовими байтами, які буде закодовано по одному за раз."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "При кодуванні літералів роблять припущення, що найвищі біти I<lc> попереднього нестисненого байта корелюють із наступним байтом. Наприклад, у типовому тексті англійською за літерою у верхньому регістрі йде літера у нижньому регістрі, а за літерою у нижньому регістрі, зазвичай, йде інша літера у нижньому регістрі. У наборі символів US-ASCII найвищими трьома бітами є 010 для літер верхнього регістру і 011 для літер нижнього регістру. Якщо I<lc> дорівнює принаймні 3, при кодуванні літералів можна отримати перевагу встановлення цієї властивості для нестиснених даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "Зазвичай, типового значення (3) достатньо. Якщо вам потрібне максимальне стискання, спробуйте B<lc=4>. Іноді це трохи допомагає, а іноді, робить стискання гіршим. Якщо стискання стане гіршим, спробуйте також B<lc=2>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Визначає кількість буквальних позиційних бітів. Мінімальною кількістю є 0, а максимальною — 4. Типовою кількістю є 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> впливає на те, яке вирівнювання у нестиснених даних слід припускати при кодуванні літералів. Див. I<pb> нижче, щоб дізнатися більше про вирівнювання."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Визначає кількість позиційних бітів. Мінімальною кількістю є 0, а максимальною — 4. Типовою кількістю є 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> впливає на те, який тип вирівнювання загалом припускатиметься для нестиснених даних. Типовим є чотирибайтове вирівнювання (2^I<pb>=2^2=4), яке, зазвичай, є добрим варіантом, якщо немає кращих припущень."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Якщо вирівнювання є відомим, встановлення відповідним чином I<pb> може трохи зменшити розмір файла. Наприклад, у текстових файлах із однобайтовим вирівнюванням (US-ASCII, ISO-8859-*, UTF-8), встановлення значення B<pb=0> може трохи поліпшити стискання. Для тексту UTF-16 добрим варіантом є B<pb=1>. Якщо вирівнювання є непарним числом, наприклад 3 байти, найкращим вибором, ймовірно, є B<pb=0>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Хоча прогнозоване вирівнювання можна скоригувати за допомогою I<pb> і I<lp>, у LZMA1 і LZMA2 дещо пріоритетним є 16-байтове вирівнювання. Це, ймовірно, слід враховувати при компонуванні форматів файлів, які, ймовірно, часто будуть стискатися з використанням LZMA1 або LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<mf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "Засіб пошуку відповідників має значний вплив на швидкість, використання пам'яті та коефіцієнт стискання кодувальника. Зазвичай, засоби пошуку відповідників на основі ланцюжка хешів є швидшими за засоби пошуку відповідників на основі двійкового дерева. Типовий засіб залежить від I<шаблона>: для 0 використовують B<hc3>, для 1\\(en3 — B<hc4>, а для решти використовують B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Передбачено підтримку вказаних нижче засобів пошуку відповідників. Наведені нижче формули обчислення використання пам'яті є грубими наближеннями, які є найближчими до реальних значень, якщо значенням I<словник> є степінь двійки."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Ланцюжок хешів із 2- та 3-байтовим хешуванням"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Мінімальне значення I<пріоритетності>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Використання пам'яті:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 7.5 (якщо I<dict> E<lt>= 16 МіБ);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 5.5 + 64 МіБ (якщо I<dict> E<gt> 16 МіБ)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Ланцюжок хешів із 2-, 3- та 4-байтовим хешуванням"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Мінімальне значення I<пріоритетності>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 7.5 (якщо I<dict> E<lt>= 32 МіБ);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 6.5 (якщо I<dict> E<gt> 32 МіБ)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Двійкове дерево із 2-байтовим хешуванням"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Мінімальне значення I<пріоритетності>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Використання пам'яті: I<dict> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Двійкове дерево із 2- і 3-байтовим хешуванням"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 11.5 (якщо I<dict> E<lt>= 16 МіБ);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 9.5 + 64 МіБ (якщо I<dict> E<gt> 16 МіБ)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Двійкове дерево із 2-, 3- і 4-байтовим хешуванням"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 11.5 (якщо I<dict> E<lt>= 32 МіБ);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 10.5 (якщо I<dict> E<gt> 32 МіБ)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<режим>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "Параметр I<режиму> стискання визначає спосіб, який буде використано для аналізу даних, які створено засобом пошуку відповідників. Підтримуваними I<режимами> є B<fast> (швидкий) і B<normal> (нормальний). Типовим є режим B<fast> для I<шаблонів> 0\\(en3 і режим B<normal> для I<шаблонів> 4\\(en9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Зазвичай, із засобом пошуку відповідників на основі ланцюжка хешів використовують B<fast>, а із засобом пошуку відповідників на основі двійкового дерева використовують B<normal>. Так само налаштовано і I<шаблони>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<пріоритетність>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Вказати, яка довжина є пріоритетною для відповідності. Щойно буде виявлено відповідність у принаймні I<пріоритетність> байтів, алгоритм зупинятиме пошук можливих кращих відповідників."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "I<Пріоритетністю> може бути число до 2\\(en273 байтів. Вищі значення дають кращий коефіцієнт стискання за рахунок швидкості. Типове значення залежить від I<шаблона>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<глибина>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Вказати максимальну глибину пошуку у засобі пошуку відповідності. Типовим є особливе значення 0, яке наказує засобу стискання визначити прийнятну I<глибину> на основі I<mf> і I<пріоритетності>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "Прийнятним значенням I<глибини> для ланцюжків хешів є 4\\(en100 і 16\\(en1000 для двійкових дерев. Використання дуже високих значень для I<глибини> може зробити кодувальник дуже повільним для деяких файлів. Не встановлюйте значення I<глибини>, що перевищує 1000, якщо ви не готові перервати стискання, якщо воно триватиме надто довго."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "При декодуванні необроблених потоків даних (B<--format=raw>), LZMA2 потребує лише I<розміру> словника. LZMA1 потребує також I<lc>, I<lp> і I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<параметри>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<параметри>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Додати фільтр гілок/викликів/переходів (branch/call/jump або BCJ) до ланцюжка фільтрів. Цими фільтрами можна скористатися, лише якщо вони не є останнім фільтром у ланцюжку фільтрів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "Фільтр BCJ перетворює відносні адреси у машинному коді на їхні абсолютні відповідники. Це не змінює розміру даних, але підвищує резервування, що може допомогти LZMA2 створити файл B<.xz> на 0\\(en15\\ % менше. Фільтри BCJ завжди є придатними до обернення, тому використання фільтра BCJ до помилкового типу даних не спричинятиме втрати даних, хоча може дещо погіршити коефіцієнт стискання. Фільтри BCJ є дуже швидкими і такими, що використовують незначний об'єм пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Ці фільтри BCJ мають відомі проблеми, які пов'язано із рівнем стискання:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "У деяких типах файлів, де зберігається виконуваний код, (наприклад, в об'єктних файлах, статичних бібліотеках та модулях ядра Linux) адреси в інструкціях заповнено значеннями заповнювача. Ці фільтри BCJ виконуватимуть перетворення адрес, яке зробить стискання для цих файлів гіршим."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Якщо фільтр BCJ застосовано до архіву, може так статися, що він погіршить коефіцієнт стискання порівняно із варіантом без фільтра BCJ. Наприклад, якщо є подібні або навіть однакові виконувані файли, фільтрування, ймовірно, зробить ці файли менш подібними, а отже, зробить стискання гіршим. Вміст файлів, які не є виконуваними, у тому самому архіві також може вплинути на результат. На практиці, варто спробувати варіанти з фільтром BCJ і без нього, щоб визначитися із тим, що буде кращим у кожній ситуації."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Різні набори інструкцій мають різне вирівнювання: виконуваний файл має бути вирівняно на кратне до цього значення у вхідних даних, щоб фільтр спрацював."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Фільтр"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Вирівнювання"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Нотатки"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "32-бітова або 64-бітова x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr ""
"Найкращим є вирівнювання за\n"
";;4096 байтами"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Лише зворотний порядок байтів"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr "RISC-V"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr "Оскільки фільтровані BCJ дані, зазвичай, стискають за допомогою LZMA2, коефіцієнт стискання можна трохи поліпшити, якщо параметри LZMA2 буде встановлено так, щоб вони відповідали вирівнюванню вибраного фільтра BCJ. Приклади:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr "Фільтр IA-64 має 16-байтове вирівнювання, отже B<pb=4,lp=4,lc=0> дасть добрі результати у поєднанні із LZMA2 (2^4=16)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr "Код RISC-V має 2-байтове або 4-байтове вирівнювання залежно від того, чи містить файл 16-бітові стислі інструкції (розширення C). Якщо використано 16-бітові інструкції, добрі результати дасть B<pb=2,lp=1,lc=3> або B<pb=1,lp=1,lc=3>. Якщо 16-бітових інструкцій немає, найкращим варіантом є B<pb=2,lp=2,lc=2>. Можна скористатися B<readelf -h>, щоб перевірити, чи є \"RVC\" у рядку \"Flags\"."

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr "ARM64 завжди вирівняно на 4 байти, тому найкращим варіантом буде B<pb=2,lp=2,lc=2>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr "Фільтр x86 є виключенням. Зазвичай, добрі результати дають типові для LZMA2 значення (B<pb=2,lp=0,lc=3>), якщо стискають виконувані файли x86."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "У всіх фільтрах BCJ передбачено підтримку тих самих I<параметрів>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<зсув>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Встановити початковий I<зсув>, який буде використано при перетворенні між відносною та абсолютною адресами. Значення I<зсув> має бути кратним до вирівнювання фільтра (див. таблицю вище).  Типовим зсувом є нульовий. На практиці, типове значення є прийнятним; визначення нетипового значення I<зсув> майже завжди нічого корисного не дає."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<параметри>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Додати дельта-фільтр до ланцюжка фільтрів. Дельта-фільтр може бути використано, лише якщо він не є останнім у ланцюжку фільтрів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "У поточній версії передбачено підтримку обчислення лише простої побітової дельти. Це може бути корисним при стисканні, наприклад, нестиснутих растрових зображень або нестиснутих звукових даних PCM. Втім, спеціалізовані алгоритми можуть давати значно кращі результати за дельту + LZMA2. Це правило особливо стосується звукових даних, які стискає швидше і краще, наприклад, B<flac>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "Підтримувані I<параметри>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<відстань>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "Вказати I<відстань> обчислень різниці у байтах. Значення I<відстань> має потрапляти у діапазон 1\\(en256. Типовим значенням є 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "Наприклад, з B<dist=2> та восьмибайтовими вхідними даними A1 B1 A2 B3 A3 B5 A4 B7, результатом буде A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Інші параметри"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Придушити попередження та сповіщення. Вкажіть цей параметр двічі, щоб придушити також повідомлення про помилки. Цей параметр не впливає на стан виходу з програми. Тобто, навіть якщо було придушено попередження, стан виходу вказуватиме на те, що попередження були."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Докладний режим повідомлень. Якщо стандартне виведення помилок з'єднано із терміналом, B<xz> показуватиме індикатор поступу. Використання B<--verbose> двічі призведе до ще докладнішого виведення."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "Індикатор поступу показує такі дані:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "Частку завершеності буде показано, якщо відомий розмір файла вхідних даних. Тобто, для каналів даних частку не може бути показано."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Об'єм стиснених виведених даних (стискання) або оброблених (розпаковування)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Об'єм незапакованих даних (стискання) або виведених даних (розпаковування)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Коефіцієнт стискання, який обчислено діленням об'єму оброблених стиснутих даних на об'єм оброблених нестиснутих даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Швидкість стискання або розпаковування. Обчислюється як об'єм нестиснутих даних (стискання) або виведених даних (розпаковування) за секунду. Його буде показано за декілька секунд з моменту, коли B<xz> почала обробляти файл."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Витрачений час у форматі Х:СС або Г:ХХ:СС."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "Оцінку часу, що лишився, буде показано, лише якщо розмір файла вхідних даних є відомим, і минуло принаймні декілька секунд з моменту, коли B<xz> почала обробляти файл. Час буде показано у менш точному форматі, без двокрапок, наприклад, 2 хв. 30 с."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Якщо стандартним виведенням помилок не є термінал, B<--verbose> призведе до того, що B<xz> виведе назву файла, стиснений розмір, нестиснений розмір, коефіцієнт стискання та, можливо, також швидкість та витрачений час у одному рядку до стандартного виведення помилок після стискання або розпаковування файла. Швидкість та витрачений час буде включено, лише якщо дія триває принаймні декілька секунд. Якщо дію не буде завершено, наприклад, через втручання користувача, буде також виведено частку виконання, якщо відомий розмір файла вхідних даних."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Не встановлювати стан виходу 2, навіть якщо було виявлено відповідність умові, яка варта попередження. Цей параметр не впливає на рівень докладності повідомлень, отже, слід використати B<--quiet> і B<--no-warn>, щоб програма не показувала попереджень і не змінювала стан виходу."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Виводити повідомлення у придатному для обробки комп'ютером форматі. Цей формат призначено для полегшення написання оболонок, які використовуватимуть B<xz> замість liblzma, що може бути зручним для різноманітних скриптів. Виведені дані з цим параметром має бути стабільним для усіх випусків B<xz>. Докладніший опис можна знайти у розділі B<РЕЖИМ РОБОТА>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "Вивести у придатному для читання людиною форматі, скільки фізичної пам'яті (RAM) та скільки потоків процесора є за даними B<xz> у системі, обмеження для стискання та розпаковування, а потім успішно завершити роботу."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Вивести повідомлення про помилку з описом найбільш типових використаних параметрів і успішно завершити роботу."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Вивести довідкове повідомлення з описом усіх можливостей B<xz> і успішно завершити роботу"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Вивести номер версії B<xz> та liblzma у зручному для читання форматі. Щоб отримати дані, зручні для обробки на комп'ютері, вкажіть B<--robot> до B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "РЕЖИМ РОБОТА"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr "Режим робота активують за допомогою параметра B<--robot>. Він спрощує обробку виведених B<xz> даних іншими програмами. У поточній версії підтримку B<--robot> передбачено лише разом із B<--list>, B<--filters-help>, B<--info-memory> і B<--version>. У майбутньому підтримку параметра буде передбачено для стискання та розпаковування."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Режим списку"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "У B<xz --robot --list> використано табуляції для поділу виведених даних. Першим стовпчиком у кожному рядку є рядок, що вказує на тип відомостей, які можна знайти у цьому рядку:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Це завжди перший рядок на початку списку файла. Другим стовпчиком у рядку є назва файла."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "У цьому рядку містяться загальні відомості щодо файла B<.xz>. Цей рядок завжди виводять після рядка B<name>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Цей тип рядка використовують, лише якщо було вказано B<--verbose>. Буде стільки рядків B<stream>, скільки потоків у файлі B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Цей тип рядка використовують, лише якщо було вказано B<--verbose>. Буде стільки рядків B<block>, скільки блоків у файлі B<.xz>. Рядки B<block> буде показано після усіх рядків B<stream>; різні типи рядків не перемежовуються."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Цей тип рядків використовують, лише якщо B<--verbose> було вказано двічі. Цей рядок буде виведено після усіх рядків B<block>. Подібно до рядка B<file>, рядок B<summary> містить загальні відомості щодо файла B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Цей рядок завжди є найостаннішим рядком у виведеному списку. У ньому буде показано загальні кількості та розміри."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Стовпчики у рядках B<файла>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Кількість потоків у файлі"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Загальна кількість блоків у потоках"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Розмір стисненого файла"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Розмір нестисненого файла"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Коефіцієнт стискання, наприклад, B<0.123>. Якщо коефіцієнт перевищує 9.999, замість коефіцієнта буде показано дефіси (B<--->)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Список відокремлених комами назв перевірок цілісності. Наведені нижче рядки використовують для відомих типів перевірок: B<None>, B<CRC32>, B<CRC64> і B<SHA-256>. Для невідомих типів перевірок буде використано B<Unknown->I<N>, де I<N> є ідентифікатором перевірки у форматі десяткового числа (одна або дві цифри)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Загальний розмір доповнення потоку у файлі"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Стовпчики у рядках B<stream>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Номер потоку (перший потік має номер 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Кількість блоків у потоці"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Зсув початку стисненого"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Зсув початку нестисненого"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Стиснений розмір (не включає доповнення потоку)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Нестиснутий розмір"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Рівень стискання"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Назва перевірки цілісності"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Розмір доповнення потоку"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Стовпчики у рядках B<block>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Номер потоку, що містить цей блок"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Номер блоку відносно початку потоку (перший блок має номер 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Номер блоку відносно початку файла"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Зсув початку стисненого відносно початку файла"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Зсув початку нестисненого відносно початку файла"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Загальний стиснений розмір блоку (включено з заголовками)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Якщо B<--verbose> було вказано двічі, до рядків B<block> буде включено додаткові стовпчики. Ці стовпчики не буде показано, якщо вказано одинарний параметр B<--verbose>, оскільки отримання цих відомостей потребує багатьох позиціювань, а ця процедура може бути повільною:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Значення перевірки цілісності у шістнадцятковій формі"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Розмір заголовка блоку"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Прапорці блоку: B<c> вказує, що наявний стиснений розмір, а B<u> вказує, що наявний нестиснений розмір. Якщо прапорець не встановлено, буде показано (B<->) замість підтримання фіксованого розміру рядка. У майбутньому наприкінці рядка може бути додано нові прапорці."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Розмір справжніх стиснених даних у блоці (це включає заголовок блоку, доповнення блоку та поля перевірок)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Об'єм пам'яті (у байтах), який потрібен для розпаковування цього блоку за допомогою цієї версії B<xz>"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Ланцюжок фільтрів. Зауважте, що більшість параметрів, які використано під час стискання, не є наперед відомим, оскільки у заголовках B<.xz> зберігаються лише параметри, які потрібні для розпаковування."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Стовпчики у рядках B<summary>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Об'єм пам'яті (у байтах), який потрібен для розпаковування цього файла за допомогою цієї версії B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> або B<no> вказує, якщо усі заголовки блоків містять одразу стиснений розмір та розпакований розмір"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Починаючи з> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Мінімальна версія B<xz>, яка потрібна для розпаковування файла"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Стовпчики рядка B<totals>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Кількість потоків"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Кількість блоків"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Стиснутий розмір"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Середній коефіцієнт стискання"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Список відокремлених комами назв перевірок цілісності, результати яких наявні у файлах"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Розмір доповнення потоку"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Кількість файлів. Наявний тут для зберігання такого самого порядку стовпчиків, що і у попередніх рядках B<file>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Якщо B<--verbose> було вказано двічі, до рядка B<totals> буде включено додаткові стовпчики:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Максимальний об'єм пам'яті (у байтах), який потрібен для розпаковування файлів за допомогою цієї версії B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "У майбутніх версіях може бути додано нові типи рядків і нові стовпчики до наявних типів рядків, але наявні стовпчики мають лишитися незмінними."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Filters help"
msgstr "Довідка з фільтрування"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr "B<xz --robot --filters-help> виведе список підтримуваних фільтрів у такому форматі:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr "I<фільтр>B<:>I<параметр>B<=E<lt>>I<значення>B<E<gt>,>I<параметр>B<=E<lt>>I<значення>B<E<gt>>..."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<filter>"
msgstr "I<фільтр>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the filter"
msgstr "Назва фільтра"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<option>"
msgstr "I<параметр>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr "Назва специфічного для фільтра параметра"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr "I<значення>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr "Числові діапазони I<value> слід вказати у форматі B<E<lt>>I<мінімум>B<->I<максимум>B<E<gt>>. Варіанти рядка I<значення> показано у B<E<lt> E<gt>> і відокремлено символом B<|>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr "Кожен фільтр буде виведено до окремого рядка."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Дані щодо обмеження пам'яті"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr "B<xz --robot --info-memory> виводить один рядок з декількома відокремленими табуляціями стовпчиками:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Загальний об'єм фізичної пам'яті (RAM) у байтах."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Обмеження на використання пам'яті для стискання у байтах (B<--memlimit-compress>). Особливе значення B<0> вказує на типові налаштування, якими для однопотокового режиму є налаштування без обмеження на використання пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Обмеження на використання пам'яті для розпакування у байтах (B<--memlimit-decompress>). Особливе значення B<0> вказує на типові налаштування, якими для однопотокового режиму є налаштування без обмеження на використання пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "Починаючи з B<xz> 5.3.4alpha: використання пам'яті для багатопотокового розпаковування у байтах (B<--memlimit-mt-decompress>). Ніколи не дорівнює нулеві, оскільки буде використано специфічне для системи типове значення, яке показано у стовпчику 5, якщо обмеження не встановлено явним чином. Також ніколи не перевищуватиме значення у стовпчику 3, навіть якщо було вказано більше значення за допомогою B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "Починаючи з B<xz> 5.3.4alpha: специфічне для системи типове обмеження на використання пам'яті, яке використовують для обмеження кількості потоків при стисканні з автоматичною кількістю потоків (B<--threads=0>) і без визначення обмеження на використання пам'яті (B<--memlimit-compress>). Це значення також використовують як типове значення для B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "Починаючи з B<xz> 5.3.4alpha: кількість доступних потоків обробки процесора."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "У майбутньому у виведенні B<xz --robot --info-memory> може бути більше стовпчиків, але у виведеному буде не більше за один рядок."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Версія"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr "B<xz --robot --version> виведе назву версії B<xz> і liblzma у такому форматі:"

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Основна версія."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Проміжна версія. Непарні номери буде використано для стабільних версій. Непарні номери є номерами тестових версій."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Рівень латання для стабільних випусків або просто лічильник для випусків, які перебувають у розробці."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Стабільність. 0 — alpha, 1 — beta, а 2 означає «стабільна версія». I<S> має завжди дорівнювати 2, якщо I<YYY> є парним."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> є тим самим в обох рядках, якщо B<xz> і liblzma належать до одного випуску XZ Utils."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Приклади: 4.999.9beta — це B<49990091>, а 5.0.0 — це B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "СТАН ВИХОДУ"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Усе добре."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "Сталася помилка."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Сталося щось варте попередження, але справжніх помилок не сталося."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Зауваження (не попередження або помилки), які виведено до стандартного виведення помилок, не впливають на стан виходу."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "СЕРЕДОВИЩЕ"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> обробляє списки відокремлених пробілами параметрів зі змінних середовища B<XZ_DEFAULTS> і B<XZ_OPT>, перш ніж обробляти параметри з рядка команди. Зауважте, що буде оброблено лише параметри зі змінних середовища; усі непараметричні записи буде без повідомлень проігноровано. Обробку буде виконано за допомогою функції B<getopt_long>(3), яку також використовують для аргументів рядка команди."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr "B<Попередження:> Встановлюючи ці змінні середовища, ви насправді змінюєте програми та скрипти, які виконують B<xz>. У більшості випадків без проблем можна встановлювати обмеження на використання пам'яті, кількість потоків і параметри стиснення за допомогою змінних середовища. Однак деякі параметри можуть порушити роботу скриптів. Очевидним прикладом є B<--help>, який змушує B<xz> показувати текст довідки замість стискання або розпаковування файла. Менш очевидними є приклади B<--quiet> і B<--verbose>. У багатьох випадках усе працюватиме добре, якщо увімкнути індикатор поступу за допомогою B<--verbose>, але у деяких ситуаціях додаткові повідомлення створюють проблеми. Рівень докладності також впливає на поведінку B<--list>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Специфічні для користувача або загальносистемні типові параметри. Зазвичай, їх встановлюють у скрипті ініціалізації оболонки для типового вмикання обмеження на використання пам'яті у B<xz> або встановлення типової кількості потоків обробки. Окрім скриптів ініціалізації оболонки і подібних особливих випадків, не слід встановлювати або скасовувати встановлення значення B<XZ_DEFAULTS> у скриптах."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Цю змінну призначено для передавання параметрів до B<xz>, якщо неможливо встановити параметри безпосередньо у рядку команди B<xz>. Це трапляється, якщо B<xz> запущено скриптом або інструментом, наприклад, GNU B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Скрипти можуть використовувати B<XZ_OPT>, наприклад, для встановлення специфічних типових параметрів стискання. Втім, рекомендуємо дозволити користувачам перевизначати B<XZ_OPT>, якщо це має якісь причини. Наприклад, у скриптах B<sh>(1) можна скористатися чимось таким:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "СУМІСНІСТЬ ІЗ LZMA UTILS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "Синтаксис рядка команди B<xz> практично є надбудовою щодо B<lzma>, B<unlzma> і B<lzcat> з LZMA Utils 4.32.x. У більшості випадків можна замінити LZMA Utils XZ Utils без порушення працездатності наявних скриптів. Втім, існують певні несумісності, які іноді можуть спричиняти проблеми."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Рівні шаблонів стискання"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "Нумерація у шаблонах рівнів стискання у B<xz> не є тотожною до нумерації у LZMA Utils. Найважливішою відмінністю є прив'язка розмірів словника до різних шаблонів. Розмір словника грубо рівний використанню пам'яті у засобі розпаковування."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Рівень"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "н/д"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 КіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 КіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Відмінності у розмірах словників також впливають на використання пам'яті засобом стискання, але є і інші відмінності між LZMA Utils і XZ Utils, які роблять різницю ще помітнішою:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 МіБ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 МіБ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Типовим рівнем стискання у LZMA Utils є B<-7>, а у XZ Utils — B<-6>, отже, обидва комплекти програм типово використовують словник розміром у 8 МіБ."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Потокові і непотокові файл .lzma"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "Розмір нестисненого файла може бути збережено у заголовку B<.lzma>. LZMA Utils зберігають дані при стисканні звичайних файлів. Альтернативним підходом є позначення нестисненого розміру як невідомого і використання позначки кінця вмісту для позначення місця, де засіб розпаковування має зупинитися. У LZMA Utils цей спосіб використовують, якщо нестиснений розмір є невідомим, що трапляється, наприклад, для конвеєрів обробки даних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "У B<xz> передбачено підтримку розпаковування файлів B<.lzma> з позначкою кінця вмісту та без неї, але усі файли B<.lzma>, які створено за допомогою B<xz>, використовують позначку кінця вмісту, а нестиснений розмір у заголовку B<.lzma> позначають як невідомий. Це може призвести до проблем у деяких нетипових ситуаціях. Наприклад, розпакувальник B<.lzma> у вбудованому пристрої може працювати лише з файлами, для яких відомий нестиснений розмір. Якщо ви зіткнулися з цією проблемою, вам слід скористатися LZMA Utils або LZMA SDK для створення файлів B<.lzma> із відомим розміром нестиснених даних."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Непідтримувані файли .lzma"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "У форматі B<.lzma> можливі значення I<lc> аж до 8 і значення I<lp> аж до 4. LZMA Utils можуть розпаковувати файли із будь-якими значеннями I<lc> і I<lp>, але завжди створюють файли з B<lc=3> і B<lp=0>. Створення файлів з іншими значеннями I<lc> і I<lp> є можливим за допомогою B<xz> і LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "Реалізація фільтра LZMA1 у liblzma потребує, щоби сума I<lc> і I<lp> не перевищувала 4. Отже, файли B<.lzma>, у яких перевищено обмеження, не може бути розпаковано за допомогою B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "LZMA Utils створюють лише файли B<.lzma>, які мають розмір словника у 2^I<n> (степінь 2), але приймають файли із будь-яким розміром словника. liblzma приймає лише файли B<.lzma>, які мають розмір словника 2^I<n> або 2^I<n> + 2^(I<n>-1). Так зроблено для зменшення помилок при виявленні файлів B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Ці обмеження не мають призводити до проблем на практиці, оскільки практично усі файли B<.lzma> було стиснено з використанням параметрів, які приймає liblzma."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Кінцевий мотлох"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "При розпаковуванні LZMA Utils без повідомлень ігнорують усі дані після першого потоку B<.lzma>. У більшості випадків це пов'язано із вадою у програмі. Це також означає, що у LZMA Utils не передбачено підтримки розпаковування з'єднаних файлів B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Якщо після першого потоку B<.lzma> лишилися дані, B<xz> вважатиме файл пошкодженим, якщо не було використано B<--single-stream>. Це може зашкодити роботі скриптів, де зроблено припущення, що кінцеві зайві дані буде проігноровано."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "ПРИМІТКИ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "Стискання даних може бути різним"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "Точні стиснені дані, які створено на основі того самого нестисненого файла вхідних даних, можуть бути різними для різних версій XZ Utils, навіть якщо використано однакові параметри стискання. Причиною цього є удосконалення у кодувальнику (пришвидшення або краще стискання) без зміни формату файлів. Виведені дані можуть бути різними навіть для різних збірок тієї самої версії XZ Utils, якщо використано різні параметри збирання."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Написане вище означає, що після реалізації B<--rsyncable> файли-результати не обов'язково можна буде синхронізувати за допомогою rsyncable, якщо старий і новий файли було стиснено за допомогою тієї самої версії xz. Цю проблему можна усунути, якщо буде заморожено частину реалізації кодувальника, щоб введені для rsync дані були стабільними між версіями xz."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Вбудовані розпакувальники .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "У вбудованих реалізаціях розпакувальника B<.xz>, подібних до XZ Embedded, не обов'язково передбачено підтримку файлів, які створено із типами I<перевірки> цілісності, відмінними від B<none> і B<crc32>. Оскільки типовим є B<--check=crc64>, вам слід використовувати  B<--check=none> або B<--check=crc32> при створенні файлів для вбудованих систем."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "Поза вбудованими системами, в усіх засобах розпаковування формату B<.xz> передбачено підтримку усіх типів I<перевірок> або принаймні можливість розпакувати файл без перевірки цілісності, якщо підтримки певної I<перевірки> не передбачено."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "У XZ Embedded передбачено підтримку BCJ, але лише з типовим початковим зсувом."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "ПРИКЛАДИ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Основи"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Стиснути файл I<foo> до I<foo.xz> за допомогою типового рівня стискання (B<-6>) і вилучити I<foo>, якщо стискання відбулося успішно:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz foo\\fR\n"
msgstr "\\f(CRxz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Розпакувати I<bar.xz> до I<bar> і не вилучати I<bar.xz>, навіть якщо розпаковування відбулося успішно:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "\\f(CRxz -dk bar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "Створити I<baz.tar.xz> з використанням шаблона B<-4e> (B<-4 --extreme>), який є повільнішими за типовий B<-6>, але потребує менше пам'яті для стискання та розпаковування (48\\ МіБ та 5\\ МіБ, відповідно):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Суміш стиснених і нестиснених файлів можна розпакувати до стандартного виведення за допомогою єдиної команди:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Паралельне стискання багатьох файлів"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "У GNU і *BSD можна скористатися B<find>(1) і B<xargs>(1) для паралельного стискання багатьох файлів:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "Параметр B<-P> B<xargs>(1) встановлює кількість паралельних процесів B<xz>. Найкраще значення параметра B<-n> залежить від того, скільки файлів має бути стиснено. Якщо файлів мало, значенням, ймовірно, має бути 1. Якщо файлів десятки тисяч, може знадобитися значення 100 або навіть більше, щоб зменшити кількість процесів B<xz>, які врешті створить B<xargs>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "Параметр B<-T1> для B<xz> тут для примусового встановлення однопотокового режиму, оскільки для керування рівнем паралелізації використано B<xargs>(1)."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Режим робота"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Обчислити скільки байтів було заощаджено загалом після стискання декількох файлів:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Скрипту можуть знадобитися дані щодо того, що використано достатньо нову версію B<xz>. У наведеному нижче скрипті B<sh>(1) виконано перевірку того, що номер версії засобу B<xz> є принаймні рівним 5.0.0. Цей спосіб є сумісним зі старими тестовими версіями, де не передбачено підтримки параметра B<--robot>:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Встановити обмеження на використання пам'яті для розпаковування за допомогою B<XZ_OPT>, але якщо обмеження вже було встановлено, не збільшувати його:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "Найпростішим використанням ланцюжка фільтрів є налаштовування шаблона LZMA2. Це може бути корисним, оскільки у шаблонах використано лише підмножину потенційно корисних комбінацій параметрів стискання."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "При налаштовуванні шаблонів LZMA2 корисними є стовпчики CompCPU таблиць з описів параметрів B<-0> ... B<-9> і B<--extreme>. Ось відповідні частини з цих двох таблиць:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Якщо вам відомо, що певний файл потребує дещо більшого словника (наприклад, 32\\ МіБ) для якісного стискання, але ви хочете стиснути його швидше за команду B<xz -8>, можна внести зміни до шаблона із нижчим значенням CompCPU (наприклад, 1) для використання більшого словника:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Для певних файлів наведена вище команда може працювати швидше за B<xz -6> і стискати дані значно краще. Втім, слід наголосити, переваги більшого словника з одночасним низьким значенням CompCPU проявляються лише для деяких файлів. Найочевиднішим випадком, коли великий словник є корисним, є випадок, коли архів містить дуже подібні файли розміром у принаймні декілька мегабайтів. Розмір словника має бути значно більшим за будь-який окремий файл, щоб у LZMA2 було використано усі переваги подібностей між послідовними файлами."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Якщо дуже високий рівень використання пам'яті у засобі стискання або розпаковування не є проблемою, і файли, який стискають має об'єм у принаймні декілька десятків мегабайтів, може бути корисним використання навіть більшого за 64 МіБ словника, який використано у B<xz -9>:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Використання B<-vv> (B<--verbose --verbose>), подібно до наведеного вище прикладу, може бути корисним для перегляду вимог з боку засобів стискання та розпаковування до пам'яті. Пам'ятайте, що використання словника, розмір якого перевищує розмір файла, який стискають, є простоюю витратою пам'яті, отже наведену вище команду не варто використовувати для малих файлів."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "Іноді час стискання не має значення, але використання пам'яті засобом розпаковування має бути низьким для того, щоб, наприклад, уможливити розпаковування файла у вбудованій системі. У наведеній нижче команді використано B<-6e> (B<-6 --extreme>) як основу і встановлено розмір словника лише у 64\\ КіБ. Файл-результат можна розпакувати за допомогою XZ Embedded (ось чому використано B<--check=crc32>) з використанням лише 100\\ КіБ пам'яті."

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Якщо вам потрібно витиснути зі стискання максимальну кількість байтів, може допомогти коригування кількості бітів контексту літералів (I<lc>) та кількість позиційних бітів (I<pb>). Також може допомогти коригування кількості бітів позиції літералів (I<lp>), але, зазвичай, важливішими є I<lc> і I<pb>. Наприклад, в архівах зі початковим кодом міститься здебільшого текст US-ASCII, щось подібне до наведеного нижче може дещо (на щось близьке до 0,1\\ %) зменшити файл, порівняно із B<xz -6e> (спробуйте також без B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "Використання іншого фільтра разом із LZMA2 може покращити стискання для певних типів файлів. Наприклад, для стискання бібліотеки спільного користування x86-32 або x86-64 з використанням фільтра BCJ x86 скористайтеся такою командою:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Зауважте, що порядок параметрів фільтрування має значення. Якщо B<--x86> вказано після B<--lzma2>, B<xz> повідомить про помилку, оскільки після LZMA2 не може бути жодного фільтра, а також оскільки фільтр BCJ x86 не можна використовувати як останній фільтр у ланцюжку."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Фільтр Delta разом із LZMA2 може дати добрі результати для растрових зображень. Зазвичай, результати є кращими за формат PNG, у якого є декілька більш досконалих фільтрів, ніж проста дельта, але там використовують для стискання Deflate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "Зображення слід берегти у нестисненому форматі, наприклад, як нестиснений TIFF. Параметр відстані фільтра Delta встановлюють так, щоб він збігався із кількістю байтів на піксель у зображенні. Наприклад, для 24-бітового растрового зображення RGB слід вказати B<dist=3>, а також добре передати B<pb=0> до LZMA2 для пристосовування до трибайтового вирівнювання:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Якщо в один архів запаковано декілька зображень (наприклад, в архів B<.tar>), фільтр Delta також даватиме добрі результати, якщо у всіх зображеннях однакова кількість байтів для кожного пікселя."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "ДИВ. ТАКОЖ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr "Вбудовуваний XZ: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr "8 квітня 2024 року"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec — невеличкі розпакувальники .xz і .lzma"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<параметр...>] [I<файл...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<параметр...>] [I<файл...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> є інструментом на основі liblzma, який призначено лише для розпаковування файлів B<.xz> (і лише файлів B<.xz>). B<xzdec> призначено для того, щоб працювати як повноцінний замінник B<xz>(1) у більшості типових ситуацій, де скрипт було написано для використання B<xz --decompress --stdout> (і, можливо, декількох інших типових параметрів), для розпаковування файлів B<.xz>. B<lzmadec> є тотожним до B<xzdec>, але у B<lzmadec> передбачено підтримку файлів B<.lzma>, замість файлів B<.xz>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Щоб зменшити розмір виконуваного файла, у B<xzdec> не передбачено підтримки багатопотокової обробки та локалізації, а також читання параметрів зі змінних середовища B<XZ_DEFAULTS> і B<XZ_OPT>. У B<xzdec> не передбачено підтримки показу проміжних даних щодо поступу: надсилання B<SIGINFO> до B<xzdec> не призводить ні до яких наслідків, але надсилання B<SIGUSR1> перериває процес, замість показу даних щодо поступу."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Буде проігноровано для сумісності з B<xz>(1). У B<xzdec> передбачено підтримку лише розпаковування."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Буде проігноровано. Призначено для сумісності з B<xz>(1). B<xzdec> ніколи не створюватиме і ніколи не вилучатиме ці файли."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Буде проігноровано. Для сумісності з B<xz>(1). B<xzdec> завжди записує розпаковані дані до стандартного виведення."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Якщо цей параметр вказано один раз, нічого не станеться, оскільки B<xzdec> ніколи не показуватиме жодних попереджень або нотаток. Вкажіть параметр двічі, щоб придушити повідомлення про помилки."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Буде проігноровано для сумісності із B<xz>(1). B<xzdec> ніколи не використовує стан виходу 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Вивести довідкове повідомлення і успішно завершити роботу."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Вивести номер версії B<xzdec> та liblzma."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Усе добре."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> не має жодних повідомлень із попередженнями, на відміну від B<xz>(1), тому у B<xzdec> стан виходу 2 не використовується."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Користуйтеся B<xz>(1), замість B<xzdec> або B<lzmadec>, для щоденних потреб. B<xzdec> та B<lzmadec> призначено лише для тих ситуацій, коли важливо мати меншу програму для розпаковування, ніж B<xz>(1)."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> і B<lzmadec> не такі вже і малі програми. Їхній розмір можна зменшити викиданням можливостей з liblzma під час збирання, але цього зазвичай не роблять для виконуваних файлів, які поширюються у типових, не вбудованих, дистрибутивах операційних систем. Якщо вам потрібний дуже мала програма для розпаковування B<.xz>, варто скористатися XZ Embedded."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30 червня 2013 року"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo — показ відомостей, які зберігаються у заголовку файла .lzma"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<файл...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> показує дані, що зберігаються у заголовку файла B<.lzma>. Вона читає перші 13 байтів із вказаного I<файла>, розкодовує заголовок і виводить його до стандартного виведення у зручному для читання форматі. Якщо не вказано жодного I<файла> або замість I<файла> вказано B<->, дані буде прочитано зі стандартного вхідного джерела даних."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "Зазвичай, найцікавішою інформацією є розпакований розмір та розмір словника. Розпакований розмір може бути показано, лише якщо файл записано у непотоковому варіанті формату B<.lzma>. Об'єм пам'яті, потрібний для розпаковування файла, складає декілька десятків кілобайтів плюс розмір словника."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> включено до XZ Utils в основному для зворотної сумісності із LZMA Utils."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "ВАДИ"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> використовує B<MB>, хоча правильним суфіксом мав би бути B<MiB> (2^20 байтів). Так зроблено, щоб зберегти сумісність виведених даних із LZMA Utils."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "2025-03-06"
msgstr "6 березня 2025 року"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff — порівняння стиснених файлів"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xzcmp> [I<параметр...>] I<файл1> [I<файл2>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr "B<xzdiff> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzcmp> \\&...  (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzdiff> \\&...  (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr "B<xzcmp> і B<xzdiff> порівнюють нестиснені дані двох файлів. Нестиснені дані та параметри буде передано B<cmp>(1) або B<diff>(1), якщо не вказано B<--help> або B<--version>."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr "Якщо вказано одразу I<файл1> і I<файл2>, це можуть бути нестиснені файли або файли у форматах, які може бути розпаковано за допомогою B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) або B<lz4>(1). Потрібні команди розпаковування визначаються з суфіксів назв файлів I<файл1> і I<файл2>. Файли із невідомими програмі суфіксами вважатимуться або нестисненими або такими, що мають формат, дані у якому може розпаковувати B<xz>(1)."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr "Якщо вказано лише одну назву файла, I<файл1> повинен мати суфікс підтримуваного формату стискання, а назвою I<файл2> має бути назва I<файл1> з вилученим суфіксом формату стискання."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Працездатність команд B<lzcmp> і B<lzdiff> забезпечено для зворотної сумісності із LZMA Utils. Ці команди вважаються застарілими, їх буде вилучено у майбутній версії комплекту програм."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr "Якщо станеться помилка розпаковування, станом виходу буде B<2>. Інакше, станом виходу буде B<cmp>(1) або буде використано B<diff>(1)."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr "xzgrep — пошук взірців у ймовірно стиснених файлах"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xzgrep> [I<параметр...>] [I<список_взірців>] [I<файл...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&...  (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&...  (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&...  (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr "B<xzgrep> викликає B<grep>(1) для розпакованих даних файлів. Формати I<файлів> визначатимуться з суфіксів назв файлів. Будь-який I<файл> із суфіксом назви, підтримку якого передбачено у B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) або B<lz4>(1), буде розпаковано; усі інші файли вважатимуться нестисненими."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr "Якщо не вказано аргументу I<файли> або замість I<файл> вказано B<->, дані буде прочитано зі стандартного джерела вхідних даних. При читанні зі стандартного джерела буде розпаковано лише файли, підтримку яких передбачено у B<xz>(1). Інші файли вважатимуться такими, що вже перебувають у нестисненому форматі."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr "Передбачено підтримку більшості I<параметрів> B<grep>(1). Втім, підтримки цих параметрів не передбачено:"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-r>, B<--recursive>"
msgstr "B<-r>, B<--recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-R>, B<--dereference-recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-d>, B<--directories=>I<дія>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-Z>, B<--null>"
msgstr "B<-Z>, B<--null>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-z>, B<--null-data>"
msgstr "B<-z>, B<--null-data>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--include=>I<glob>"
msgstr "B<--include=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr "B<--exclude=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-from=>I<file>"
msgstr "B<--exclude-from=>I<файл>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr "B<--exclude-dir=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr "B<xzegrep> є альтернативним записом B<xzgrep -E>.  B<xzfgrep> є альтернативним записом B<xzgrep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Працездатність команд B<lzgrep>, B<lzegrep> і B<lzfgrep> забезпечено для зворотної сумісності із LZMA Utils. Ці команди вважаються застарілими, їх буде вилучено у майбутній версії комплекту програм."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "В одному з файлів вхідних даних знайдено принаймні одну відповідність. Помилок не сталося."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "У жодному з файлів вхідних даних не знайдено відповідника. Не сталося ніяких помилок."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "Сталася одна або декілька помилок. Невідомо, чи було знайдено відповідники критерію пошуку."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr "Якщо для змінної середовища B<GREP> встановлено непорожнє значення, його буде використано замість B<grep>(1), B<grep -E> або B<grep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless — перегляд стиснених xz або lzma (текстових) файлів"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<файл>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<файл>...] (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr "B<xzless> є фільтром, який показує текст зі стиснених файлів у терміналі. Файли, підтримку обробки яких передбачено у B<xz>(1), буде розпаковано; інші файли вважатимуться нестисненими. Якщо не вказано жодного I<файла>, B<xzless> читатиме дані зі стандартного джерела вхідних даних."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "Для показу виведених даних B<xzless> використовує B<less>(1). На відміну від B<xzmore>, вибір програми для поділу на сторінки не можна змінити за допомогою змінної середовища. Команди засновано на B<more>(1) і B<vi>(1). За допомогою команд можна просуватися назад і вперед даними та шукати дані. Щоб дізнатися більше, ознайомтеся із підручником з B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Команду B<lzless> реалізовано для забезпечення зворотної сумісності з LZMA Utils. Ця команда вважається застарілою, її буде вилучено у майбутній версії комплекту програм."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Список символів, які є особливими символами командної оболонки. Встановлюється B<xzless>, якщо його ще не встановлено у середовищі."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Має значення рядка команди для виклику засобу розпаковування B<xz>(1) для обробки вхідних файлів B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore — перегляд стиснених xz або lzma (текстових) файлів"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzmore> [I<файл>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzmore> [I<файл>...] (ЗАСТАРІЛО)"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr "B<xzmore> показує текст зі стиснених файлів у терміналі за допомогою B<more>(1). Файли, підтримку яких передбачено у B<xz>(1), буде розпаковано; інші файли вважатимуться вже наданими у розпакованій формі. Якщо не вказано аргументу I<файли>, B<xzmore> читатиме дані зі стандартного джерела даних. Див. підручник з B<more>(1), щоб дізнатися більше про клавіатурні команди."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr "Зауважте, що гортання у зворотному напрямку може бути неможливим через реалізацію B<more>(1). Причиною є те, що B<xzmore> використовує канал для передавання розпакованих даних B<more>(1). B<xzless>(1) використовує B<less>(1), можливості якої є ширшими."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Команду B<lzmore> реалізовано для забезпечення зворотної сумісності з LZMA Utils. Ця команда вважається застарілою, її буде вилучено у майбутній версії комплекту програм."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr "B<PAGER>"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr "Якщо встановлено значення B<PAGER>, значення буде використано для засобу поділу на сторінки, замість B<more>(1)."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
