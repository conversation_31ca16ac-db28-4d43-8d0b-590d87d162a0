# SPDX-License-Identifier: 0BSD
#
# German translation for xz.
# This file is published under the BSD Zero Clause License.
# <PERSON> <<EMAIL>>, 2010.
# <PERSON> <<EMAIL>>, 2015.
# <PERSON> <<EMAIL>>, 2019, 2022-2025.
msgid ""
msgstr ""
"Project-Id-Version: xz 5.7.1-dev1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2025-01-26 11:02+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Lokalize 24.12.1\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Ungültiges Argument für --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Zu viele Argumente für --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "In --block-list fehlt die Blockgröße nach der Filterkettennummer »%c:«"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 kann nur das letzte Element in --block-list sein"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Unbekanntes Dateiformat"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Integritätsprüfungstyp nicht unterstützt"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Nur eine Datei kann als Argument für »--files« oder »--files0« angegeben werden."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Die Umgebungsvariable %s enthält zu viele Argumente"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Die Unterstützung für Kompression wurde zum Zeitpunkt der Erstellung deaktiviert"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Die Unterstützung für Dekompression wurde zum Zeitpunkt der Erstellung deaktiviert"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Kompression von lzip-Dateien (.lz) wird nicht unterstützt"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list wird ignoriert, außer wenn in das .xz-Format komprimiert wird"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Mit --format=raw ist --suffix=.SUF notwendig, falls nicht in die Standardausgabe geschrieben wird"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Maximal vier Filter möglich"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Fehler in der Option --filters%s=FILTER:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Die Speicherbedarfsbegrenzung ist für die gegebene Filter-Konfiguration zu niedrig."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "Filterkette %u wird von --block-list verwendet, wurde aber nicht mit --filters%u= angegeben"

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Verwendung einer Voreinstellung im Roh-Modus wird nicht empfohlen."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "Die genauen Optionen der Voreinstellung können zwischen Softwareversionen variieren."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Das .lzma-Format unterstützt nur den LZMA1-Filter"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 kann nicht mit dem .xz-Format verwendet werden"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "Die Filterkette %u ist inkompatibel zu --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Wegen --flush-timeout wird auf den Einzelthread-Modus umgeschaltet"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Nicht unterstützte Optionen in Filterkette %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Bis zu %<PRIu32> Threads werden benutzt."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Filterkette oder Filteroptionen werden nicht unterstützt"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Dekompression wird %s MiB Speicher brauchen."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Anzahl der Threads wurde von %s auf %s reduziert, um die Speicherbedarfsbegrenzung von %s MiB nicht zu übersteigen"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Anzahl der Threads wurde von %s auf einen reduziert. Die automatische Begrenzung des Speicherverbrauchs auf %s MiB wird immer noch überschritten. %s MiB an Speicher sind erforderlich. Es wird dennoch fortgesetzt."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Es wurde in den Einzelthread-Modus gewechselt, um die Speicherbedarfsbegrenzung von %s MiB nicht zu übersteigen"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Die LZMA%c-Wörterbuchgröße wurde von %s MiB auf %s MiB angepasst, um die Speicherbedarfsbegrenzung von %s MiB nicht zu übersteigen"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Die LZMA%c-Wörterbuchgröße für --filters%u wurde von %s MiB auf %s MiB angepasst, um die Speicherbedarfsbegrenzung von %s MiB nicht zu übersteigen"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Fehler beim Wechsel zur Filterkette %u: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Fehler beim Erzeugen der Pipeline: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: poll() ist fehlgeschlagen: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Datei scheint verschoben worden zu sein, daher wird sie nicht gelöscht"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Löschen nicht möglich: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Dateieigentümer kann nicht gesetzt werden: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Dateigruppe kann nicht gesetzt werden: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Zugriffsrechte können nicht gesetzt werden: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Fehler beim Synchronisieren der Datei: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Fehler beim Synchronisieren des Verzeichnisses der Datei: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Dateistatus-Markierungen können nicht aus der Standardeingabe ermittelt werden: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Ist ein symbolischer Link, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Ist ein Verzeichnis, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Keine reguläre Datei, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Datei hat das setuid- oder setgid-Bit gesetzt, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Datei hat sticky-Bit gesetzt, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Eingabedatei hat mehr als einen harten Link, wird übersprungen"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Leerer Dateiname, wird übersprungen"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Fehler beim Wiederherstellen der Status-Markierungen für die Standardeingabe: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Status-Markierungen der Standardausgabe können nicht ermittelt werden: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Fehler beim Öffnen des Verzeichnisses: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Destination is not a regular file"
msgstr "%s: Ziel ist keine reguläre Datei"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Fehler beim Wiederherstellen der O_APPEND-Markierungen für die Standardausgabe: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Fehler beim Schließen der Datei: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Positionierungsfehler beim Versuch, eine Sparse-Datei (dünnbesetzte Datei) zu erzeugen: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Lesefehler: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Fehler beim Durchsuchen der Datei: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Unerwartetes Ende der Datei"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Schreibfehler: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Deaktiviert"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Gesamtmenge physischer Speicher (RAM):"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Anzahl der Prozessor-Threads:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Kompression:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Dekompression:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Multithread-Dekompression:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Vorgabe für -T0:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Hardware-Information:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Speicherbedarfsbegrenzung:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Datenströme:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Blöcke:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Größe komprimiert:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Größe unkomprimiert:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Verhältnis:"

#: src/xz/list.c
msgid "Check:"
msgstr "Prüfung:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Datenstromauffüllung:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Benötigter Speicher:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Größe in Köpfen:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Anzahl Dateien:"

#: src/xz/list.c
msgid "Stream"
msgstr "Datenstrom"

#: src/xz/list.c
msgid "Block"
msgstr "Block"

#: src/xz/list.c
msgid "Blocks"
msgstr "Blöcke"

#: src/xz/list.c
msgid "CompOffset"
msgstr "KompVers"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "UnkompVers"

#: src/xz/list.c
msgid "CompSize"
msgstr "KompGröße"

#: src/xz/list.c
msgid "UncompSize"
msgstr "UnkompGröße"

#: src/xz/list.c
msgid "TotalSize"
msgstr "Gesamt"

#: src/xz/list.c
msgid "Ratio"
msgstr "Verhältnis"

#: src/xz/list.c
msgid "Check"
msgstr "Prüfung"

#: src/xz/list.c
msgid "CheckVal"
msgstr "Prüfwert"

#: src/xz/list.c
msgid "Padding"
msgstr "Auffüllung"

#: src/xz/list.c
msgid "Header"
msgstr "Kopf"

#: src/xz/list.c
msgid "Flags"
msgstr "Flags"

#: src/xz/list.c
msgid "MemUsage"
msgstr "SpeichVerb"

#: src/xz/list.c
msgid "Filters"
msgstr "Filter"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Keine"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Unbek.2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Unbek.3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Unbek.5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Unbek.6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Unbek.7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Unbek.8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Unbek.9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Unbek.11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Unbek.12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Unbek.13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Unbek.14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Unbek.15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Datei ist leer"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Zu klein, um eine gültige .xz-Datei zu sein"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr " Str.  Blöcke       Kompr.     Unkompr.  Verh.  Check   Dateiname"

#: src/xz/list.c
msgid "Yes"
msgstr "Ja"

#: src/xz/list.c
msgid "No"
msgstr "Nein"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr "Minimal erforderliche XZ Utils-Version:"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s Datei\n"
msgstr[1] "%s Dateien\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Gesamt:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list funktioniert nur mit .xz-Dateien (--format=xz oder --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Versuchen Sie »lzmainfo« mit .lzma-Dateien."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list unterstützt kein Lesen aus der Standardeingabe"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Fehler beim Lesen der Dateinamen: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Unerwartetes Ende der Eingabe beim Lesen der Dateinamen"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Null-Zeichen beim Lesen der Dateinamen gefunden; meinten Sie »--files0« statt »--files«?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Kompression und Dekompression mit --robot wird noch nicht unterstützt."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Lesen der Daten aus der Standardeingabe ist nicht möglich, wenn die Dateinamen auch aus der Standardeingabe gelesen werden"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Interner Fehler (Bug)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Signalroutine kann nicht gesetzt werden"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Keine Integritätsprüfung; Integrität der Datei wird nicht überprüft"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Typ der Integritätsprüfung wird nicht unterstützt; Integrität der Datei wird nicht überprüft"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Speicherbedarfsbegrenzung erreicht"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Dateiformat nicht erkannt"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Optionen nicht unterstützt"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Komprimierte Daten sind beschädigt"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Unerwartetes Ende der Eingabe"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s MiB Speicher wird benötigt. Die Begrenzung ist deaktiviert."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s MiB Speicher wird benötigt. Die Begrenzung ist %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Filterkette: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "Versuchen Sie »%s --help« für mehr Informationen."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr "Fehler bei der Ausgabe des Hilfetextes (Fehlercode %d)"

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr "Aufruf: %s [OPTION] … [DATEI] …\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr "Komprimiert oder dekomprimiert .xz-DATEI(EN)."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr ""
"Obligatorische Argumente für lange Optionen sind auch für kurze Optionen\n"
"zwingend."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation mode:"
msgstr "Aktionsmodus:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force compression"
msgstr "Kompression erzwingen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force decompression"
msgstr "Dekompression erzwingen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr "Integrität der komprimierten Datei überprüfen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr "Informationen zu .xz-Dateien auflisten"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation modifiers:"
msgstr "Aktionsmodifikatoren:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr "Eingabedateien beibehalten (nicht löschen)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr "Überschreiben der Ausgabedatei erzwingen und Links (de)komprimieren"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "write to standard output and don't delete input files"
msgstr "In die Standardausgabe schreiben und die Eingabedateien nicht löschen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr "Ausgabedatei nicht auf dem Speichergerät synchronisieren, bevor die Eingabedatei gelöscht wird"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "Nur den ersten Datenstrom dekomprimieren und stillschweigend mögliche weitere Eingabedaten ignorieren"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr "Beim Dekomprimieren keine Sparse-Dateien erzeugen"

#: src/xz/message.c
msgid ".SUF"
msgstr ".END"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr "».END« als Endung für komprimierte Dateien benutzen"

#: src/xz/message.c
msgid "FILE"
msgstr "DATEI"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr "Zu verarbeitende Dateinamen aus DATEI lesen; falls keine DATEI angegeben wurde, werden Dateinamen aus der Standardeingabe gelesen. Dateinamen müssen durch einen Zeilenumbruch voneinander getrennt werden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr "Wie --files, aber das Null-Zeichen wird als Trenner benutzt"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr "Grundlegende Optionen für Dateiformat und Kompression:"

#: src/xz/message.c
msgid "FORMAT"
msgstr "FORMAT"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr "Dateiformat zur Kodierung oder Dekodierung; mögliche Werte sind »auto« (Voreinstellung), »xz«, »lzma«, »lzip« und »raw«"

#: src/xz/message.c
msgid "NAME"
msgstr "NAME"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr "Typ der Integritätsprüfung: »none« (Vorsicht!), »crc32«, »crc64« (Voreinstellung) oder »sha256«"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr "Integritätsprüfung beim Dekomprimieren nicht ausführen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr "Kompressionseinstellung; Voreinstellung ist 6. Beachten Sie den Speicherbedarf des Kompressors *und* des Dekompressors, wenn Sie 7-9 benutzen!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr "Versuchen, durch stärkere CPU-Auslastung das Kompressionsverhältnis zu verbessern. Dies beeinflusst nicht den Speicherbedarf des Dekompressors."

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr "ANZAHL"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr "Höchstens die angegebene ANZAHL Threads erzeugen; die Voreinstellung ist 0, wobei so viele Threads erzeugt werden, wie Prozessorkerne vorhanden sind"

#: src/xz/message.c
msgid "SIZE"
msgstr "GRÖẞE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr "Einen neuen .xz-Block nach der angegebenen GRÖẞE der Eingabe in Bytes beginnen; benutzen Sie diese Option, um die Blockgröße für die Kompression mit mehreren Threads zu setzen"

#: src/xz/message.c
msgid "BLOCKS"
msgstr "BLÖCKE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr "Einen neuen .xz-Block gemäß der angegebenen, durch Kommata getrennten Intervalle an unkomprimierten Daten beginnen; optional kann eine Filterkettennummer (0-9) angegeben werden, gefolgt von einem »:« und der unkomprimierten Datengröße"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr "Wenn beim Komprimieren mehr als die angegebene ANZAHL an Millisekunden seit der letzten Leerungsaktion vergangen ist und das Lesen von zusätzlichen Eingabedaten den Prozess blockieren würde, dann werden alle noch ausstehenden Daten geschrieben"

#: src/xz/message.c
msgid "LIMIT"
msgstr "BEGRENZUNG"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr "Speicherbedarfsbegrenzung für Kompression, Dekompression oder beides setzen; die BEGRENZUNG wird in Bytes oder als Prozentsatz RAM angegeben. Geben Sie 0 an, um die Grundeinstellungen zu verwenden."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "Wenn die Kompressionseinstellungen die Speicherbedarfsbegrenzung übersteigen, wird ein Fehler ausgegeben, statt die Einstellungen nach unten anzupassen."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr "Benutzerdefinierte Filterkette für Kompression (alternativ zur Verwendung von Voreinstellungen):"

#: src/xz/message.c
msgid "FILTERS"
msgstr "FILTER"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr "Die Filterkette anhand der Liblzma-Filterkettensyntax setzen; mit --filters-help erhalten Sie weitere Informationen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr "Zusätzliche Filter anhand der Liblzma-Filterkettensyntax setzen, die mit --block-list verwendet werden sollen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr "Weitere Informationen über die Liblzma-Filterkettensyntax anzeigen und beenden."

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr "OPTIONEN"

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr "LZMA1 oder LZMA2; OPTIONEN ist eine durch Kommata getrennte Liste bestehend aus den folgenden Optionen (zulässige Werte; Voreinstellung):"

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr "ZAHL"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr "Optionen auf Voreinstellungsstufe zurücksetzen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr "Wörterbuchgröße"

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr "Anzahl der Literal-Kontext-Bits"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr "Anzahl der Literal-Positions-Bits"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr "Anzahl der Positions-Bits"

#: src/xz/message.c
msgid "MODE"
msgstr "MODUS"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression mode"
msgstr "Kompressionsmodus"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr "Nice-Länge eines Treffers"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr "Algorithmus zum Auffinden von Übereinstimmungen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr "Maximale Suchtiefe; 0=automatisch (Voreinstellung)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr "x86 BCJ-Filter (32-bit und 64-bit)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr "ARM-BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr "ARM-Thumb-BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr "ARM64-BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr "PowerPC-BCJ-Filter (nur Big Endian)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr "IA64-(Itanium-)BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr "SPARC-BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr "RISC-V-BCJ-Filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr "Zulässige Optionen für alle BCJ-Filter:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr "Startversatz für Konversion (Voreinstellung=0)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr "Delta-Filter; zulässige Optionen (gültige Werte; Voreinstellung):"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr "Abstand zwischen den Bytes, die voneinander subtrahiert werden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Other options:"
msgstr "Andere Optionen:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr "Warnungen unterdrücken; wird diese Option zweimal angegeben, werden auch Fehlermeldungen unterdrückt"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr "Ausführlicher Modus; wird diese Option zweimal angegeben, erfolgen noch ausführlichere Ausgaben"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr "Warnungen verändern nicht den Exit-Status"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use machine-parsable messages (useful for scripts)"
msgstr "Maschinenlesbare Meldungen ausgeben (nützlich für Skripte)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "Gesamtspeicher (RAM) sowie die gegenwärtig aktive Speicherbedarfsbegrenzung anzeigen und das Programm beenden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr "Kurze Hilfe anzeigen (zeigt nur die grundlegenden Optionen)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr "Diese lange Hilfe anzeigen und das Programm beenden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr "Diese kurze Hilfe anzeigen und das Programm beenden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr "Die lange Hilfe (und damit auch fortgeschrittene Optionen) anzeigen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the version number and exit"
msgstr "Versionsnummer anzeigen und beenden"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "With no FILE, or when FILE is -, read standard input."
msgstr "Wenn DATEI nicht angegeben wurde oder DATEI gleich - ist, dann wird aus der Standardeingabe gelesen."

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "Melden Sie Fehler an <%s> (auf Englisch oder Finnisch)."

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s home page: <%s>"
msgstr "%s-Homepage: <%s>"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "DIES IST EINE NICHT FÜR DEN PRODUKTIVBETRIEB GEEIGNETE ENTWICKLERVERSION."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr "Filterketten werden durch Setzen der Optionen --filters=FILTER oder --filters1=FILTER … --filters9=FILTER definiert. Die Filter können innerhalb der Kette durch Leerzeichen oder »--« getrennt werden. Alternativ kann eine Voreinstellung %s anstelle einer Filterkette verwendet werden."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "Folgende Filterkettem und Filteroptionen werden unterstützt:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "Optionen müssen in der Form »Name=Wert« gegeben werden, getrennt durch Kommata"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Ungültiger Optionsname"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Invalid option value"
msgstr "Ungültiger Optionswert"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "LZMA1/LZMA2-Voreinstellung wird nicht unterstützt: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Die Summe aus lc und lp darf höchstens 4 sein"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Dateiname hat unbekanntes Suffix, wird übersprungen"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Datei hat bereits das Suffix »%s«, wird übersprungen"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Ungültige Dateiendung"

#: src/xz/util.c src/liblzma/common/string_conversion.c
msgid "Value is not a non-negative decimal integer"
msgstr "Wert ist keine nicht-negative dezimale Ganzzahl"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Ungültige Einheit"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Gültige Einheiten sind »KiB« (2^10), »MiB« (2^20) und »GiB« (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Wert der Option »%s« muss im Bereich [%<PRIu64>, %<PRIu64>] sein"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Komprimierte Daten können nicht vom Terminal gelesen werden"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Komprimierte Daten können nicht auf das Terminal geschrieben werden"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr "Aufruf: %s [--help] [--version] [DATEI] …\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr "Im .lzma-Dateikopf gespeicherte Informationen anzeigen."

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "Die Datei ist zu klein, um eine .lzma-Datei zu sein"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Keine .lzma-Datei"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Schreiben in die Standardausgabe fehlgeschlagen"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Unbekannter Fehler"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported preset"
msgstr "Voreinstellung wird nicht unterstützt"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported flag in the preset"
msgstr "Nicht unterstützter Schalter in der Voreinstellung"

#: src/liblzma/common/string_conversion.c
msgid "Unknown option name"
msgstr "Unbekannter Optionsname"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr "Optionswert darf nicht leer sein"

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr "Wert ist außerhalb des zulässigen Bereichs"

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr "Diese Option unterstützt keine Einheiten-Endungen"

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "Ungültige Einheit (KiB, MiB oder GiB)"

#: src/liblzma/common/string_conversion.c
msgid "Unknown filter name"
msgstr "Unbekannter Filtername"

#: src/liblzma/common/string_conversion.c
msgid "This filter cannot be used in the .xz format"
msgstr "Dieser Filter kann nicht im .xz-Format verwendet werden"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr "Speicherzuweisung ist fehlgeschlagen"

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr "Leere Zeichenkette ist nicht erlaubt; versuchen Sie »6«, wenn der Vorgabewert erwünscht ist"

#: src/liblzma/common/string_conversion.c
msgid "The maximum number of filters is four"
msgstr "Maximal vier Filter sind möglich"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr "Filtername fehlt"

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr "Unzulässige Filterkette (»lzma2« fehlt am Ende?)"
