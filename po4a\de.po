# SPDX-License-Identifier: 0BSD
# German translation for xz-man.
# This file is published under the BSD Zero Clause License.
# Copyright (C) The XZ Utils authors and contributors
#
# <PERSON> <<EMAIL>>, 2015, 2019-2020, 2022-2025.
msgid ""
msgstr ""
"Project-Id-Version: xz-man 5.8.0-pre1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-03-08 14:50+0200\n"
"PO-Revision-Date: 2025-03-10 16:39+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Lokalize 24.12.1\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr "8. März 2025"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "XZ-Dienstprogramme"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "BEZEICHNUNG"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat - .xz- und .lzma-Dateien komprimieren oder dekomprimieren"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "ÜBERSICHT"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<Option…>] [I<Datei…>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "BEFEHLSALIASE"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> ist gleichbedeutend mit B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> ist gleichbedeutend mit B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> ist gleichbedeutend mit B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> ist gleichbedeutend mit B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> ist gleichbedeutend mit B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Wenn Sie Skripte schreiben, die Dateien dekomprimieren, sollten Sie stets den Namen B<xz> mit den entsprechenden Argumenten (B<xz -d> oder B<xz -dc>) anstelle der Namen B<unxz> und B<xzcat> verwenden."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "BESCHREIBUNG"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> ist ein Allzweckwerkzeug zur Datenkompression, dessen Befehlszeilensyntax denen von B<gzip>(1) und B<bzip2>(1) ähnelt. Das native Dateiformat ist das B<.xz>-Format, aber das veraltete, von den LZMA-Dienstprogrammen verwendete Format sowie komprimierte Rohdatenströme ohne Containerformat-Header werden ebenfalls unterstützt. Außerdem wird die Dekompression des von B<lzip> verwendeten B<.lz>-Formats unterstützt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> komprimiert oder dekomprimiert jede I<Datei> entsprechend des gewählten Vorgangsmodus. Falls entweder B<-> oder keine Datei angegeben ist, liest B<xz> aus der Standardeingabe und leitet die verarbeiteten Dateien in die Standardausgabe. Wenn die Standardausgabe kein Terminal ist, verweigert B<xz> das Schreiben komprimierter Daten in die Standardausgabe. Dabei wird eine Fehlermeldung angezeigt und die I<Datei> übersprungen. Ebenso verweigert B<xz> das Lesen komprimierter Daten aus der Standardeingabe, wenn diese ein Terminal ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "I<Dateien>, die nicht als B<-> angegeben sind, werden in eine neue Datei geschrieben, deren Name aus dem Namen der Quell-I<Datei> abgeleitet wird (außer wenn B<--stdout> angegeben ist):"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "Bei der Kompression wird das Suffix des Formats der Zieldatei (B<.xz> oder B<.lzma>) an den Namen der Quelldatei angehängt und so der Name der Zieldatei gebildet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "Bei der Dekompression wird das Suffix B<.xz>, B<.lzma> oder B<.lz> vom Dateinamen entfernt und so der Name der Zieldatei gebildet. Außerdem erkennt B<xz> die Suffixe B<.txz> und B<.tlz> und ersetzt diese durch B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Wenn die Zieldatei bereits existiert, wird eine Fehlermeldung angezeigt und die I<Datei> übersprungen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "Außer beim Schreiben in die Standardausgabe zeigt B<xz> eine Warnung an und überspringt die I<Datei>, wenn eine der folgenden Bedingungen zutreffend ist:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "Die I<Datei> ist keine reguläre Datei. Symbolischen Verknüpfungen wird nicht gefolgt und diese daher nicht zu den regulären Dateien gezählt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "Die I<Datei> hat mehr als eine harte Verknüpfung."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "Für die I<Datei> ist das »setuid«-, »setgid«- oder »sticky«-Bit gesetzt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "Der Aktionsmodus wird auf Kompression gesetzt und die I<Datei> hat bereits das Suffix des Zieldateiformats (B<.xz> oder B<.txz> beim Komprimieren in das B<.xz>-Format und B<.lzma> oder B<.tlz> beim Komprimieren in das B<.lzma>-Format)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "Der Aktionsmodus wird auf Dekompression gesetzt und die I<Datei> hat nicht das Suffix eines der unterstützten Zieldateiformate (B<.xz>, B<.txz>, B<.lzma>, B<.tlz> oder B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Nach erfolgreicher Kompression oder Dekompression der I<Datei> kopiert B<xz> Eigentümer, Gruppe, Zugriffsrechte, Zugriffszeit und Änderungszeit aus der Ursprungs-I<Datei> in die Zieldatei. Sollte das Kopieren der Gruppe fehlschlagen, werden die Zugriffsrechte so angepasst, dass jenen Benutzern der Zugriff auf die Zieldatei verwehrt bleibt, die auch keinen Zugriff auf die Ursprungs-I<Datei> hatten. Das Kopieren anderer Metadaten wie Zugriffssteuerlisten oder erweiterter Attribute wird von B<xz> noch nicht unterstützt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Sobald die Zieldatei erfolgreich geschlossen wurde, wird die Ursprungs-I<Datei> entfernt. Dies wird durch die Option B<--keep> verhindert. Die Ursprungs-I<Datei> wird niemals entfernt, wenn die Ausgabe in die Standardausgabe geschrieben wird oder falls ein Fehler auftritt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "Durch Senden der Signale B<SIGINFO> oder B<SIGUSR1> an den B<xz>-Prozess werden Fortschrittsinformationen in den Fehlerkanal der Standardausgabe geleitet. Dies ist nur eingeschränkt hilfreich, wenn die Standardfehlerausgabe ein Terminal ist. Mittels B<--verbose> wird ein automatisch aktualisierter Fortschrittsanzeiger angezeigt."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Speicherbedarf"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "In Abhängigkeit von den gewählten Kompressionseinstellungen bewegt sich der Speicherverbrauch zwischen wenigen hundert Kilobyte und mehreren Gigabyte. Die Einstellungen bei der Kompression einer Datei bestimmen dabei den Speicherbedarf bei der Dekompression. Die Dekompression benötigt üblicherweise zwischen 5\\ % und 20\\ % des Speichers, der bei der Kompression der Datei erforderlich war. Beispielsweise benötigt die Dekompression einer Datei, die mit B<xz -9> komprimiert wurde, gegenwärtig etwa 65\\ MiB Speicher. Es ist jedoch auch möglich, dass B<.xz>-Dateien mehrere Gigabyte an Speicher zur Dekompression erfordern."

# cripple → lahmlegen...? War mir hier zu sehr Straßenslang.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "Insbesondere für Benutzer älterer Systeme wird eventuell ein sehr großer Speicherbedarf ärgerlich sein. Um unangenehme Überraschungen zu vermeiden, verfügt B<xz> über eine eingebaute Begrenzung des Speicherbedarfs, die allerdings in der Voreinstellung deaktiviert ist. Zwar verfügen einige Betriebssysteme über eingebaute Möglichkeiten zur prozessabhängigen Speicherbegrenzung, doch diese sind zu unflexibel (zum Beispiel kann B<ulimit>(1) beim Begrenzen des virtuellen Speichers B<mmap>(2) beeinträchtigen)."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "Die Begrenzung des Speicherbedarfs kann mit der Befehlszeilenoption B<--memlimit=>I<Begrenzung> aktiviert werden. Oft ist es jedoch bequemer, die Begrenzung durch Setzen der Umgebungsvariable B<XZ_DEFAULTS> standardmäßig zu aktivieren, zum Beispiel B<XZ_DEFAULTS=--memlimit=150MiB>. Die Begrenzungen können getrennt für Kompression und Dekompression mittels B<--memlimit-compress=>I<Begrenzung> und B<--memlimit-decompress=>I<Begrenzung> festgelegt werden. Die Verwendung einer solchen Option außerhalb der Variable B<XZ_DEFAULTS> ist kaum sinnvoll, da B<xz> in einer einzelnen Aktion nicht gleichzeitig Kompression und Dekompression ausführen kann und B<--memlimit=>I<Begrenzung> (oder B<-M> I<Begrenzung>) lässt sich einfacher in der Befehlszeile eingeben."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Wenn die angegebene Speicherbegrenzung bei der Dekompression überschritten wird, schlägt der Vorgang fehl und B<xz> zeigt eine Fehlermeldung an. Wird die Begrenzung bei der Kompression überschritten, dann versucht B<xz> die Einstellungen entsprechend anzupassen, außer wenn B<--format=raw> oder B<--no-adjust> angegeben ist. Auf diese Weise schlägt die Aktion nicht fehl, es sei denn, die Begrenzung wurde sehr niedrig angesetzt. Die Anpassung der Einstellungen wird schrittweise vorgenommen, allerdings entsprechen die Schritte nicht den Voreinstellungen der Kompressionsstufen. Das bedeutet, wenn beispielsweise die Begrenzung nur geringfügig unter den Anforderungen für B<xz -9> liegt, werden auch die Einstellungen nur wenig angepasst und nicht vollständig herunter zu den Werten für B<xz -8>"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Verkettung und Auffüllung von .xz-Dateien"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "Es ist möglich, B<.xz>-Dateien direkt zu verketten. Solche Dateien werden von B<xz> genauso dekomprimiert wie eine einzelne B<.xz>-Datei."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "Es ist weiterhin möglich, eine Auffüllung zwischen den verketteten Teilen oder nach dem letzten Teil einzufügen. Die Auffüllung muss aus Null-Bytes bestehen und deren Größe muss ein Vielfaches von vier Byte sein. Dies kann zum Beispiel dann vorteilhaft sein, wenn die B<.xz>-Datei auf einem Datenträger gespeichert wird, dessen Dateisystem die Dateigrößen in 512-Byte-Blöcken speichert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Verkettung und Auffüllung sind für B<.lzma>-Dateien oder Rohdatenströme nicht erlaubt."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "OPTIONEN"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Ganzzahlige Suffixe und spezielle Werte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "An den meisten Stellen, wo ein ganzzahliges Argument akzeptiert wird, kann ein optionales Suffix große Ganzzahlwerte einfacher darstellen. Zwischen Ganzzahl und dem Suffix dürfen sich keine Leerzeichen befinden."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "multipliziert die Ganzzahl mit 1.024 (2^10). B<Ki>, B<k>, B<kB>, B<K> und B<KB> werden als Synonyme für B<KiB> akzeptiert."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "multipliziert die Ganzzahl mit 1.048.576 (2^20). B<Mi>, B<m>, B<M> und B<MB> werden als Synonyme für B<MiB> akzeptiert."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "multipliziert die Ganzzahl mit 1.073.741.824 (2^30). B<Gi>, B<g>, B<G> und B<GB> werden als Synonyme für B<GiB> akzeptiert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "Der spezielle Wert B<max> kann dazu verwendet werden, um den von der jeweiligen Option akzeptierten maximalen Ganzzahlwert anzugeben."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Aktionsmodus"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Falls mehrere Aktionsmodi angegeben sind, wird der zuletzt angegebene verwendet."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Kompression. Dies ist der voreingestellte Aktionsmodus, sofern keiner angegeben ist und auch kein bestimmter Modus aus dem Befehlsnamen abgeleitet werden kann (der Befehl B<unxz> impliziert zum Beispiel B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Nach erfolgreicher Kompression wird die Quelldatei gelöscht, außer wenn in die Standardausgabe geschrieben wird oder B<--keep> angegeben wurde."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Dekomprimieren. Nach erfolgreicher Dekompression wird die Quelldatei gelöscht, außer wenn in die Standardausgabe geschrieben wird oder B<--keep> angegeben wurde."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "prüft die Integrität der komprimierten I<Dateien>. Diese Option ist gleichbedeutend mit B<--decompress --stdout>, außer dass die dekomprimierten Daten verworfen werden, anstatt sie in die Standardausgabe zu leiten. Es werden keine Dateien erstellt oder entfernt."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "gibt Informationen zu den komprimierten I<Dateien> aus. Es werden keine unkomprimierten Dateien ausgegeben und keine Dateien angelegt oder entfernt. Im Listenmodus kann das Programm keine komprimierten Daten aus der Standardeingabe oder anderen nicht durchsuchbaren Quellen lesen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "Die Liste zeigt in der Standardeinstellung grundlegende Informationen zu den I<Dateien> an, zeilenweise pro Datei. Detailliertere Informationen erhalten Sie mit der Option B<--verbose>. Wenn Sie diese Option zweimal angeben, werden noch ausführlichere Informationen ausgegeben. Das kann den Vorgang allerdings deutlich verlangsamen, da die Ermittlung der zusätzlichen Informationen zahlreiche Suchvorgänge erfordert. Die Breite der ausführlichen Ausgabe übersteigt 80 Zeichen, daher könnte die Weiterleitung in beispielsweise\\& B<less\\ -S> sinnvoll sein, falls das Terminal nicht breit genug ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "Die exakte Ausgabe kann in verschiedenen B<xz>-Versionen und Spracheinstellungen unterschiedlich sein. Wenn eine maschinell auswertbare Ausgabe gewünscht ist, dann sollten Sie B<--robot --list> verwenden."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Aktionsattribute"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "verhindert das Löschen der Eingabedateien."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Seit der B<xz>-Version 5.2.6 wird die Kompression oder Dekompression auch dann ausgeführt, wenn die Eingabe ein symbolischer Link zu einer regulären Datei ist, mehr als einen harten Link hat oder das »setuid«-, »setgid«- oder »sticky«-Bit gesetzt ist. Die genannten Bits werden nicht in die Zieldatei kopiert. In früheren Versionen geschah dies nur mit B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Diese Option hat verschiedene Auswirkungen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Wenn die Zieldatei bereits existiert, wird diese vor der Kompression oder Dekompression gelöscht."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Die Kompression oder Dekompression wird auch dann ausgeführt, wenn die Eingabe ein symbolischer Link zu einer regulären Datei ist, mehr als einen harten Link hat oder das »setuid«-, »setgid«- oder »sticky«-Bit gesetzt ist. Die genannten Bits werden nicht in die Zieldatei kopiert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Wenn es zusammen mit B<--decompress> und B<--stdout> verwendet wird und B<xz> den Typ der Quelldatei nicht ermitteln kann, wird die Quelldatei unverändert in die Standardausgabe kopiert. Dadurch kann B<xzcat> B<--force> für Dateien, die nicht mit B<xz> komprimiert wurden, wie B<cat>(1) verwendet werden. Zukünftig könnte B<xz> neue Dateikompressionsformate unterstützen, wodurch B<xz> mehr Dateitypen dekomprimieren kann, anstatt sie unverändert in die Standardausgabe zu kopieren. Mit der Option B<--format=>I<Format> können Sie B<xz> anweisen, nur ein einzelnes Dateiformat zu dekomprimieren."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "schreibt die komprimierten oder dekomprimierten Daten in die Standardausgabe anstatt in eine Datei. Dies impliziert B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "dekomprimiert nur den ersten B<.xz>-Datenstrom und ignoriert stillschweigend weitere Eingabedaten, die möglicherweise dem Datenstrom folgen. Normalerweise führt solcher anhängender Datenmüll dazu, dass B<xz> eine Fehlermeldung ausgibt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> dekomprimiert niemals mehr als einen Datenstrom aus B<.lzma>-Dateien oder Rohdatenströmen, aber dennoch wird durch diese Option möglicherweise vorhandener Datenmüll nach der B<.lzma>-Datei oder dem Rohdatenstrom ignoriert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Diese Option ist wirkungslos, wenn der Aktionsmodus nicht B<--decompress> oder B<--test> ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr "Seit der Programmversion B<xz> 5.7.1alpha impliziert B<--single-stream> zusätzlich die Option B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "verhindert die Erzeugung von Sparse-Dateien. In der Voreinstellung versucht B<xz>, bei der Dekompression in eine reguläre Datei eine Sparse-Datei zu erzeugen, wenn die dekomprimierten Daten lange Abfolgen von binären Nullen enthalten. Dies funktioniert auch beim Schreiben in die Standardausgabe, sofern diese in eine reguläre Datei weitergeleitet wird und bestimmte Zusatzbedingungen erfüllt sind, die die Aktion absichern. Die Erzeugung von Sparse-Dateien kann Plattenplatz sparen und beschleunigt die Dekompression durch Verringerung der Ein-/Ausgaben der Platte."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "verwendet I<.suf> bei der Dekompression anstelle von B<.xz> oder B<.lzma> als Suffix für die Zieldatei. Falls nicht in die Standardausgabe geschrieben wird und die Quelldatei bereits das Suffix I<.suf> hat, wird eine Warnung angezeigt und die Datei übersprungen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "berücksichtigt bei der Dekompression zusätzlich zu Dateien mit den Suffixen B<.xz>, B<.txz>, B<.lzma>, B<.tlz> oder B<.lz> auch jene mit dem Suffix I<.suf>. Falls die Quelldatei das Suffix I<.suf> hat, wird dieses entfernt und so der Name der Zieldatei abgeleitet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "Beim Komprimieren oder Dekomprimieren von Rohdatenströmen mit B<--format=raw> muss das Suffix stets angegeben werden, außer wenn die Ausgabe in die Standardausgabe erfolgt. Der Grund dafür ist, dass es kein vorgegebenes Suffix für Rohdatenströme gibt."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<Datei>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "liest die zu verarbeitenden Dateinamen aus I<Datei>. Falls keine I<Datei> angegeben ist, werden die Dateinamen aus der Standardeingabe gelesen. Dateinamen müssen mit einem Zeilenumbruch beendet werden. Ein Bindestrich (B<->) wird als regulärer Dateiname angesehen und nicht als Standardeingabe interpretiert. Falls Dateinamen außerdem als Befehlszeilenargumente angegeben sind, werden diese vor den Dateinamen aus der I<Datei> verarbeitet."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<Datei>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Dies ist gleichbedeutend mit B<--files>[B<=>I<Datei>], außer dass jeder Dateiname mit einem Null-Zeichen abgeschlossen werden muss."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Grundlegende Dateiformat- und Kompressionsoptionen"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<Format>, B<--format=>I<Format>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "gibt das I<Format> der zu komprimierenden oder dekomprimierenden Datei an:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Dies ist die Voreinstellung. Bei der Kompression ist B<auto> gleichbedeutend mit B<xz>. Bei der Dekompression wird das Format der Eingabedatei automatisch erkannt. Beachten Sie, dass Rohdatenströme, wie sie mit B<--format=raw> erzeugt werden, nicht automatisch erkannt werden können."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Die Kompression erfolgt in das B<.xz>-Dateiformat oder akzeptiert nur B<.xz>-Dateien bei der Dekompression."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Die Kompression erfolgt in das veraltete B<.lzma>-Dateiformat oder akzeptiert nur B<.lzma>-Dateien bei der Dekompression. Der alternative Name B<alone> dient der Abwärtskompatibilität zu den LZMA-Dienstprogrammen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Akzeptiert nur B<.lz>-Dateien bei der Dekompression. Kompression wird nicht unterstützt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "Das B<.lz>-Format wird in Version 0 und der unerweiterten Version 1 unterstützt. Dateien der Version 0 wurden von B<lzip> 1.3 und älter erstellt. Solche Dateien sind nicht sehr weit verbreitet, können aber in Dateiarchiven gefunden werden, da einige Quellpakete in diesem Format veröffentlicht wurden. Es ist auch möglich, dass Benutzer alte persönliche Dateien in diesem Format haben. Die Dekompressionsunterstützung für das Format der Version 0 wurde mit der Version 1.18 aus B<lzip> entfernt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip>-Versionen ab 1.4 erstellen Dateien im Format der Version 0. Die Erweiterung »Sync Flush Marker« zur Formatversion 1 wurde in B<lzip> 1.6 hinzugefügt. Diese Erweiterung wird sehr selten verwendet und wird von B<xz> nicht unterstützt (die Eingabe wird als beschädigt erkannt)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Komprimiert oder dekomprimiert einen Rohdatenstrom (ohne Header). Diese Option ist nur für fortgeschrittene Benutzer bestimmt. Zum Dekodieren von Rohdatenströmen müssen Sie die Option B<--format=raw> verwenden und die Filterkette ausdrücklich angeben, die normalerweise in den (hier fehlenden) Container-Headern gespeichert worden wäre."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<Prüfung>, B<--check=>I<Prüfung>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "gibt den Typ der Integritätsprüfung an. Die Prüfsumme wird aus den unkomprimierten Daten berechnet und in der B<.xz>-Datei gespeichert. Diese Option wird nur bei der Kompression in das B<.xz>-Format angewendet, da das B<.lzma>-Format keine Integritätsprüfungen unterstützt. Die eigentliche Integritätsprüfung erfolgt (falls möglich), wenn die B<.xz>-Datei dekomprimiert wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Folgende Typen von I<Prüfungen> werden unterstützt:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "führt keine Integritätsprüfung aus. Dies ist eine eher schlechte Idee. Dennoch kann es nützlich sein, wenn die Integrität der Daten auf andere Weise sichergestellt werden kann."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "berechnet die CRC32-Prüfsumme anhand des Polynoms aus IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "berechnet die CRC64-Prüfsumme anhand des Polynoms aus ECMA-182. Dies ist die Voreinstellung, da beschädigte Dateien etwas besser als mit CRC32 erkannt werden und die Geschwindigkeitsdifferenz unerheblich ist."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "berechnet die SHA-256-Prüfsumme. Dies ist etwas langsamer als CRC32 und CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "Die Integrität der B<.xz>-Header wird immer mit CRC32 geprüft. Es ist nicht möglich, dies zu ändern oder zu deaktivieren."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "verifiziert die Integritätsprüfsumme der komprimierten Daten bei der Dekompression nicht. Die CRC32-Werte in den B<.xz>-Headern werden weiterhin normal verifiziert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Verwenden Sie diese Option nicht, außer Sie wissen, was Sie tun.> Mögliche Gründe, diese Option zu verwenden:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Versuchen, Daten aus einer beschädigten .xz-Datei wiederherzustellen."

# Irgendwie ist mir »extrem gut komprimiert« hier zu diffus. Was soll »gut« hier bedeuten? Besonders stark, besonders clever, was auch immer...
#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Erhöhung der Geschwindigkeit bei der Dekompression. Dies macht sich meist mit SHA-256 bemerkbar, oder mit Dateien, die extrem stark komprimiert sind. Wir empfehlen, diese Option nicht für diesen Zweck zu verwenden, es sei denn, die Integrität der Datei wird extern auf andere Weise überprüft."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> … B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "wählt eine der voreingestellten Kompressionsstufen, standardmäßig B<-6>. Wenn mehrere Voreinstellungsstufen angegeben sind, ist nur die zuletzt angegebene wirksam. Falls bereits eine benutzerdefinierte Filterkette angegeben wurde, wird diese durch die Festlegung der Voreinstellung geleert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Die Unterschiede zwischen den Voreinstellungsstufen sind deutlicher als bei B<gzip>(1) und B<bzip2>(1). Die gewählten Kompressionseinstellungen bestimmen den Speicherbedarf bei der Dekompression, daher ist es auf älteren Systemen mit wenig Speicher bei einer zu hoch gewählten Voreinstellung schwer, eine Datei zu dekomprimieren. Insbesondere B<ist es keine gute Idee, blindlings -9 für alles> zu verwenden, wie dies häufig mit B<gzip>(1) und B<bzip2>(1) gehandhabt wird."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> … B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Diese Voreinstellungen sind recht schnell. B<-0> ist manchmal schneller als B<gzip -9>, wobei aber die Kompression wesentlich besser ist. Die schnelleren Voreinstellungen sind im Hinblick auf die Geschwindigkeit mit B<bzip2>(1) vergleichbar , mit einem ähnlichen oder besseren Kompressionsverhältnis, wobei das Ergebnis aber stark vom Typ der zu komprimierenden Daten abhängig ist."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> … B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Gute bis sehr gute Kompression, wobei der Speicherbedarf für die Dekompression selbst auf alten Systemen akzeptabel ist. B<-6> ist die Voreinstellung, welche üblicherweise eine gute Wahl für die Verteilung von Dateien ist, die selbst noch auf Systemen mit nur 16\\ MiB Arbeitsspeicher dekomprimiert werden müssen (B<-5e> oder B<-6e> sind ebenfalls eine Überlegung wert. Siehe B<--extreme>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 … -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Ähnlich wie B<-6>, aber mit einem höheren Speicherbedarf für die Kompression und Dekompression. Sie sind nur nützlich, wenn Dateien komprimiert werden sollen, die größer als 8\\ MiB, 16\\ MiB beziehungsweise 32\\ MiB sind."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "Auf der gleichen Hardware ist die Dekompressionsgeschwindigkeit ein nahezu konstanter Wert in Bytes komprimierter Daten pro Sekunde. Anders ausgedrückt: Je besser die Kompression, umso schneller wird üblicherweise die Dekompression sein. Das bedeutet auch, dass die Menge der pro Sekunde ausgegebenen unkomprimierten Daten stark variieren kann."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "Die folgende Tabelle fasst die Eigenschaften der Voreinstellungen zusammen:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Voreinst."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "Wörtb.Gr"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "KomprCPU"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "KompSpeich"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "DekompSpeich"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Spaltenbeschreibungen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "Wörtb.Größe ist die Größe des LZMA2-Wörterbuchs. Es ist Speicherverschwendung, ein Wörterbuch zu verwenden, das größer als die unkomprimierte Datei ist. Daher ist es besser, die Voreinstellungen B<-7> … B<-9> zu vermeiden, falls es keinen wirklichen Bedarf dafür gibt. Mit B<-6> und weniger wird üblicherweise so wenig Speicher verschwendet, dass dies nicht ins Gewicht fällt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "KomprCPU ist eine vereinfachte Repräsentation der LZMA2-Einstellungen, welche die Kompressionsgeschwindigkeit beeinflussen. Die Wörterbuchgröße wirkt sich ebenfalls auf die Geschwindigkeit aus. Während KompCPU für die Stufen B<-6> bis B<-9> gleich ist, tendieren höhere Stufen dazu, etwas langsamer zu sein. Um eine noch langsamere, aber möglicherweise bessere Kompression zu erhalten, siehe B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr "KompSpeich enthält den Speicherbedarf des Kompressors im Einzel-Thread-Modus. Dieser kann zwischen den B<xz>-Versionen leicht variieren."

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "DekompSpeich enthält den Speicherbedarf für die Dekompression. Das bedeutet, dass die Kompressionseinstellungen den Speicherbedarf bei der Dekompression bestimmen. Der exakte Speicherbedarf bei der Dekompression ist geringfügig größer als die Größe des LZMA2-Wörterbuchs, aber die Werte in der Tabelle wurden auf ganze MiB aufgerundet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr " Der Speicherbedarf einiger der zukünftigen Multithread-Modi kann dramatisch höher sein als im Einzel-Thread-Modus. Mit dem Standardwert von B<--block-size> benötigt jeder Thread 3*3*Wörtb.Gr plus KompSpeich oder DekompSpeich. Beispielsweise benötigen vier Threads mit der Voreinstellung B<-6> etwa 660 bis 670 MiB Speicher."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "verwendet eine langsamere Variante der gewählten Kompressions-Voreinstellungsstufe (B<-0> … B<-9>), um hoffentlich ein etwas besseres Kompressionsverhältnis zu erreichen, das aber in ungünstigen Fällen auch schlechter werden kann. Der Speicherverbrauch bei der Dekompression wird dabei nicht beeinflusst, aber der Speicherverbrauch der Kompression steigt in den Voreinstellungsstufen B<-0> bis B<-3> geringfügig an."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Da es zwei Voreinstellungen mit den Wörterbuchgrößen 4\\ MiB und 8\\ MiB gibt, verwenden die Voreinstellungsstufen B<-3e> und B<-5e> etwas schnellere Einstellungen (niedrigere KompCPU) als B<-4e> beziehungsweise B<-6e>. Auf diese Weise sind zwei Voreinstellungen nie identisch."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "Zum Beispiel gibt es insgesamt vier Voreinstellungen, die ein 8\\ MiB großes Wörterbuch verwenden, deren Reihenfolge von der schnellsten zur langsamsten B<-5>, B<-6>, B<-5e> und B<-6e> ist."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "sind etwas irreführende Aliase für B<-0> beziehungsweise B<-9>. Sie werden nur zwecks Abwärtskompatibilität zu den LZMA-Dienstprogrammen bereitgestellt. Sie sollten diese Optionen besser nicht verwenden."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<Größe>"

# CHECK multi-threading and makes limited random-access
#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "teilt beim Komprimieren in das B<.xz>-Format die Eingabedaten in Blöcke der angegebenen I<Größe> in Byte. Die Blöcke werden unabhängig voneinander komprimiert, was dem Multi-Threading entgegen kommt und Zufallszugriffe bei der Dekompression begrenzt. Diese Option wird typischerweise eingesetzt, um die vorgegebene Blockgröße im Multi-Thread-Modus außer Kraft zu setzen, aber sie kann auch im Einzel-Thread-Modus angewendet werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr "Im Multi-Thread-Modus wird etwa die dreifache I<Größe> in jedem Thread zur Pufferung der Ein- und Ausgabe belegt. Die vorgegebene I<Größe> ist das Dreifache der Größe des LZMA2-Wörterbuchs oder 1 MiB, je nachdem, was mehr ist. Typischerweise ist das Zwei- bis Vierfache der Größe des LZMA2-Wörterbuchs oder wenigstens 1 MB ein guter Wert. Eine I<Größe>, die geringer ist als die des LZMA2-Wörterbuchs, ist Speicherverschwendung, weil dann der LZMA2-Wörterbuchpuffer niemals vollständig genutzt werden würde. Im Multi-Thread-Modus wird die Größe der Blöcke wird in den Block-Headern gespeichert. Die Größeninformation wird für eine Multi-Thread-Dekompression genutzt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr "Im Einzel-Thread-Modus werden die Blöcke standardmäßig nicht geteilt. Das Setzen dieser Option wirkt sich nicht auf den Speicherbedarf aus. In den Block-Headern werden keine Größeninformationen gespeichert, daher werden im Einzel-Thread-Modus erzeugte Dateien nicht zu den im Multi-Thread-Modus erzeugten Dateien identisch sein. Das Fehlen der Größeninformation bedingt auch, dass B<xz> nicht in der Lage sein wird, die Dateien im Multi-Thread-Modus zu dekomprimieren."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-list=>I<items>"
msgstr "B<--block-list=>I<Blöcke>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr "beginnt bei der Kompression in das B<.xz>-Format nach den angegebenen Intervallen unkomprimierter Daten einen neuen Block, optional mit einer benutzerdefinierten Filterkette."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr "Die I<Blöcke> werden in einer durch Kommata getrennten Liste angegeben. Jeder Block besteht aus einer optionalen Filterkettennummer zwischen 0 und 9, gefolgt von einem Doppelpunkt (B<:>) und der Größe der unkomprimierten Daten (diese Angabe ist erforderlich). Überspringen eines Blocks (zwei oder mehr aufeinander folgende Kommata) ist ein Kürzel dafür, die Größe und die Filter des vorherigen Blocks zu verwenden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr "Falls die Eingabedatei größer ist als die Summe der I<Blöcke>, dann wird der letzte in I<VBlöcke> angegebene Wert bis zum Ende der Datei wiederholt. Mit dem speziellen Wert B<0> können Sie angeben, dass der Rest der Datei als einzelner Block kodiert werden soll."

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr "Eine alternative Filterkette für jeden Block kann in Kombination mit den Optionen B<--filters1=>I<Filter> \\&…\\& B<--filters9=>I<Filter> angegeben werden. Diese Optionen definieren Filterketten mit einem Bezeichner zwischen 1 und 9. Die Filterkette 0 bezeichnet hierbei die voreingestellte Filterkette, was dem Nichtangeben einer Filterkette gleichkommt. Der Filterkettenbezeichner kann vor der unkomprimierten Größe verwendet werden, gefolgt von einem Doppelpunkt (B<:>). Falls Sie beispielsweise B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> angeben, werden die Blöcke folgendermaßen erstellt:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr "Die durch B<--filters1> angegebene Filterkette und 2 MiB Eingabe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr "Die durch B<--filters3> angegebene Filterkette und 2 MiB Eingabe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr "Die durch B<--filters2> angegebene Filterkette und 4 MiB Eingabe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr "Die vorgegebene Filterkette und 2 MiB Eingabe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr "Die vorgegebene Filterkette und 4 MiB Eingabe für jeden Block bis zum Ende der Eingabe."

# FIXME encoder → compressor
#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr "Falls Sie eine Größe angeben, welche die Blockgröße des Encoders übersteigen (entweder den Vorgabewert im Thread-Modus oder den mit B<--block-size=>I<Größe> angegebenen Wert), wird der Encoder zusätzliche Blöcke erzeugen, wobei die in den I<Blöcke> angegebenen Grenzen eingehalten werden. Wenn Sie zum Beispiel B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> angeben und die Eingabedatei 80 MiB groß ist, erhalten Sie 11 Blöcke: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10 und 1 MiB."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "Im Multi-Thread-Modus werden die Blockgrößen in den Block-Headern gespeichert. Dies geschieht im Einzel-Thread-Modus nicht, daher wird die kodierte Ausgabe zu der im Multi-Thread-Modus nicht identisch sein."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<Zeit>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "löscht bei der Kompression die ausstehenden Daten aus dem Encoder und macht sie im Ausgabedatenstrom verfügbar, wenn mehr als die angegebene I<Zeit> in Millisekunden (als positive Ganzzahl) seit dem vorherigen Löschen vergangen ist und das Lesen weiterer Eingaben blockieren würde. Dies kann nützlich sein, wenn B<xz> zum Komprimieren von über das Netzwerk eingehenden Daten verwendet wird. Kleine I<Zeit>-Werte machen die Daten unmittelbar nach dem Empfang nach einer kurzen Verzögerung verfügbar, während große I<Zeit>-Werte ein besseres Kompressionsverhältnis bewirken."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Dieses Funktionsmerkmal ist standardmäßig deaktiviert. Wenn diese Option mehrfach angegeben wird, ist die zuletzt angegebene wirksam. Für die Angabe der I<Zeit> kann der spezielle Wert B<0> verwendet werden, um dieses Funktionsmerkmal explizit zu deaktivieren."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Dieses Funktionsmerkmal ist außerhalb von POSIX-Systemen nicht verfügbar."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Dieses Funktionsmerkmal ist noch experimentell.> Gegenwärtig ist B<xz> aufgrund der Art und Weise, wie B<xz> puffert, für Dekompression in Echtzeit ungeeignet."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sync>"
msgstr "B<--no-sync>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr "synchronisiert die Zieldatei und deren Verzeichnis auf dem Speichergerät nicht, bevor die Quelldatei gelöscht wird. So kann die Performance beim Komprimieren oder Dekomprimieren vieler kleiner Dateien verbessert werden. Jedoch wäre es möglich, falls es kurz nach dem Löschen zu einem Systemabsturz kommt, dass die Zieldatei noch nicht auf dem Speichergerät geschrieben, aber der Löschvorgang bereits ausgeführt wurde. In diesem Fall gehen sowohl die Quelldatei als auch die Zieldatei verloren."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr "Diese Option ist nur wirksam, wenn B<xz> die Quelldatei löscht. In anderen Fällen wird niemals synchronisiert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr "Die Synchronisierung und B<--no-sync> wurden in Version B<xz> 5.7.1alpha hinzugefügt."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<Grenze>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "legt eine Grenze für die Speichernutzung bei der Kompression fest. Wenn diese Option mehrmals angegeben wird, ist die zuletzt angegebene wirksam."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Falls die Kompressionseinstellungen die I<Grenze> überschreiten, versucht B<xz>, die Einstellungen nach unten anzupassen, so dass die Grenze nicht mehr überschritten wird und zeigt einen Hinweis an, dass eine automatische Anpassung vorgenommen wurde. Die Anpassungen werden in folgender Reihenfolge angewendet: Reduzierung der Anzahl der Threads, Wechsel in den Einzelthread-Modus, falls sogar ein einziger Thread im Multithread-Modus die I<Grenze> überschreitet, und schlussendlich die Reduzierung der Größe des LZMA2-Wörterbuchs."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "Beim Komprimieren mit B<--format=raw> oder falls B<--no-adjust> angegeben wurde, wird nur die Anzahl der Threads reduziert, da nur so die komprimierte Ausgabe nicht beeinflusst wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Falls die I<Grenze> nicht anhand der vorstehend beschriebenen Anpassungen gesetzt werden kann, wird ein Fehler angezeigt und B<xz> wird mit dem Exit-Status 1 beendet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "Die I<Grenze> kann auf verschiedene Arten angegeben werden:"

# FIXME integer suffix
#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "Die I<Grenze> kann ein absoluter Wert in Byte sein. Ein Suffix wie B<MiB> kann dabei hilfreich sein. Beispiel: B<--memlimit-compress=80MiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "Die I<Grenze> kann als Prozentsatz des physischen Gesamtspeichers (RAM) angegeben werden. Dies ist insbesondere nützlich, wenn in einem Shell-Initialisierungsskript, das mehrere unterschiedliche Rechner gemeinsam verwenden, die Umgebungsvariable B<XZ_DEFAULTS> gesetzt ist. Auf diese Weise ist die Grenze auf Systemen mit mehr Speicher höher. Beispiel: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "Mit B<0> kann die I<Grenze> auf den Standardwert zurückgesetzt werden. Dies ist gegenwärtig gleichbedeutend mit dem Setzen der I<Grenze> auf B<max> (keine Speicherbegrenzung)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "Für die 32-Bit-Version von B<xz> gibt es einen Spezialfall: Falls die Grenze über B<4020\\ MiB> liegt, wird die I<Grenze> auf B<4020\\ MiB> gesetzt. Auf MIPS32 wird stattdessen B<2000\\ MB> verwendet (die Werte B<0> und B<max> werden hiervon nicht beeinflusst; für die Dekompression gibt es keine vergleichbare Funktion). Dies kann hilfreich sein, wenn ein 32-Bit-Executable auf einen 4\\ GiB großen Adressraum (2 GiB auf MIPS32) zugreifen kann, wobei wir hoffen wollen, dass es in anderen Situationen keine negativen Effekte hat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Siehe auch den Abschnitt B<Speicherbedarf>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<Grenze>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "legt eine Begrenzung des Speicherverbrauchs für die Dekompression fest. Dies beeinflusst auch den Modus B<--list>. Falls die Aktion nicht ausführbar ist, ohne die I<Grenze> zu überschreiten, gibt B<xz> eine Fehlermeldung aus und die Dekompression wird fehlschlagen. Siehe B<--memlimit-compress=>I<Grenze> zu möglichen Wegen, die I<Grenze> anzugeben."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<Grenze>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "legt eine Begrenzung des Speicherverbrauchs für Multithread-Dekompression fest. Dies beeinflusst lediglich die Anzahl der Threads; B<xz> wird dadurch niemals die Dekompression einer Datei verweigern. Falls die I<Grenze> für jegliches Multithreading zu niedrig ist, wird sie ignoriert und B<xz> setzt im Einzelthread-modus fort. Beachten Sie auch, dass bei der Verwendung von B<--memlimit-decompress> dies stets sowohl auf den Einzelthread-als auch auf den Multithread-Modus angewendet wird und so die effektive I<Grenze> für den Multithread-Modus niemals höher sein wird als die mit B<--memlimit-decompress> gesetzte Grenze."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "Im Gegensatz zu anderen Optionen zur Begrenzung des Speicherverbrauchs hat B<--memlimit-mt-decompress=>I<Grenze> eine systemspezifisch vorgegebene I<Grenze>. Mit B<xz --info-memory> können Sie deren aktuellen Wert anzeigen lassen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Diese Option und ihr Standardwert existieren, weil die unbegrenzte threadbezogene Dekompression bei einigen Eingabedateien zu unglaublich großem Speicherverbrauch führen würde. Falls die vorgegebene I<Grenze> auf Ihrem System zu niedrig ist, können Sie die I<Grenze> durchaus erhöhen, aber setzen Sie sie niemals auf einen Wert größer als die Menge des nutzbaren Speichers, da B<xz> bei entsprechenden Eingabedateien versuchen wird, diese Menge an Speicher auch bei einer geringen Anzahl von Threads zu verwnden. Speichermangel oder Auslagerung verbessern die Dekomprimierungsleistung nicht."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Siehe B<--memlimit-compress=>I<Grenze> für mögliche Wege zur Angabe der I<Grenze>.  Sezen der I<Grenze> auf B<0> setzt die I<Grenze> auf den vorgegebenen systemspezifischen Wert zurück."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<Grenze>, B<--memlimit=>I<Grenze>, B<--memory=>I<Grenze>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Dies ist gleichbedeutend mit B<--memlimit-compress=>I<Grenze> B<--memlimit-decompress=>I<Grenze> B<--memlimit-mt-decompress=>I<Grenze>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "zeigt einen Fehler an und beendet, falls die Grenze der Speichernutzung nicht ohne Änderung der Einstellungen, welche die komprimierte Ausgabe beeinflussen, berücksichtigt werden kann. Das bedeutet, dass B<xz> daran gehindert wird, den Encoder vom Multithread-Modus in den Einzelthread-Modus zu versetzen und die Größe des LZMA2-Wörterbuchs zu reduzieren. Allerdings kann bei Verwendung dieser Option dennoch die Anzahl der Threads reduziert werden, um die Grenze der Speichernutzung zu halten, sofern dies die komprimierte Ausgabe nicht beeinflusst."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "Die automatische Anpassung ist beim Erzeugen von Rohdatenströmen (B<--format=raw>) immer deaktiviert."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<Threads>, B<--threads=>I<Threads>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "gibt die Anzahl der zu verwendenden Arbeits-Threads an. Wenn Sie I<Threads> auf einen speziellen Wert B<0> setzen, verwendet B<xz> maximal so viele Threads, wie der/die Prozessor(en) im System untestützen. Die tatsächliche Anzahl kann geringer sein als die angegebenen I<Threads>, wenn die Eingabedatei nicht groß genug für Threading mit den gegebenen Einstellungen ist oder wenn mehr Threads die Speicherbegrenzung übersteigen würden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "Die Multithread- bzw. Einzelthread-Kompressoren erzeugen unterschiedliche Ausgaben. Der Einzelthread-Kompressor erzeugt die geringste Dateigröße, aber nur die Ausgabe des Multithread-Kompressors kann mit mehreren Threads wieder dekomprimiert werden. Das Setzen der Anzahl der I<Threads> auf B<1> wird den Einzelthread-Modus verwenden.  Das Setzen der Anzahl der I<Threads> auf einen anderen Wert einschließlich B<0> verwendet den Multithread-Kompressor, und zwar sogar dann, wenn das System nur einen einzigen Hardware-Thread unterstützt (B<xz> 5.2.x verwendete in diesem Fall noch den Einzelthread-Modus)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Um den Multithread-Modus mit nur einem einzigen Thread zu verwenden, setzen Sie die Anzahl der I<Threads> auf B<+1>. Das Präfix B<+> hat mit Werten verschieden von B<1> keinen Effekt. Eine Begrenzung des Speicherverbrauchs kann B<xz> dennoch veranlassen, den Einzelthread-Modus zu verwenden, außer wenn B<--no-adjust> verwendet wird. Die Unterstützung für das Präfix B<+> wurde in B<xz> 5.4.0 hinzugefügt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr "Falls das automatische Setzen der Anzahl der Threads angefordert und keine Speicherbegrenzung angegeben wurde, dann wird eine systemspezifisch vorgegebene weiche Grenze verwendet, um eventuell die Anzahl der Threads zu begrenzen. Es ist eine weiche Grenze im Sinne davon, dass sie ignoriert wird, falls die Anzahl der Threads 1 ist; daher wird eine weiche Grenze B<xz> niemals an der Kompression oder Dekompression hindern. Diese vorgegebene weiche Grenze veranlasst B<xz> nicht, vom Multithread-Modus in den Einzelthread-Modus zu wechseln. Die aktiven Grenzen können Sie mit dem Befehl B<xz --info-memory> anzeigen lassen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "Die gegenwärtig einzige Threading-Methode teilt die Eingabe in Blöcke und komprimiert diese unabhängig voneinander. Die vorgegebene Blockgröße ist von der Kompressionsstufe abhängig und kann mit der Option B<--block-size=>I<Größe> außer Kraft gesetzt werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "Eine thread-basierte Dekompression wird nur bei Dateien funktionieren, die mehrere Blöcke mit Größeninformationen in deren Headern enthalten. Alle im Multi-Thread-Modus komprimierten Dateien, die groß genug sind, erfüllen diese Bedingung, im Einzel-Thread-Modus komprimierte Dateien dagegen nicht, selbst wenn B<--block-size=>I<Größe> verwendet wurde."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr "Der Vorgabewert für I<Threads> is B<0>. In B<xz> 5.4.x und älteren Versionen ist der Vorgabewert B<1>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Benutzerdefinierte Filterketten für die Kompression"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Eine benutzerdefinierte Filterkette ermöglicht die Angabe detaillierter Kompressionseinstellungen, anstatt von den Voreinstellungen auszugehen. Wenn eine benutzerdefinierte Filterkette angegeben wird, werden die vorher in der Befehlszeile angegebenen Voreinstellungsoptionen (B<-0> … B<-9> und B<--extreme>) außer Kraft gesetzt. Wenn eine Voreinstellungsoption nach einer oder mehreren benutzerdefinierten Filterkettenoptionen angegeben wird, dann wird die neue Voreinstellung wirksam und die zuvor angegebenen Filterkettenoptionen werden außer Kraft gesetzt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Eine Filterkette ist mit dem Piping (der Weiterleitung) in der Befehlszeile vergleichbar. Bei der Kompression gelangt die unkomprimierte Eingabe in den ersten Filter, dessen Ausgabe wiederum in den zweiten Filter geleitet wird (sofern ein solcher vorhanden ist). Die Ausgabe des letzten Filters wird in die komprimierte Datei geschrieben. In einer Filterkette sind maximal vier Filter zulässig, aber typischerweise besteht eine Filterkette nur aus einem oder zwei Filtern."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Bei vielen Filtern ist die Positionierung in der Filterkette eingeschränkt: Einige Filter sind nur als letzte in der Kette verwendbar, einige können nicht als letzte Filter gesetzt werden, und andere funktionieren an beliebiger Stelle. Abhängig von dem Filter ist diese Beschränkung entweder auf das Design des Filters selbst zurückzuführen oder ist aus Sicherheitsgründen vorhanden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr "Eine benutzerdefinierte Filterkette kann auf zwei verschiedene Arten angegeben werden. Die Optionen B<--filters=>I<Filter> und B<--filters1=>I<Filter> \\&…\\& B<--filters9=>I<Filter> ermöglichen die Angabe einer ganzen Filterkette in einer einzelnen Option gemäß der Liblzma-Filterzeichenkettensyntax. Alternativ können Sie eine Filterkette mit einer oder mehreren individuellen Filteroptionen in der Reihenfolge angeben, in der sie in der Filterkette verwendet werden sollen. Daher ist die Reihenfolge der individuellen Filteroptionen wichtig! Beim Dekodieren von Rohdatenströmen (B<--format=raw>) muss die Filterkette in der gleichen Reihenfolge wie bei der Komprimierung angegeben werden. Alle individuellen Filter- oder Voreinstellungsoptionen, die I<vor> der vollen Filterkettenoption (B<--filters=>I<Filter>) angegeben werden, werden verworfen. Individuelle Filter, die I<nach> der vollen Filterkettenoption angegeben werden, setzen die Filterkette zurück"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr "Sowohl vollständige als auch individuelle Filteroptionen akzeptieren filterspezifische I<Optionen> in einer durch Kommata getrennten Liste. Zusätzliche Kommata in den I<Optionen> werden ignoriert. Jede Option hat einen Standardwert, daher brauchen Sie nur jene anzugeben, die Sie ändern wollen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Um die gesamte Filterkette und die I<Optionen> anzuzeigen, rufen Sie B<xz -vv> auf (was gleichbedeutend mit der zweimaligen Angabe von B<--verbose> ist). Dies funktioniert auch zum Betrachten der von den Voreinstellungen verwendeten Filterkettenoptionen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters=>I<filters>"
msgstr "B<--filters=>I<Filter>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr "gibt die vollständige Filterkette oder eine Voreinstellung in einer einzelnen Option an. Mehrere Filter können durch Leerzeichen oder zwei Minuszeichen (B<-->) voneinander getrennt werden. Es kann notwendig sein, die I<Filter> in der Shell-Befehlszeile zu maskieren, so dass diese als einzelne Option ausgewertet werden. Um Optionen Werte zuzuordnen, verwenden Sie B<:> oder B<=>. Einer Voreinstellung kann ein B<-> vorangestellt werden, dem keiner oder mehrere Schalter folgen. Der einzige unterstützte Schalter ist B<e> zum Anwenden der gleichen Optionen wie B<--extreme>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr "B<--filters1>=I<Filter> … B<--filters9>=I<Filter>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr "gibt bis zu neun optionale Filterketten an, die mit B<--block-list> verwendet werden können."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr "Wenn Sie beispielsweise ein Archiv mit ausführbaren Dateien gefolgt von Textdateien komprimieren, könnte der Teil mit den ausführbaren Dateien eine Filterkette mit einem BCJ-Filter und der Textdateiteil lediglich den LZMA2-Filter verwenden."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters-help>"
msgstr "B<--filters-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr "zeigt eine Hilfemeldung an, welche beschreibt, wie Voreinstellungen und benutzerdefinierte Filterketten in den Optionen B<--filters> und B<--filters1=>I<Filter> \\&… \\& B<--filters9=>I<Filter> angegeben werden und beendet das Programm."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<Optionen>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "fügt LZMA1- oder LZMA2-Filter zur Filterkette hinzu. Diese Filter können nur als letzte Filter in der Kette verwendet werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 ist ein veralteter Filter, welcher nur wegen des veralteten B<.lzma>-Dateiformats unterstützt wird, welches nur LZMA1 unterstützt. LZMA2 ist eine aktualisierte Version von LZMA1, welche einige praktische Probleme von LZMA1 behebt. Das B<.xz>-Format verwendet LZMA2 und unterstützt LZMA1 gar nicht. Kompressionsgeschwindigkeit und -verhältnis sind bei LZMA1 und LZMA2 praktisch gleich."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 und LZMA2 haben die gleichen I<Optionen>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<Voreinstellung>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "setzt alle LZMA1- oder LZMA2-I<Optionen> auf die I<Voreinstellung> zurück. Diese I<Voreinstellung> wird in Form einer Ganzzahl angegeben, der ein aus einem einzelnen Buchstaben bestehender Voreinstellungsmodifikator folgen kann. Die Ganzzahl kann B<0> bis B<9> sein, entsprechend den Befehlszeilenoptionen B<-0> … B<-9>. Gegenwärtig ist B<e> der einzige unterstützte Modifikator, was B<--extreme> entspricht. Wenn keine B<Voreinstellung> angegeben ist, werden die Standardwerte der LZMA1- oder LZMA2-I<Optionen> der Voreinstellung B<6> entnommen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<Größe>"

# FIXME Dezimaltrenner in 1.5 GB
#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "Die I<Größe> des Wörterbuchs (Chronikpuffers) gibt an, wie viel Byte der kürzlich verarbeiteten unkomprimierten Daten im Speicher behalten werden sollen. Der Algorithmus versucht, sich wiederholende Byte-Abfolgen (Übereinstimmungen) in den unkomprimierten Daten zu finden und diese durch Referenzen zu den Daten zu ersetzen, die sich gegenwärtig im Wörterbuch befinden. Je größer das Wörterbuch, umso größer ist die Chance, eine Übereinstimmung zu finden. Daher bewirkt eine Erhöhung der I<Größe> des Wörterbuchs üblicherweise ein besseres Kompressionsverhältnis, aber ein Wörterbuch, das größer ist als die unkomprimierte Datei, wäre Speicherverschwendung."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "Typische Wörterbuch-I<Größen> liegen im Bereich von 64\\ KiB bis 64\\ MiB. Das Minimum ist 4\\ KiB. Das Maximum für die Kompression ist gegenwärtig 1.5\\ GiB (1536\\ MiB). Bei der Dekompression wird bereits eine Wörterbuchgröße bis zu 4\\ GiB minus 1 Byte unterstützt, welche das Maximum für die LZMA1- und LZMA2-Datenstromformate ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "Die I<Größe> des Wörterbuchs und der Übereinstimmungsfinder (I<Üf>) bestimmen zusammen den Speicherverbrauch des LZMA1- oder LZMA2-Kodierers. Bei der Dekompression ist ein Wörterbuch der gleichen I<Größe> (oder ein noch größeres) wie bei der Kompression erforderlich, daher wird der Speicherverbrauch des Dekoders durch die Größe des bei der Kompression verwendeten Wörterbuchs bestimmt. Die B<.xz>-Header speichern die I<Größe> des Wörterbuchs entweder als 2^I<n> oder 2^I<n> + 2^(I<n>-1), so dass diese I<Größen> für die Kompression etwas bevorzugt werden. Andere I<Größen> werden beim Speichern in den B<.xz>-Headern aufgerundet."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "gibt die Anzahl der literalen Kontextbits an. Das Minimum ist 0 und das Maximum 4; der Standardwert ist 3. Außerdem darf die Summe von I<lc> und I<lp> nicht größer als 4 sein."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Alle Bytes, die nicht als Übereinstimmungen kodiert werden können, werden als Literale kodiert. Solche Literale sind einfache 8-bit-Bytes, die jeweils für sich kodiert werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "Bei der Literalkodierung wird angenommen, dass die höchsten I<lc>-Bits des zuvor unkomprimierten Bytes mit dem nächsten Byte in Beziehung stehen. Zum Beispiel folgt in typischen englischsprachigen Texten auf einen Großbuchstaben ein Kleinbuchstabe und auf einen Kleinbuchstaben üblicherweise wieder ein Kleinbuchstabe. Im US-ASCII-Zeichensatz sind die höchsten drei Bits 010 für Großbuchstaben und 011 für Kleinbuchstaben. Wenn I<lc> mindestens 3 ist, kann die literale Kodierung diese Eigenschaft der unkomprimierten Daten ausnutzen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "Der Vorgabewert (3) ist üblicherweise gut. Wenn Sie die maximale Kompression erreichen wollen, versuchen Sie B<lc=4>. Manchmal hilft es ein wenig, doch manchmal verschlechtert es die Kompression. Im letzteren Fall versuchen Sie zum Beispiel auch\\& B<lc=2>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "gibt die Anzahl der literalen Positionsbits an. Das Minimum ist 0 und das Maximum 4; die Vorgabe ist 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> beeinflusst, welche Art der Ausrichtung der unkomprimierten Daten beim Kodieren von Literalen angenommen wird. Siehe I<pb> weiter unten für weitere Informationen zur Ausrichtung."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<Anzahl>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "legt die Anzahl der Positions-Bits fest. Das Minimum ist 0 und das Maximum 4; Standard ist 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> beeinflusst, welche Art der Ausrichtung der unkomprimierten Daten generell angenommen wird. Standardmäßig wird eine Vier-Byte-Ausrichtung angenommen (2^I<pb>=2^2=4), was oft eine gute Wahl ist, wenn es keine bessere Schätzung gibt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Wenn die Ausrichtung bekannt ist, kann das entsprechende Setzen von I<pb> die Dateigröße ein wenig verringern. Wenn Textdateien zum Beispiel eine Ein-Byte-Ausrichtung haben (US-ASCII, ISO-8859-*, UTF-8), kann das Setzen von B<pb=0> die Kompression etwas verbessern. Für UTF-16-Text ist B<pb=1> eine gute Wahl. Wenn die Ausrichtung eine ungerade Zahl wie beispielsweise 3 Byte ist, könnte B<pb=0> die beste Wahl sein."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Obwohl die angenommene Ausrichtung mit I<pb> und I<lp> angepasst werden kann, bevorzugen LZMA1 und LZMA2 noch etwas die 16-Byte-Ausrichtung. Das sollten Sie vielleicht beim Design von Dateiformaten berücksichtigen, die wahrscheinlich oft mit LZMA1 oder LZMA2 komprimiert werden."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<Üf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "Der Übereinstimmungsfinder hat einen großen Einfluss auf die Geschwindigkeit des Kodierers, den Speicherbedarf und das Kompressionsverhältnis. Üblicherweise sind auf Hash-Ketten basierende Übereinstimmungsfinder schneller als jene, die mit Binärbäumen arbeiten. Die Vorgabe hängt von der I<Voreinstellungsstufe> ab: 0 verwendet B<hc3>, 1-3 verwenden B<hc4> und der Rest verwendet B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Die folgenden Übereinstimmungsfinder werden unterstützt. Die Formeln zur Ermittlung des Speicherverbrauchs sind grobe Schätzungen, die der Realität am nächsten kommen, wenn I<Wörterbuch> eine Zweierpotenz ist."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Hash-Kette mit 2- und 3-Byte-Hashing"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Minimalwert für I<nice>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Speicherbedarf:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 7,5 (falls I<dict> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 5,5 + 64 MiB (falls I<dict> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Hash-Kette mit 2-, 3- und 4-Byte-Hashing"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Minimaler Wert für I<nice>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 7,5 (falls I<dict> E<lt>= 32 MiB ist);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 6,5 (falls I<dict> E<gt> 32 MiB ist)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Binärbaum mit 2-Byte-Hashing"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Minimaler Wert für I<nice>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Speicherverbrauch: I<dict> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Binärbaum mit 2- und 3-Byte-Hashing"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<dict> * 11,5 (falls I<dict> E<lt>= 16 MiB ist);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<dict> * 9,5 + 64 MiB (falls I<dict> E<gt> 16 MiB ist)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Binärbaum mit 2-, 3- und 4-Byte-Hashing"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<dict> * 11,5 (falls I<dict> E<lt>= 32 MiB ist);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<dict> * 10,5 (falls I<dict> E<gt> 32 MiB ist)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<Modus>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "gibt die Methode zum Analysieren der vom Übereinstimmungsfinder gelieferten Daten an. Als I<Modi> werden B<fast> und B<normal> unterstützt. Die Vorgabe ist B<fast> für die I<Voreinstellungsstufen> 0-3 und B<normal> für die I<Voreinstellungsstufen> 4-9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Üblicherweise wird B<fast> mit Hashketten-basierten Übereinstimmungsfindern und B<normal> mit Binärbaum-basierten Übereinstimmungsfindern verwendet. So machen es auch die I<Voreinstellungsstufen>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<nice>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "gibt an, was als annehmbarer Wert für eine Übereinstimmung angesehen werden kann. Wenn eine Übereinstimmung gefunden wird, die mindestens diesen I<nice>-Wert hat, sucht der Algorithmus nicht weiter nach besseren Übereinstimmungen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "Der I<nice>-Wert kann 2-273 Byte sein. Höhere Werte tendieren zu einem besseren Kompressionsverhältnis, aber auf Kosten der Geschwindigkeit. Die Vorgabe hängt von der I<Voreinstellungsstufe> ab."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<Tiefe>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "legt die maximale Suchtiefe im Übereinstimmungsfinder fest. Vorgegeben ist der spezielle Wert 0, der den Kompressor veranlasst, einen annehmbaren Wert für I<Tiefe> aus I<Üf> und I<nice>-Wert zu bestimmen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "Die angemessene I<Tiefe> für Hash-Ketten ist 4-100 und 16-1000 für Binärbäume. Hohe Werte für die I<Tiefe> können den Kodierer bei einigen Dateien extrem verlangsamen. Vermeiden Sie es, die I<Tiefe> über einen Wert von 100 zu setzen, oder stellen Sie sich darauf ein, die Kompression abzubrechen, wenn sie zu lange dauert."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "Beim Dekodieren von Rohdatenströmen (B<--format=raw>) benötigt LZMA2 nur die Wörterbuch-I<Größe>.  LZMA1 benötigt außerdem I<lc>, I<lp> und I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<Optionen>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<Optionen>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "fügt ein »Branch/Call/Jump«-(BCJ-)Filter zur Filterkette hinzu. Diese Filter können nicht als letzter Filter in der Filterkette verwendet werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "Ein BCJ-Filter wandelt relative Adressen im Maschinencode in deren absolute Gegenstücke um. Die Datengröße wird dadurch nicht geändert, aber die Redundanz erhöht, was LZMA2 dabei helfen kann, eine um 10 bis 15% kleinere B<.xz>-Datei zu erstellen. Die BCJ-Filter sind immer reversibel, daher verursacht die Anwendung eines BCJ-Filters auf den falschen Datentyp keinen Datenverlust, wobei aber das Kompressionsverhältnis etwas schlechter werden könnte. Die BCJ-Filter sind sehr schnell und verbrauchen nur wenig mehr Speicher."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Diese BCJ-Filter haben bekannte Probleme mit dem Kompressionsverhältnis:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "In einigen Dateitypen, die ausführbaren Code enthalten (zum Beispiel Objektdateien, statische Bibliotheken und Linux-Kernelmodule), sind die Adressen in den Anweisungen mit Füllwerten gefüllt. Diese BCJ-Filter führen dennoch die Adressumwandlung aus, wodurch die Kompression bei diesen Dateien schlechter wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Falls ein BCJ-Filter auf ein Archiv angewendet wird, ist es möglich, dass das Kompressionsverhältnis schlechter als ohne Filter wird. Falls es beispielsweise ähnliche oder sogar identische ausführbare Dateien gibt, dann werden diese durch die Filterung wahrscheinlich »unähnlicher« und verschlechtern dadurch das Kompressionsverhältnis. Der Inhalt nicht-ausführbarer Dateien im gleichen Archiv kann sich ebenfalls darauf auswirken. In der Praxis werden Sie durch Versuche mit oder ohne BCJ-Filter selbst herausfinden müssen, was situationsbezogen besser ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Verschiedene Befehlssätze haben unterschiedliche Ausrichtungen: Die ausführbare Datei muss in den Eingabedateien einem Vielfachen dieses Wertes entsprechen, damit dieser Filter funktioniert."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Filter"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Ausrichtung"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Hinweise"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "32-Bit oder 64-Bit x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr "4096-Byte-Ausrichtung ist optimal"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Nur Big Endian"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr "RISC-V"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr "Da die BCJ-gefilterten Daten üblicherweise mit LZMA2 komprimiert sind, kann das Kompressionsverhältnis dadurch etwas verbessert werden, dass die LZMA2-Optionen so gesetzt werden, dass sie der Ausrichtung des gewählten BCJ-Filters entsprechen. Beispiele:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr "Der IA-64-Filter hat eine 16-Byte-Ausrichtung, daher ist B<pb=4,lp=4,lc=0> für LZMA2 passend (2^4=16)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr "RISC-V-Code hat eine 2-Byte- oder 4-Byte-Ausrichtung, abhängig davon, ob die Datei 16-bit-komprimierte Instruktionen enthält (die C-Erweiterung). Wenn 16-bit-Instruktionen verwendet werden, ist B<pb=2,lp=1,lc=3> oder B<pb=1,lp=1,lc=3> passend. Wenn keine 16-bit-Instruktionen vorhanden sind, ist B<pb=2,lp=2,lc=2> am besten. Mit B<readelf -h> können Sie überprüfen, ob »RVC« in der »Flags«-Zeile auftritt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr "ARM64 hat stets eine 4-Byte-Ausrichtung, daher ist B<pb=2,lp=2,lc=2> am besten."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr "Der x86-Filter stellt eine Ausnahme dar. Es ist üblicherweise eine gute Wahl, bei den Voreinstellungen von LZMA2 (B<pb=2,lp=0,lc=3>) zu bleiben, wenn Sie ausführbare x86-Dateien komprimieren"

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Alle BCJ-Filter unterstützen die gleichen I<Optionen>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<Versatz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "gibt den Start-I<Versatz> an, der bei der Umwandlung zwischen relativen und absoluten Adressen verwendet wird. Der I<Versatz> muss ein Vielfaches der Filterausrichtung sein (siehe die Tabelle oben). Der Standardwert ist 0. In der Praxis ist dieser Standardwert gut; die Angabe eines benutzerdefinierten I<Versatzes> ist fast immer unnütz."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<Optionen>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "fügt den Delta-Filter zur Filterkette hinzu. Der Delta-Filter kann nicht als letzter Filter in der Filterkette verwendet werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "Gegenwärtig wird nur eine einfache, Byte-bezogene Delta-Berechnung unterstützt. Beim Komprimieren von zum Beispiel unkomprimierten Bitmap-Bildern oder unkomprimierten PCM-Audiodaten kann es jedoch sinnvoll sein. Dennoch können für spezielle Zwecke entworfene Algorithmen deutlich bessere Ergebnisse als Delta und LZMA2 liefern. Dies trifft insbesondere auf Audiodaten zu, die sich zum Beispiel mit B<flac>(1) schneller und besser komprimieren lassen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "Unterstützte I<Optionen>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<Abstand>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "gibt den I<Abstand> der Delta-Berechnung in Byte an. Zulässige Werte für den I<Abstand> sind 1 bis 256. Der Vorgabewert ist 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "Zum Beispiel wird mit B<dist=2> und der 8-Byte-Eingabe A1 B1 A2 B3 A3 B5 A4 B7 die Ausgabe A1 B1 01 02 01 02 01 02 sein."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Andere Optionen"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "unterdrückt Warnungen und Hinweise. Geben Sie dies zweimal an, um auch Fehlermeldungen zu unterdrücken. Diese Option wirkt sich nicht auf den Exit-Status aus. Das bedeutet, das selbst bei einer unterdrückten Warnung der Exit-Status zur Anzeige einer Warnung dennoch verwendet wird."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "bewirkt ausführliche Ausgaben. Wenn die Standardfehlerausgabe mit einem Terminal verbunden ist, zeigt B<xz> den Fortschritt an. Durch zweimalige Angabe von B<--verbose> wird die Ausgabe noch ausführlicher."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "Der Fortschrittsanzeiger stellt die folgenden Informationen dar:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "Der Prozentsatz des Fortschritts wird angezeigt, wenn die Größe der Eingabedatei bekannt ist. Das bedeutet, dass der Prozentsatz in Weiterleitungen (Pipes) nicht angezeigt werden kann."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Menge der erzeugten komprimierten Daten (bei der Kompression) oder der verarbeiteten Daten (bei der Dekompression)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Menge der verarbeiteten unkomprimierten Daten (bei der Kompression) oder der erzeugten Daten (bei der Dekompression)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Kompressionsverhältnis, das mittels Dividieren der Menge der bisher komprimierten Daten durch die Menge der bisher verarbeiteten unkomprimierten Daten ermittelt wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Kompressions- oder Dekompressionsgeschwindigkeit. Diese wird anhand der Menge der unkomprimierten verarbeiteten Daten (bei der Kompression) oder der Menge der erzeugten Daten (bei der Dekompression) pro Sekunde gemessen. Die Anzeige startet einige Sekunden nachdem B<xz> mit der Verarbeitung der Datei begonnen hat."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Die vergangene Zeit im Format M:SS oder H:MM:SS."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "Die geschätzte verbleibende Zeit wird nur angezeigt, wenn die Größe der Eingabedatei bekannt ist und bereits einige Sekunden vergangen sind, nachdem B<xz> mit der Verarbeitung der Datei begonnen hat. Die Zeit wird in einem weniger präzisen Format ohne Doppelpunkte angezeigt, zum Beispiel 2 min 30 s."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Wenn die Standardfehlerausgabe kein Terminal ist, schreibt B<xz> mit B<--verbose> nach dem Komprimieren oder Dekomprimieren der Datei in einer einzelnen Zeile den Dateinamen, die komprimierte Größe, die unkomprimierte Größe, das Kompressionsverhältnis und eventuell auch die Geschwindigkeit und die vergangene Zeit in die Standardfehlerausgabe. Die Geschwindigkeit und die vergangene Zeit werden nur angezeigt, wenn der Vorgang mindestens ein paar Sekunden gedauert hat. Wurde der Vorgang nicht beendet, zum Beispiel weil ihn der Benutzer abgebrochen hat, wird außerdem der Prozentsatz des erreichten Verarbeitungsfortschritts aufgenommen, sofern die Größe der Eingabedatei bekannt ist."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "setzt den Exit-Status nicht auf 2, selbst wenn eine Bedingung erfüllt ist, die eine Warnung gerechtfertigt hätte. Diese Option wirkt sich nicht auf die Ausführlichkeitsstufe aus, daher müssen sowohl B<--quiet> als auch B<--no-warn> angegeben werden, um einerseits keine Warnungen anzuzeigen und andererseits auch den Exit-Status nicht zu ändern."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "gibt Meldungen in einem maschinenlesbaren Format aus. Dadurch soll das Schreiben von Frontends erleichtert werden, die B<xz> anstelle von Liblzma verwenden wollen, was in verschiedenen Skripten der Fall sein kann. Die Ausgabe mit dieser aktivierten Option sollte über mehrere B<xz>-Veröffentlichungen stabil sein. Details hierzu finden Sie im Abschnitt B<ROBOTER-MODUS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "zeigt in einem menschenlesbaren Format an, wieviel physischen Speicher (RAM) und wie viele Prozessor-Threads das System nach Annahme von B<xz> hat, sowie die Speicherbedarfsbegrenzung für Kompression und Dekompression, und beendet das Programm erfolgreich."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "zeigt eine Hilfemeldung mit den am häufigsten genutzten Optionen an und beendet das Programm erfolgreich."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

# FIXME Satzpunkt fehlt
#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "zeigt eine Hilfemeldung an, die alle Funktionsmerkmale von B<xz> beschreibt und beendet das Programm erfolgreich."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "zeigt die Versionsnummer von B<xz> und Liblzma in einem menschenlesbaren Format an. Um eine maschinell auswertbare Ausgabe zu erhalten, geben Sie B<--robot> vor B<--version> an."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "ROBOTER-MODUS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr "Der Roboter-Modus wird mit der Option B<--robot> aktiviert. Er bewirkt, dass die Ausgabe von B<xz> leichter von anderen Programmen ausgewertet werden kann. Gegenwärtig wird B<--robot> nur zusammen mit B<--list>, B<--filters-help>, B<--info-memory> und B<--version> unterstützt. In der Zukunft wird dieser Modus auch für Kompression und Dekompression unterstützt."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Listenmodus"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> verwendet eine durch Tabulatoren getrennte Ausgabe. In der ersten Spalte jeder Zeile bezeichnet eine Zeichenkette den Typ der Information, die in dieser Zeile enthalten ist:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Dies ist stets die erste Zeile, wenn eine Datei aufgelistet wird. Die zweite Spalte in der Zeile enthält den Dateinamen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

# CHECK overall
#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Diese Zeile enthält allgemeine Informationen zur B<.xz>-Datei. Diese Zeile wird stets nach der B<name>-Zeile ausgegeben."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Dieser Zeilentyp wird nur verwendet, wenn B<--verbose> angegeben wurde. Es gibt genau so viele B<stream>-Zeilen, wie Datenströme in der B<.xz>-Datei enthalten sind."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Dieser Zeilentyp wird nur verwendet, wenn B<--verbose> angegeben wurde. Es gibt so viele B<block>-Zeilen, wie Blöcke in der B<.xz>-Datei. Die B<block>-Zeilen werden nach allen B<stream>-Zeilen angezeigt; verschiedene Zeilentypen werden nicht verschachtelt."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Dieser Zeilentyp wird nur verwendet, wenn B<--verbose> zwei Mal angegeben wurde. Diese Zeile wird nach allen B<block>-Zeilen ausgegeben. Wie die B<file>-Zeile enthält die B<summary>-Zeile allgemeine Informationen zur B<.xz>-Datei."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Diese Zeile ist immer die letzte der Listenausgabe. Sie zeigt die Gesamtanzahlen und -größen an."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Die Spalten der B<file>-Zeilen:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Anzahl der Datenströme in der Datei"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Gesamtanzahl der Blöcke in den Datenströmen"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Komprimierte Größe der Datei"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Unkomprimierte Größe der Datei"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Das Kompressionsverhältnis, zum Beispiel B<0.123>. Wenn das Verhältnis über 9.999 liegt, werden drei Minuszeichen (B<--->) anstelle des Kompressionsverhältnisses angezeigt."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Durch Kommata getrennte Liste der Namen der Integritätsprüfungen. Für die bekannten Überprüfungstypen werden folgende Zeichenketten verwendet: B<None>, B<CRC32>, B<CRC64> und B<SHA-256>. B<Unknown->I<N> wird verwendet, wobei I<N> die Kennung der Überprüfung als Dezimalzahl angibt (ein- oder zweistellig)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Gesamtgröße der Datenstromauffüllung in der Datei"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Die Spalten der B<stream>-Zeilen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Datenstromnummer (der erste Datenstrom ist 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Anzahl der Blöcke im Datenstrom"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Komprimierte Startposition"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Unkomprimierte Startposition"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Komprimierte Größe (schließt die Datenstromauffüllung nicht mit ein)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Unkomprimierte Größe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Kompressionsverhältnis"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Name der Integritätsprüfung"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Größe der Datenstromauffüllung"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Die Spalten der B<block>-Zeilen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Anzahl der in diesem Block enthaltenen Datenströme"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Blocknummer relativ zum Anfang des Datenstroms (der erste Block ist 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Blocknummer relativ zum Anfang der Datei"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Komprimierter Startversatz relativ zum Beginn der Datei"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Unkomprimierter Startversatz relativ zum Beginn der Datei"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Komprimierte Gesamtgröße des Blocks (einschließlich Header)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Wenn B<--verbose> zwei Mal angegeben wurde, werden zusätzliche Spalten in die B<block>-Zeilen eingefügt. Diese werden mit einem einfachen B<--verbose> nicht angezeigt, da das Ermitteln dieser Informationen viele Suchvorgänge erfordert und daher recht langsam sein kann:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Wert der Integritätsprüfung in hexadezimaler Notation"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Block-Header-Größe"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Block-Schalter: B<c> gibt an, dass die komprimierte Größe verfügbar ist, und B<u> gibt an, dass die unkomprimierte Größe verfügbar ist. Falls der Schalter nicht gesetzt ist, wird stattdessen ein Bindestrich (B<->) angezeigt, um die Länge der Zeichenkette beizubehalten. In Zukunft könnten neue Schalter am Ende der Zeichenkette hinzugefügt werden."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Größe der tatsächlichen komprimierten Daten im Block. Ausgeschlossen sind hierbei die Block-Header, die Blockauffüllung und die Prüffelder."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Größe des Speichers (in Byte), der zum Dekomprimieren dieses Blocks mit dieser B<xz>-Version benötigt wird."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Filterkette. Beachten Sie, dass die meisten der bei der Kompression verwendeten Optionen nicht bekannt sein können, da in den B<.xz>-Headern nur die für die Dekompression erforderlichen Optionen gespeichert sind."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Die Spalten der B<summary>-Zeilen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Größe des Speichers (in Byte), der zum Dekomprimieren dieser Datei mit dieser B<xz>-Version benötigt wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> oder B<no> geben an, ob in allen Block-Headern sowohl die komprimierte als auch die unkomprimierte Größe gespeichert ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Seit> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Minimale B<xz>-Version, die zur Dekompression der Datei erforderlich ist"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Die Spalten der B<totals>-Zeile:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Anzahl der Datenströme"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Anzahl der Blöcke"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Komprimierte Größe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Durchschnittliches Kompressionsverhältnis"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Durch Kommata getrennte Liste der Namen der Integritätsprüfungen, die in den Dateien präsent waren."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Größe der Datenstromauffüllung"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Anzahl der Dateien. Dies dient dazu, die Reihenfolge der vorigen Spalten an die in den B<file>-Zeilen anzugleichen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Wenn B<--verbose> zwei Mal angegeben wird, werden zusätzliche Spalten in die B<totals>-Zeile eingefügt:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Maximale Größe des Speichers (in Byte), der zum Dekomprimieren der Dateien mit dieser B<xz>-Version benötigt wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Zukünftige Versionen könnten neue Zeilentypen hinzufügen, weiterhin könnten auch in den vorhandenen Zeilentypen weitere Spalten hinzugefügt werden, aber die existierenden Spalten werden nicht geändert."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Filters help"
msgstr "Filterhilfe"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr "B<xz --robot --filters-help> gibt die unterstützten Filter im folgenden Format aus:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr "I<Filter>B<:>I<Option>B<=E<lt>>I<Wert>B<E<gt>,>I<Option>B<=E<lt>>I<Wert>B<E<gt>> …"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<filter>"
msgstr "I<Filter>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the filter"
msgstr "Name des Filters"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<option>"
msgstr "I<Option>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr "Name der filterspezifischen Option"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr "I<Wert>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr "Der numerische I<Wert> erscheint als Bereich B<E<lt>>I<Minimum>B<->I<Maximum>B<E<gt>>. Die Auswahl des Zeichenketten-I<Wert>s wird in B<E<lt> E<gt>> eingeschlossen und durch B<|> getrennt."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr "Jeder Filter wird in einer separaten Zeile ausgegeben."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Informationen zur Speicherbedarfsbegrenzung"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr "B<xz --robot --info-memory> gibt eine einzelne Zeile mit mehreren durch Tabulatoren getrennten Spalten aus:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Gesamter physischer Speicher (RAM) in Byte."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Speicherbedarfsbegrenzung für die Kompression in Byte (B<--memlimit-compress>). Ein spezieller Wert von B<0> bezeichnet die Standardeinstellung, die im Einzelthread-Modus bedeutet, dass keine Begrenzung vorhanden ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Speicherbedarfsbegrenzung für die Dekompression in Byte (B<--memlimit-decompress>). Ein spezieller Wert von B<0> bezeichnet die Standardeinstellung, die im Einzelthread-Modus bedeutet, dass keine Begrenzung vorhanden ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "Seit B<xz> 5.3.4alpha: Die Speichernutzung für Multithread-Dekompression in Byte (B<--memlimit-mt-decompress>). Dies ist niemals B<0>, da ein systemspezifischer Vorgabewert (gezeigt in Spalte 5) verwendet wird, falls keine Grenze ausdrücklich angegeben wurde. Dies ist außerdem niemals größer als der Wert in in Spalte 3, selbst wenn mit B<--memlimit-mt-decompress> ein größerer Wert angegeben wurde."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "Seit B<xz> 5.3.4alpha: Eine systemspezifisch vorgegebene Begrenzung des Speicherverbrauchs, die zur Begrenzung der Anzahl der Threads beim Komprimieren mit automatischer Anzahl der Threads (B<--threads=0>) und wenn keine Speicherbedarfsbegrenzung angegeben wurde (B<--memlimit-compress>) verwendet wird. Dies wird auch als Standardwert für B<--memlimit-mt-decompress> verwendet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "Seit B<xz> 5.3.4alpha: Anzahl der verfügbaren Prozessorthreads."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "In der Zukunft könnte die Ausgabe von B<xz --robot --info-memory> weitere Spalten enthalten, aber niemals mehr als eine einzelne Zeile."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Version"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr "B<xz --robot --version> gibt die Versionsnummern von B<xz> und Liblzma im folgenden Format aus:"

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Hauptversion."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Unterversion. Gerade Zahlen bezeichnen eine stabile Version. Ungerade Zahlen bezeichnen Alpha- oder Betaversionen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Patch-Stufe für stabile Veröffentlichungen oder einfach nur ein Zähler für Entwicklungsversionen."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Stabilität. 0 ist Alpha, 1 ist Beta und 2 ist stabil. I<S> sollte immer 2 sein, wenn I<YYY> eine gerade Zahl ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> sind in beiden Zeilen gleich, sofern B<xz> und Liblzma aus der gleichen Veröffentlichung der XZ-Utils stammen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Beispiele: 4.999.9beta ist B<49990091> und 5.0.0 is B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "EXIT-STATUS"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Alles ist in Ordnung."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "Ein Fehler ist aufgetreten."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Es ist etwas passiert, das eine Warnung rechtfertigt, aber es sind keine tatsächlichen Fehler aufgetreten."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "In die Standardausgabe geschriebene Hinweise (keine Warnungen oder Fehler), welche den Exit-Status nicht beeinflussen."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "UMGEBUNGSVARIABLEN"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> wertet eine durch Leerzeichen getrennte Liste von Optionen in den Umgebungsvariablen B<XZ_DEFAULTS> und B<XZ_OPT> aus (in dieser Reihenfolge), bevor die Optionen aus der Befehlszeile ausgewertet werden. Beachten Sie, dass beim Auswerten der Umgebungsvariablen nur Optionen berücksichtigt werden; alle Einträge, die keine Optionen sind, werden stillschweigend ignoriert. Die Auswertung erfolgt mit B<getopt_long>(3), welches auch für die Befehlszeilenargumente verwendet wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr "B<Warnung:> Durch Setzen dieser Umgebungsvariablen könnte man effektiv Programme und Skripte modifizieren, die B<xz> ausführen. Meist ist es sicher, die Speichernutzungsbegrenzung und Kompressionsoptionen über die Umgebungsvariablen zu setzen. Dennoch können einige Optionen Skripte beeinflussen. Ein typisches Beispiel ist die Option B<--help>, die einen Hilfetext anzeigt, anstatt eine Datei zu komprimieren oder zu dekomprimieren. Weniger augenfällige Beispiele sind die Optionen B<--quiet> und B<--verbose>. In vielen Fällen funktioniert es gut, den Fortschrittsindikator mit B<--verbose> zu aktivieren, aber in einigen Situationen können die zusätzlichen Meldungen Probleme verursachen. Die Ausführlichkeitsstufe beeinflusst auch das Verhalten von B<--list>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Benutzerspezifische oder systemweite Standardoptionen. Typischerweise werden diese in einem Shell-Initialisierungsskript gesetzt, um die Speicherbedarfsbegrenzung von B<xz> standardmäßig zu aktivieren oder die Anzahl der Threads festzulegen. Außer bei Shell-Initialisierungsskripten und in ähnlichen Spezialfällen sollte die Variable B<XZ_DEFAULTS> in Skripten niemals gesetzt oder außer Kraft gesetzt werden."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Dies dient der Übergabe von Optionen an B<xz>, wenn es nicht möglich ist, die Optionen direkt in der Befehlszeile von B<xz> zu übergeben. Dies ist der Fall, wenn B<xz> von einem Skript oder Dienstprogramm ausgeführt wird, zum Beispiel GNU B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Skripte können B<XZ_OPT> zum Beispiel zum Setzen skriptspezifischer Standard-Kompressionsoptionen verwenden. Es ist weiterhin empfehlenswert, Benutzern die Außerkraftsetzung von B<XZ_OPT> zu erlauben, falls dies angemessen ist. Zum Beispiel könnte in B<sh>(1)-Skripten Folgendes stehen:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "KOMPATIBILITÄT ZU DEN LZMA-UTILS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "Die Befehlszeilensyntax von B<xz> ist praktisch eine Obermenge der von B<lzma>, B<unlzma> und B<lzcat> in den LZMA-Utils der Versionen 4.32.x. In den meisten Fällen sollte es möglich sein, die LZMA-Utils durch die XZ-Utils zu ersetzen, ohne vorhandene Skripte ändern zu müssen. Dennoch gibt es einige Inkompatibilitäten, die manchmal Probleme verursachen können."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Voreinstellungsstufen zur Kompression"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "Die Nummerierung der Voreinstellungsstufen der Kompression ist in B<xz> und den LZMA-Utils unterschiedlich. Der wichtigste Unterschied ist die Zuweisung der Wörterbuchgrößen zu den verschiedenen Voreinstellungsstufen. Die Wörterbuchgröße ist etwa gleich dem Speicherbedarf bei der Dekompression."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Stufe"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA-Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "nicht verfügbar"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Die Unterschiede in der Wörterbuchgröße beeinflussen auch den Speicherbedarf bei der Kompression, aber es gibt noch einige andere Unterschiede zwischen den LZMA-Utils und den XZ-Utils, die die Kluft noch vergrößern:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA-Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Die standardmäßige Voreinstellungsstufe in den LZMA-Utils ist B<-7>, während diese in den XZ-Utils B<-6> ist, daher verwenden beide standardmäßig ein 8 MiB großes Wörterbuch."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Vor- und Nachteile von .lzma-Dateien als Datenströme"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "Die unkomprimierte Größe der Datei kann in den B<.lzma>-Headern gespeichert werden. Die LZMA-Utils tun das beim Komprimieren gewöhnlicher Dateien. Als Alternative kann die unkomprimierte Größe als unbekannt markiert und eine Nutzdatenende-Markierung (end-of-payload) verwendet werden, um anzugeben, wo der Dekompressor stoppen soll. Die LZMA-Utils verwenden diese Methode, wenn die unkomprimierte Größe unbekannt ist, was beispielsweise in Pipes (Befehlsverkettungen) der Fall ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> unterstützt die Dekompression von B<.lzma>-Dateien mit oder ohne Nutzdatenende-Markierung, aber alle von B<xz> erstellten B<.lzma>-Dateien verwenden diesen Nutzdatenende-Markierung, wobei die unkomprimierte Größe in den B<.lzma>-Headern als unbekannt markiert wird. Das könnte in einigen unüblichen Situationen ein Problem sein. Zum Beispiel könnte ein B<.lzma>-Dekompressor in einem Gerät mit eingebettetem System nur mit Dateien funktionieren, deren unkomprimierte Größe bekannt ist. Falls Sie auf dieses Problem stoßen, müssen Sie die LZMA-Utils oder das LZMA-SDK verwenden, um B<.lzma>-Dateien mit bekannter unkomprimierter Größe zu erzeugen."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Nicht unterstützte .lzma-Dateien"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "Das B<.lzma>-Format erlaubt I<lc>-Werte bis zu 8 und I<lp>-Werte bis zu 4. Die LZMA-Utils können Dateien mit beliebigem I<lc> und I<lp> dekomprimieren, aber erzeugen immer Dateien mit B<lc=3> und B<lp=0>. Das Erzeugen von Dateien mit anderem I<lc> und I<lp> ist mit B<xz> und mit dem LZMA-SDK möglich."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "Die Implementation des LZMA-Filters in liblzma setzt voraus, dass die Summe von I<lc> und I<lp> nicht größer als 4 ist. Daher können B<.lzma>-Dateien, welche diese Begrenzung überschreiten, mit B<xz> nicht dekomprimiert werden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "Die LZMA-Utils erzeugen nur B<.lzma>-Dateien mit einer Wörterbuchgröße von 2^I<n> (einer Zweierpotenz), aber akzeptieren Dateien mit einer beliebigen Wörterbuchgröße. Liblzma akzeptiert nur B<.lzma>-Dateien mit einer Wörterbuchgröße von 2^I<n> oder 2^I<n> + 2^(I<n>-1). Dies dient zum Verringern von Fehlalarmen beim Erkennen von B<.lzma>-Dateien."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Diese Einschränkungen sollten in der Praxis kein Problem sein, da praktisch alle B<.lzma>-Dateien mit Einstellungen komprimiert wurden, die Liblzma akzeptieren wird."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Angehängter Datenmüll"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Bei der Dekompression ignorieren die LZMA-Utils stillschweigend alles nach dem ersten B<.lzma>-Datenstrom. In den meisten Situationen ist das ein Fehler. Das bedeutet auch, dass die LZMA-Utils die Dekompression verketteter B<.lzma>-Dateien nicht unterstützen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Wenn nach dem ersten B<.lzma>-Datenstrom Daten verbleiben, erachtet B<xz> die Datei als beschädigt, es sei denn, die Option B<--single-stream> wurde verwendet. Dies könnte die Ausführung von Skripten beeinflussen, die davon ausgehen, dass angehängter Datenmüll ignoriert wird."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "ANMERKUNGEN"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "Die komprimierte Ausgabe kann variieren"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "Die exakte komprimierte Ausgabe, die aus der gleichen unkomprimierten Eingabedatei erzeugt wird, kann zwischen den Versionen der XZ-Utils unterschiedlich sein, selbst wenn die Kompressionsoptionen identisch sind. Das kommt daher, weil der Kodierer verbessert worden sein könnte (hinsichtlich schnellerer oder besserer Kompression), ohne das Dateiformat zu beeinflussen. Die Ausgabe kann sogar zwischen verschiedenen Programmen der gleichen Version der XZ-Utils variieren, wenn bei der Erstellung des Binärprogramms unterschiedliche Optionen verwendet wurden."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Sobald B<--rsyncable> implementiert wurde, bedeutet das, dass die sich ergebenden Dateien nicht notwendigerweise mit Rsync abgeglichen werden können, außer wenn die alte und neue Datei mit der gleichen B<xz>-Version erzeugt wurden. Das Problem kann beseitigt werden, wenn ein Teil der Encoder-Implementierung eingefroren wird, um die mit Rsync abgleichbare Ausgabe über B<xz>-Versionsgrenzen hinweg stabil zu halten."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Eingebettete .xz-Dekompressoren"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "Eingebettete B<.xz>-Dekompressor-Implementierungen wie XZ Embedded unterstützen nicht unbedingt Dateien, die mit anderen Integritätsprüfungen (I<Prüfung>-Typen) als B<none> und B<crc32> erzeugt wurden. Da B<--check=crc64> die Voreinstellung ist, müssen Sie B<--check=none> oder B<--check=crc32> verwenden, wenn Sie Dateien für eingebettete Systeme erstellen."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "Außerhalb eingebetteter Systeme unterstützen die Dekompressoren des B<.xz>-Formats alle I<Prüfung>-Typen oder sind mindestens in der Lage, die Datei zu dekomprimieren, ohne deren Integrität zu prüfen, wenn die bestimmte I<Prüfung> nicht verfügbar ist."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded unterstützt BCJ-Filter, aber nur mit dem vorgegebenen Startversatz."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "BEISPIELE"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Grundlagen"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Komprimiert die Datei I<foo> mit der Standard-Kompressionsstufe (B<-6>) zu I<foo.xz> und entfernt I<foo> nach erfolgreicher Kompression:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz foo\\fR\n"
msgstr "\\f(CRxz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "I<bar.xz> in I<bar> dekomprimieren und I<bar.xz> selbst dann nicht löschen, wenn die Dekompression erfolgreich war:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "\\f(CRxz -dk bar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "I<baz.tar.xz> mit der Voreinstellung B<-4e> (B<-4 --extreme>) erzeugen, was langsamer ist als die Vorgabe B<-6>, aber weniger Speicher für Kompression und Dekompression benötigt (48\\ MiB beziehungsweise 5\\ MiB):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Eine Mischung aus komprimierten und unkomprimierten Dateien kann mit einem einzelnen Befehl dekomprimiert in die Standardausgabe geschrieben werden:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Parallele Kompression von vielen Dateien"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "Auf GNU- und *BSD-Systemen können B<find>(1) und B<xargs>(1) zum Parallelisieren der Kompression vieler Dateien verwendet werden:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "Die Option B<-P> von B<xargs>(1) legt die Anzahl der parallelen B<xz>-Prozesse fest. Der beste Wert für die Option B<-n> hängt davon ab, wie viele Dateien komprimiert werden sollen. Wenn es sich nur um wenige Dateien handelt, sollte der Wert wahrscheinlich 1 sein; bei Zehntausenden von Dateien kann 100 oder noch mehr angemessener sein, um die Anzahl der B<xz>-Prozesse zu beschränken, die B<xargs>(1) schließlich erzeugen wird."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "Die Option B<-T1> für B<xz> dient dazu, den Einzelthread-Modus zu erzwingen, da B<xargs>(1) zur Steuerung des Umfangs der Parallelisierung verwendet wird."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Roboter-Modus"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Berechnen, wie viel Byte nach der Kompression mehrerer Dateien insgesamt eingespart wurden:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Ein Skript könnte abfragen wollen, ob es ein B<xz> verwendet, das aktuell genug ist. Das folgende B<sh>(1)-Skript prüft, ob die Versionsnummer des Dienstprogramms B<xz> mindestens 5.0.0 ist. Diese Methode ist zu alten Beta-Versionen kompatibel, welche die Option B<--robot> nicht unterstützen:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Ihre Version von Xz ist zu alt.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Eine Speicherbedarfsbegrenzung für die Dekompression mit B<XZ_OPT> setzen, aber eine bereits gesetzte Begrenzung nicht erhöhen:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "Der einfachste Anwendungsfall für benutzerdefinierte Filterketten ist die Anpassung von LZMA2-Voreinstellungsstufen. Das kann nützlich sein, weil die Voreinstellungen nur einen Teil der potenziell sinnvollen Kombinationen aus Kompressionseinstellungen abdecken."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "Die KompCPU-Spalten der Tabellen aus den Beschreibungen der Optionen B<-0> … B<-9> und B<--extreme> sind beim Anpassen der LZMA2-Voreinstellungen nützlich. Diese sind die relevanten Teile aus diesen zwei Tabellen:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Wenn Sie wissen, dass eine Datei für eine gute Kompression ein etwas größeres Wörterbuch benötigt (zum Beispiel 32 MiB), aber Sie sie schneller komprimieren wollen, als dies mit B<xz -8> geschehen würde, kann eine Voreinstellung mit einem niedrigen KompCPU-Wert (zum Beispiel 1) dahingehend angepasst werden, ein größeres Wörterbuch zu verwenden:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Mit bestimmten Dateien kann der obige Befehl schneller sein als B<xz -6>, wobei die Kompression deutlich besser wird. Dennoch muss betont werden, dass nur wenige Dateien von einem größeren Wörterbuch profitieren, wenn der KompCPU-Wert niedrig bleibt. Der offensichtlichste Fall, in dem ein größeres Wörterbuch  sehr hilfreich sein kann, ist ein Archiv, das einander sehr ähnliche Dateien enthält, die jeweils wenigstens einige Megabyte groß sind. Das Wörterbuch muss dann deutlich größer sein als die einzelne Datei, damit LZMA2 den größtmöglichen Vorteil aus den Ähnlichkeiten der aufeinander folgenden Dateien zieht."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Wenn hoher Speicherbedarf für Kompression und Dekompression kein Problem ist und die zu komprimierende Datei mindestens einige Hundert Megabyte groß ist, kann es sinnvoll sein, ein noch größeres Wörterbuch zu verwenden, als die 64 MiB, die mit B<xz -9> verwendet werden würden:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Die Verwendung von B<-vv> (B<--verbose --verbose>) wie im obigen Beispiel kann nützlich sein, um den Speicherbedarf für Kompressor und Dekompressor zu sehen. Denken Sie daran, dass ein Wörterbuch, das größer als die unkomprimierte Datei ist, Speicherverschwendung wäre. Daher ist der obige Befehl für kleine Dateien nicht sinnvoll."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "Manchmal spielt die Kompressionszeit keine Rolle, aber der Speicherbedarf bei der Dekompression muss gering gehalten werden, zum Beispiel um die Datei auf eingebetteten Systemen dekomprimieren zu können. Der folgende Befehl verwendet B<-6e> (B<-6 --extreme>) als Basis und setzt die Wörterbuchgröße auf nur 64\\ KiB. Die sich ergebende Datei kann mit XZ Embedded (aus diesem Grund ist dort B<--check=crc32>) mit nur etwa 100\\ KiB Speicher dekomprimiert werden."

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Wenn Sie so viele Byte wie möglich herausquetschen wollen, kann die Anpassung der Anzahl der literalen Kontextbits (I<lc>) und der Anzahl der Positionsbits (I<pb>) manchmal hilfreich sein. Auch die Anpassung der Anzahl der literalen Positionsbits (I<lp>) könnte helfen, aber üblicherweise sind I<lc> und I<pb> wichtiger. Wenn ein Quellcode-Archiv zum Beispiel hauptsächlich ASCII-Text enthält, könnte ein Aufruf wie der folgende eine etwas kleinere Datei (etwa 0,1\\ %) ergeben als mit B<xz -6e> (versuchen Sie es auch B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "Die Verwendung eines anderen Filters mit LZMA2 kann die Kompression bei verschiedenen Dateitypen verbessern. So könnten Sie eine gemeinsam genutzte Bibliothek der Architekturen x86-32 oder x86-64 mit dem BCJ-Filter für x86 komprimieren:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Beachten Sie, dass die Reihenfolge der Filteroptionen von Bedeutung ist. Falls B<--x86> nach B<--lzma2> angegeben wird, gibt B<xz> einen Fehler aus, weil nach LZMA2 kein weiterer Filter sein darf und auch weil der BCJ-Filter für x86 nicht als letzter Filter in der Filterkette gesetzt werden darf."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Der Delta-Filter zusammen mit LZMA2 kann bei Bitmap-Bildern gute Ergebnisse liefern. Er sollte üblicherweise besser sein als PNG, welches zwar einige fortgeschrittene Filter als ein simples delta bietet, aber für die eigentliche Kompression »Deflate« verwendet."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "Das Bild muss in einem unkomprimierten Format gespeichert werden, zum Beispiel als unkomprimiertes TIFF. Der Abstandsparameter des Delta-Filters muss so gesetzt werden, dass er der Anzahl der Bytes pro Pixel im Bild entspricht. Zum Beispiel erfordert ein 24-Bit-RGB-Bitmap B<dist=3>, außerdem ist es gut, B<pb=0> an LZMA2 zu übergeben, um die 3-Byte-Ausrichtung zu berücksichtigen:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Wenn sich mehrere Bilder in einem einzelnen Archiv befinden (zum Beispiel\\& B<.tar>), funktioniert der Delta-Filter damit auch, sofern alle Bilder im Archiv die gleiche Anzahl Bytes pro Pixel haben."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "SIEHE AUCH"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr "LZMA-SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr "8. August 2024"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec - Kleine Dekompressoren für .xz und .lzma"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<Option…>] [I<Datei…>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<Option…>] [I<Datei…>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> ist ein auf Liblzma basierendes Nur-Dekompressionswerkzeug für B<.xz>-Dateien (und B<nur> für B<.xz>-Dateien). B<xzdec> ist als direkter Ersatz für B<xz>(1) in jenen Situationen konzipiert, wo ein Skript B<xz --decompress --stdout> (und eventuelle einige andere höufig genutzte Optionen) zum Dekomprimieren von B<.xz>-Dateien. B<lzmadec> ist weitgehend identisch zu B<xzdec>, mit der Ausnahme, dass B<lzmadec> B<.lzma>-Dateien anstelle von B<.xz>-Dateien unterstützt."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Um die Größe der ausführbaren Datei zu reduzieren, unterstützt B<xzdec> weder Multithreading noch Lokalisierung. Außerdem liest es keine Optionen aus den Umgebungsvariablen B<XZ_DEFAULTS> und B<XZ_OPT>. B<xzdec> unterstützt keine zwischenzeitlichen Fortschrittsinformationen: Das Senden von B<SIGINFO> an B<xzdec> hat keine Auswirkungen, jedoch beendet B<SIGUSR1> den Prozess, anstatt Fortschrittsinformationen anzuzeigen."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "ist zwecks Kompatibilität zu B<xz>(1) vorhanden; wird ignoriert. B<xzdec> unterstützt nur Dekompression."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "ist zwecks Kompatibilität zu B<xz>(1) vorhanden; wird ignoriert. B<xzdec> erzeugt oder entfernt niemals Dateien."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "ist zwecks Kompatibilität zu B<xz>(1) vorhanden; wird ignoriert. B<xzdec> schreibt die dekomprimierten Daten immer in die Standardausgabe."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "hat bei einmaliger Angabe keine Wirkung, da B<xzdec> niemals Warnungen oder sonstige Meldungen anzeigt. Wenn Sie dies zweimal angeben, werden Fehlermeldungen unterdrückt."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "ist zwecks Kompatibilität zu B<xz>(1) vorhanden; wird ignoriert. B<xzdec> verwendet niemals den Exit-Status 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "zeigt eine Hilfemeldung an und beendet das Programm erfolgreich."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "zeigt die Versionsnummer von B<xzdec> und liblzma an."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Alles ist in Ordnung."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> gibt keine Warnmeldungen wie B<xz>(1) aus, daher wird der Exit-Status 2 von B<xzdec> nicht verwendet."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Verwenden Sie B<xz>(1) anstelle von B<xzdec> oder B<lzmadec> im normalen täglichen Gebrauch. B<xzdec> oder B<lzmadec> sind nur für Situationen gedacht, in denen ein kleinerer Dekompressor statt des voll ausgestatteten B<xz>(1) wichtig ist."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> und B<lzmadec> sind nicht wirklich extrem klein. Die Größe kann durch Deaktivieren von Funktionen bei der Kompilierung von Liblzma weiter verringert werden, aber das sollte nicht für ausführbare Dateien getan werden, die in typischen Betriebssystemen ausgeliefert werden, außer in den Distributionen für eingebettete Systeme. Wenn Sie einen wirklich winzigen Dekompressor für B<.xz>-Dateien brauchen, sollten Sie stattdessen XZ Embedded in Erwägung ziehen."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30. Juni 2013"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo - im .lzma-Dateikopf enthaltene Informationen anzeigen"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<Datei …>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> zeigt die im B<.lzma>-Dateikopf enthaltenen Informationen an. Es liest die ersten 13 Bytes aus der angegebenen I<Datei>, dekodiert den Dateikopf und gibt das Ergebnis in die Standardausgabe in einem menschenlesbaren Format aus. Falls keine I<Datei>en angegeben werden oder die I<Datei> als B<-> übergeben wird, dann wird aus der Standardeingabe gelesen."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "In der Regel sind die unkomprimierte Größe der Daten und die Größe des Wörterbuchs am bedeutsamsten. Die unkomprimierte Größe kann nur dann angezeigt werden, wenn die Datei im B<.lzma>-Format kein Datenstrom ist. Die Größe des für die Dekompression nötigen Speichers beträgt einige Dutzend Kilobyte zuzüglich der Größe des Inhaltsverzeichnisses."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> ist in den XZ-Dienstprogrammen hauptsächlich zur Kompatibilität zu den LZMA-Dienstprogrammen enthalten."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "FEHLER"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> verwendet B<MB>, während das korrekte Suffix B<MiB> (2^20 Bytes) wäre. Damit wird die Kompatibilität zu den LZMA-Dienstprogrammen gewährleistet."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "2025-03-06"
msgstr "6. März 2025"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff - komprimierte Dateien vergleichen"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xzcmp> [I<Option …>] I<Datei1> [I<Datei2>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr "B<xzdiff> \\&…"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzcmp> \\&…  (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzdiff> \\&…  (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr "B<xzcmp> und B<xzdiff> vergleichen den unkomprimierten Inhalt zweier Dateien. Die unkomprimierten Daten und Optionen werden an B<cmp>(1) oder B<diff>(1) übergeben, sofern weder B<--help> noch B<--version> angegeben wird."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr "Wenn sowohl I<Datei1> als auch I<Datei2> angegeben sind, können diese unkomprimierte Dateien oder Dateien in Formaten sein, die B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) oder B<lz4>(1) dekomprimieren können. Die erforderlichen Dekomprimierungsbefehle werden aus den Dateiendungen von I<Datei1> und I<Datei2> abgeleitet. Für eine Datei mit einer unbekannten Endung wird angenommen, dass sie entweder unkomprimiert ist oder von B<xz>(1) dekomprimiert werden kann."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr "Falls nur ein Dateiname angegeben wird, muss I<Datei1> eine Endung eines unterstützten Kompressionsformat haben und der Name von I<Datei2> wird aus I<Datei1> abgeleitet, indem die Endung des Kompressionsformats entfernt wird."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Die Befehle B<lzcmp> und B<lzdiff> dienen der Abwärtskompatibilität zu den LZMA-Dienstprogrammen. Sie werden als veraltet angesehen und werden in einer zukünftigen Version entfernt."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr "Falls ein Dekompressionsfehler auftritt, ist der Exit-Status B<2>. Anderenfalls wird der Exit-Status von B<cmp>(1) oder B<diff>(1) verwendet."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr "xzgrep - möglicherweise komprimierte Dateien nach Mustern durchsuchen"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xzgrep> [I<Option …>] [I<Musterliste>] [I<Datei …>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> …"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> …"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&…  (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&…  (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&…  (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr "B<xzgrep> ruft B<grep>(1) mit dem unkomprimierten Inhalt von Dateien auf. Die Formate der I<Dateien> werden aus den Dateiendungen abgeleitet. Jede I<Datei> mit einer Endung, die von B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) oder B<lz4>(1) unterstützt wird, wird dekomprimiert; für andere Dateien wird angenommen, dass sie bereits in unkomprimierter Form vorliegen."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr "Wenn keine I<Dateien> angegeben werden oder I<Datei> als B<-> angegeben wird, wird aus der Standardeingabe gelesen. Beim Lesen aus der Standardeingabe werden nur die von B<xz>(1) unterstützten Dateien dekomprimiert. Für andere Dateien wird angenommen, dass sie bereits in unkomprimierter Form vorliegen."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr "Die meisten I<Optionen> von B<grep>(1) werden unterstützt, die folgenden jedoch nicht:"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-r>, B<--recursive>"
msgstr "B<-r>, B<--recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-R>, B<--dereference-recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-d>, B<--directories=>I<Aktion>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-Z>, B<--null>"
msgstr "B<-Z>, B<--null>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-z>, B<--null-data>"
msgstr "B<-z>, B<--null-data>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--include=>I<glob>"
msgstr "B<--include=>I<Glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr "B<--exclude=>I<Glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-from=>I<file>"
msgstr "B<--exclude-from=>I<Datei>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr "B<--exclude-dir=>I<Glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr "B<xzegrep> ist ein Alias für B<xzgrep -E>.  B<xzfgrep> ist ein Alias für B<xzgrep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Die Befehle B<lzgrep>, B<lzegrep> und B<lzfgrep> dienen der Abwärtskompatibilität zu den LZMA-Dienstprogrammen. Sie werden als veraltet angesehen und werden in einer zukünftigen Version entfernt."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "In mindestens einer der Eingabedateien wurde mindestens ein Treffer gefunden. Es sind keine Fehler aufgetreten."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "In keiner der Eingabedateien wurde ein Treffer gefunden. Es sind keine Fehler aufgetreten."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "Ein oder mehrere Fehler sind aufgetreten. Es ist unbekannt, ob Treffer gefunden wurden."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr "Wenn die Umgebungsvariable B<GREP> auf einen nicht-leeren Wert gesetzt ist, wird diese anstelle von B<grep>(1), B<grep -E> oder B<grep -F> verwendet."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless - mit xz oder lzma komprimierte (Text-)Dateien betrachten"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<Datei> …]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<Datei> …] (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr "B<xzless> ist ein Filter, der Text aus komprimierten Dateien in einem Terminal anzeigt. Von B<xz>(1) unterstützte Dateien werden dekomprimiert; für andere Dateien wird angenommen, dass sie bereits in unkomprimierter Form vorliegen. Wenn keine I<Dateien> angegeben werden, liest B<xzless> aus der Standardeingabe."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> verwendet B<less>(1) zur Darstellung der Ausgabe. Im Gegensatz zu B<xzmore> können Sie das zu verwendende Textanzeigeprogramm nicht durch Setzen einer Umgebungsvariable ändern. Die Befehle basieren auf B<more>(1) und B<vi>(1) und ermöglichen Vorwärts- und Rückwärtssprünge sowie Suchvorgänge. In der Handbuchseite zu B<less>(1) finden Sie weitere Informationen."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Der Befehl B<lzless> dient der Abwärtskompatibilität zu den LZMA-Dienstprogrammen. Er wird als veraltet angesehen und wird in einer zukünftigen Version entfernt."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Dies enthält eine Zeichenliste mit Bezug zur Shell. Wenn diese Variable nicht bereits gesetzt ist, wird sie durch B<xzless> gesetzt."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Dies ist auf die Befehlszeile zum Aufruf von B<xz>(1) gesetzt, die zur Vorverarbeitung der Eingabedateien für B<less>(1) nötig ist."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore - mit xz oder lzma komprimierte (Text-)Dateien lesen"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzmore> [I<Datei> …]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzmore> [I<Datei> …] (VERALTET)"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr "B<xzmore> zeigt Text aus komprimierten Dateien mit Hilfe des Pagers (Textanzeigeprogramms) B<more>(1) in einem Terminal an. Von B<xz>(1) unterstützte Dateien werden dekomprimiert; für andere Dateien wird angenommen, dass sie bereits in unkomprimierter Form vorliegen. Wenn keine I<Dateien> angegeben werden, liest B<xzmore> aus der Standardeingabe. Im B<more>(1)-Handbuch finden Sie Informationen zu den Tastaturbefehlen."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr "Beachten Sie, dass Zurückrollen nicht möglich sein könnte, abhängig von der Implementierung von B<more>(1). Das kommt daher, dass B<xzmore> eine Pipe verwendet, um die dekomprimierten Daten an B<more>(1) zu übergeben. B<xzless>(1) verwendet B<less>(1), welches fortgeschrittenere Funktionen bietet."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Der Befehl B<lzmore> dient der Abwärtskompatibilität zu den LZMA-Dienstprogrammen. Er wird als veraltet angesehen und wird in einer zukünftigen Version entfernt."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr "B<PAGER>"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr "Falls die Umgebungsvariable B<PAGER> gesetzt ist, wird deren Wert als Pager (Textanzeigeprogramm) anstelle von B<more>(1) verwendet."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
