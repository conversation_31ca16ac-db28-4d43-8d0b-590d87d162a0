# SPDX-License-Identifier: 0BSD
#
# Chinese translations for xz package.
# This file is published under the BSD Zero Clause License.
#
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019, 2023, 2024
msgid ""
msgstr ""
"Project-Id-Version: xz 5.7.1-dev1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2025-02-02 00:12+0800\n"
"Last-Translator: <PERSON>-<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (traditional) <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-SourceCharset: UTF-8\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s：傳入 --block-list 的參數無效"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s：傳入 --block-list 的參數過多"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "在 --block-list 中，編號「%c:」後方遺漏區塊大小"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 只能作為 --block-list 的最後一個元素"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s：未知檔案格式類型"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s：不支援的完整性檢查類型"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "「--files」或「--files0」只能指定一個檔案。"

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s：%s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "%s 環境變數包含過多參數"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "已在編譯時停用壓縮支援"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "已在編譯時停用解壓縮支援"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "不支援壓縮為 lzip 檔案 (.lz)"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list 只有在壓縮成 .xz 格式時才會生效"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "搭配 --format=raw 時，除非寫入標準輸出，否則需要傳入 --suffix=.SUF"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "最多只能指定 4 個過濾器"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "--filters%s=FILTERS 選項發生錯誤："

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "記憶體用量限制過低，不足以設定指定的過濾器。"

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "--block-list 使用了 %u，但未使用 --filters%u= 指定"

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "不建議在 Raw 模式使用設定檔。"

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "設定檔的選項可能因軟體版本而有異。"

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr ".lzma 格式僅支援 LZMA1 過濾器"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 不能與 .xz 格式一同使用"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "過濾鏈 %u 與 --flush-timeout 不相容"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "因指定 --flush-timeout，因此切換到單執行緒模式"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "過濾鏈 %u 不支援此選項"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "使用最多 %<PRIu32> 個執行緒。"

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "不支援的或過濾器選項"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "解壓縮將需要 %s MiB 的記憶體。"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "已將執行緒數量從 %s 個減少至 %s 個，以不超過記憶體用量的 %s MiB 限制"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "已將執行緒數量從 %s 減少至一個，但依然超出 %s MiB 的自動記憶體用量限制。需要 %s MiB 的記憶體。依然繼續執行。"

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "正在切換至單執行緒模式，以免超出 %s MiB 的記憶體用量限制"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "已將 LZMA%c 的字典大小從 %s MiB 調整至 %s MiB，以不超過記憶體用量的 %s MiB 限制"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "已將 --filters%2$u 的 LZMA%1$c 字典大小，將 %3$s MiB 調整為 %4$s MiB，以避免超過 %5$s MiB 的記憶體用量限制"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "變更為 %u 時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "建立管線時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s：poll() 失敗：%s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s：檔案似乎已經遷移，不移除"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s：無法移除：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s：無法設定檔案所有者：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s：無法設定檔案群組：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s：無法設定檔案權限：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s：同步檔案失敗：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s：同步檔案目錄失敗：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "從標準輸入取得檔案狀態旗標時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s：是個符號連結，跳過"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s：是個目錄，跳過"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s：不是一般檔案，跳過"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s：檔案已設定 setuid 或 setgid 位元，跳過"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s：檔案已設定黏性位元（sticky bit），跳過"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s：輸入檔有超過一個實際連結 (hard link)，跳過"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "空檔名，跳過"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "將狀態旗標還原到標準輸入時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "從標準輸出取得檔案狀態旗標時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Opening the directory failed: %s"
msgstr "%s：開啟目錄失敗：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Destination is not a regular file"
msgstr "%s：目的地不是一般檔案"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "將 O_APPEND 旗標還原到標準輸出時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s：關閉檔案失敗：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s：嘗試建立疏鬆檔案時發生搜尋失敗：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s：讀取時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s：搜尋檔案時發生錯誤：%s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s：非期望的檔案結尾"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s：寫入時發生錯誤：%s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "已停用"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "實體記憶體 (RAM) 數量："

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "處理器執行緒的數量："

#: src/xz/hardware.c
msgid "Compression:"
msgstr "壓縮："

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "解壓縮："

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "多執行緒解壓縮："

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "-T0 的預設值："

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "硬體資訊："

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "記憶體用量上限："

#: src/xz/list.c
msgid "Streams:"
msgstr "串流："

#: src/xz/list.c
msgid "Blocks:"
msgstr "區塊："

#: src/xz/list.c
msgid "Compressed size:"
msgstr "壓縮後大小："

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "壓縮前大小："

#: src/xz/list.c
msgid "Ratio:"
msgstr "壓縮比："

#: src/xz/list.c
msgid "Check:"
msgstr "檢查："

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "串流填充："

#: src/xz/list.c
msgid "Memory needed:"
msgstr "所需記憶體："

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "檔頭中標示大小："

#: src/xz/list.c
msgid "Number of files:"
msgstr "檔案數："

#: src/xz/list.c
msgid "Stream"
msgstr "串流"

#: src/xz/list.c
msgid "Block"
msgstr "區塊"

#: src/xz/list.c
msgid "Blocks"
msgstr "區塊"

#: src/xz/list.c
msgid "CompOffset"
msgstr "壓縮偏移"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "未壓縮偏移"

#: src/xz/list.c
msgid "CompSize"
msgstr "壓縮大小"

#: src/xz/list.c
msgid "UncompSize"
msgstr "未壓縮大小"

#: src/xz/list.c
msgid "TotalSize"
msgstr "總計大小"

#: src/xz/list.c
msgid "Ratio"
msgstr "比率"

#: src/xz/list.c
msgid "Check"
msgstr "檢查"

#: src/xz/list.c
msgid "CheckVal"
msgstr "檢查值"

#: src/xz/list.c
msgid "Padding"
msgstr "補空"

#: src/xz/list.c
msgid "Header"
msgstr "檔頭"

#: src/xz/list.c
msgid "Flags"
msgstr "旗標"

#: src/xz/list.c
msgid "MemUsage"
msgstr "Mem用量"

#: src/xz/list.c
msgid "Filters"
msgstr "過濾器"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "無"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "未知-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "未知-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "未知-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "未知-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "未知-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "未知-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "未知-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "未知-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "未知-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "未知-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "未知-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "未知-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s：檔案是空的"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s：因過小而不認為是個有效 .xz 檔"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr " 串流    區塊       已壓縮       未壓縮   比例  檢驗碼  檔名"

#: src/xz/list.c
msgid "Yes"
msgstr "是"

#: src/xz/list.c
msgid "No"
msgstr "否"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr "最小 XZ 工具程式版本："

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s 個檔案\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "總計："

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list 只能在 .xz 檔使用（--format=xz 或 --format=auto）"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "試試看用「lzmainfo」處理 .lzma 檔案。"

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list 不支援從標準輸入讀取"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s：讀取檔名時發生錯誤：%s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s：讀取檔名時遇到非預期的輸入結尾"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s：讀取檔名時發現空字元；或許您想使用「--files0」而非「--files」？"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "尚未支援搭配 --robot 壓縮和解壓縮。"

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "從標準輸入讀取檔名時，無法從標準輸入讀取資料"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s："

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "內部錯誤（臭蟲）"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "無法確立信號處理器"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "沒有完整性檢查；不驗證檔案完整性"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "未知完整性檢查類型；不驗證檔案完整性"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "達到記憶體用量上限"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "無法識別檔案格式"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "不支援的選項"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "壓縮資料是損壞的"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "遇到非預期輸入結尾"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "需要 %s MiB 的記憶體。已停用記憶體限制器。"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "需要 %s MiB 的記憶體。記憶體限制為 %s。"

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s：過濾鏈：%s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "嘗試「%s --help」取得更多資訊。"

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr "輸出說明文字時發生錯誤（錯誤碼 %d）"

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr "用法：%s [OPTION]... [FILE]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr "用 .xz 格式壓縮，或解壓縮 .xz 格式中的 <檔案>。"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "長選項的必填引數，對短選項也是必填。"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation mode:"
msgstr "操作模式："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force compression"
msgstr "強制壓縮"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force decompression"
msgstr "強制解壓縮"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr "測試壓縮後檔案的完整性"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr "列出更多 .xz 檔案的資訊"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation modifiers:"
msgstr "操作修飾元："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr "保留（不要刪除）輸入檔案"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr "強制覆蓋輸出檔案並壓縮/解壓縮連結"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "write to standard output and don't delete input files"
msgstr "輸出到標準輸出，且不刪除輸入檔案"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr "移除輸入檔案前，不要將輸出檔案同步至儲存裝置"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "僅解壓縮第一個串流，再安靜地忽略可能剩餘的輸入資料"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr "不要在解壓縮時建立稀疏檔案"

#: src/xz/message.c
msgid ".SUF"
msgstr ".SUF"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr "在壓縮後的檔案加入「.SUF」後綴"

#: src/xz/message.c
msgid "FILE"
msgstr "FILE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr "從 FILE 讀取要處理的檔案名稱，若未指定 FILE，\t則從標準輸入讀取檔案名稱。檔案名稱必須以換行字元結尾"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr "類似 --files 但使用 null 字元作為終止字元"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr "基本檔案格式與壓縮選項："

#: src/xz/message.c
msgid "FORMAT"
msgstr "FORMAT"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr "要編解碼的檔案格式，可以輸入的值有「auto」（預設值）、\t「xz」、「lzma」、「lzip」和「raw」"

#: src/xz/message.c
msgid "NAME"
msgstr "NAME"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr "完整性檢查類型：「none」（小心使用）、「crc32」、\t「crc64」（預設值）或「sha256」"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr "解壓縮時不進行完整性檢查"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr "壓縮設定檔。預設值為 6，使用 7-9 壓縮等級前，\t請務必考量壓縮與解壓縮的記憶體用量！"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr "嘗試使用更多 CPU 時間來改善壓縮率\t（不影響解壓縮器的記憶體需求）"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr "NUM"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr "使用最多 NUM 個執行緒。預設為 0，即使用所有的處理器核心"

#: src/xz/message.c
msgid "SIZE"
msgstr "SIZE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr "每處理 SIZE 位元組的輸入資料後，就開始一個新的 .xz 區塊。\t使用這個設定多執行緒壓縮的區塊大小"

#: src/xz/message.c
msgid "BLOCKS"
msgstr "BLOCKS"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr "在指定的未壓縮資料間隔（以逗號分隔）後開始新的 .xz 區塊，\t可以選擇在未壓縮資料大小前加上過濾鏈編號（0-9）\t和冒號「:」"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr "壓縮時，若自上次排清後已超過 NUM 毫秒，\t且讀取更多輸入會造成阻塞，則將所有待處理資料排清"

#: src/xz/message.c
msgid "LIMIT"
msgstr "LIMIT"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr "設定壓縮、解壓縮、多執行緒解壓縮或所有這些模式的記憶體\t上限。\tLIMIT 的單位可以是位元組、\t記憶體佔用百分比，\t或 0 表示預設值"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "如果壓縮設定超過記憶體用量上限，請給出錯誤而非下調設定"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr "自訂壓縮過濾鏈（設定檔以外的替代選項）："

#: src/xz/message.c
msgid "FILTERS"
msgstr "FILTERS"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr "使用 liblzma 過濾器字串語法設定過濾鏈。輸入 --filters-help 取得更多資訊"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr "使用 liblzma 過濾器字串語法設定額外的過濾鏈，以配合 --block-list 使用"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr "顯示有關 liblzma 過濾器字串語法的更多資訊後結束"

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr "OPTS"

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr "LZMA1 或 LZMA2。OPTS 是個以逗號分隔的列表，內有 0 或多個下述選項（有效值；預設值）："

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr "PRE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr "將選項重設為設定檔的值"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr "字典大小"

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
msgid "number of literal context bits"
msgstr "字元上下文位元數"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
msgid "number of literal position bits"
msgstr "字元位置位元數"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
msgid "number of position bits"
msgstr "位置位元的數量"

#: src/xz/message.c
msgid "MODE"
msgstr "MODE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression mode"
msgstr "壓縮模式"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr "符合項目的最佳長度"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr "比對搜尋器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr "最大搜尋深度。0 表示自動（預設值）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr "x86 BCJ 過濾器（32 或 64 位元）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr "ARM BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr "ARM-Thumb BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr "ARM64 BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr "PowerPC BCJ 過濾器（只面向大端序）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr "IA-64 (Itanium) BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr "SPARC BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr "RISC-V BCJ 過濾器"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr "對所有 BCJ 過濾器都有效的 OPTS："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr "轉換的起始位移（預設值=0）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr "差異過濾器。有效 OPTS（有效值；預設值）："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr "相減位元組之間的距離"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Other options:"
msgstr "其他選項："

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr "隱藏警告，指定兩次則一併隱藏錯誤"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr "輸出得更詳細，指定兩次則會輸出得更詳細"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr "即使有警告也不改變結束狀態碼"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use machine-parsable messages (useful for scripts)"
msgstr "輸出機器可以解析的訊息（適合用於指令稿）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "顯示記憶體總量和目前生效的記憶體用量限制後結束"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr "顯示比較短的說明文字（只列出基本選項）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr "顯示較長的說明文字後結束"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr "顯示較短的說明文字後結束"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr "顯示比較長的說明文字（一併列出進階選項）"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the version number and exit"
msgstr "顯示版本號碼後結束"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "With no FILE, or when FILE is -, read standard input."
msgstr "如果 FILE 沒有指定或指定為 -，則從標準輸入讀取。"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "請回報臭蟲至 <%s>（使用英文或芬蘭語）。"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s home page: <%s>"
msgstr "%s 首頁：<%s>"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "此為開發版本，不打算在生產環境使用。"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr "過濾鏈可透過 --filters=FILTERS 或 --filters1=FILTERS ... --filters9=FILTERS 選項來設定。鏈中的每個過濾器可以用空格或 '--' 分隔。您也可以指定 %s 設定檔來取代過濾鏈。"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "支援的過濾器和選項為："

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "選項必須是以逗號分隔的「name=value」值對"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s：選項名稱無效"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Invalid option value"
msgstr "選項值無效"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "不支援的 LZMA1/LZMA2 設定檔：%s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "lc 和 lp 的總和不能超過 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s：檔名有未知後綴，跳過"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s：檔案已有「%s」後綴，略過"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s：檔名後綴無效"

#: src/xz/util.c src/liblzma/common/string_conversion.c
msgid "Value is not a non-negative decimal integer"
msgstr "數值不是非負數十進位整數"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s：乘數後綴無效"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "有效的後綴有「KiB」(2^10)、「MiB」(2^20) 及「GiB」(2^30)。"

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "選項「%s」的數值必須落在 [%<PRIu64>, %<PRIu64>] 範圍內"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "不能從終端機讀入已壓縮資料"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "不能將已壓縮資料寫入終端機"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr "用法：%s [--help] [--version] [FILE]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr "顯示儲存在 .lzma 檔案標頭中的資訊。"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "檔案過小，故不是 .lzma 檔案"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "不是 .lzma 檔案"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "寫入標準輸出失敗"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "未知錯誤"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported preset"
msgstr "不支援的設定檔"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported flag in the preset"
msgstr "設定檔中包含不支援的旗標"

#: src/liblzma/common/string_conversion.c
msgid "Unknown option name"
msgstr "未知選項名稱"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr "選項值不能空白"

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr "數值超出範圍"

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr "這個選項不支援任何乘數後綴"

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "乘數後綴無效（KiB、MiB 或 GiB）"

#: src/liblzma/common/string_conversion.c
msgid "Unknown filter name"
msgstr "未知過濾器名稱"

#: src/liblzma/common/string_conversion.c
msgid "This filter cannot be used in the .xz format"
msgstr "這個過濾器不能在 .xz 格式中使用"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr "分配記憶體失敗"

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr "不允許輸入空白字串，如需預設值請輸入「6」"

#: src/liblzma/common/string_conversion.c
msgid "The maximum number of filters is four"
msgstr "過濾器最多只能指定 4 個"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr "缺少過濾器名稱"

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr "無效的過濾鏈（結尾缺少「lzma2」？）"
