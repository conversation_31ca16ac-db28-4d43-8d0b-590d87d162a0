// SPDX-License-Identifier: 0BSD

// This file has been generated by price_tablegen.c.

#include "range_encoder.h"

const uint8_t lzma_rc_prices[RC_PRICE_TABLE_SIZE] = {
	 128, 103,  91,  84,  78,  73,  69,  66,
	  63,  61,  58,  56,  54,  52,  51,  49,
	  48,  46,  45,  44,  43,  42,  41,  40,
	  39,  38,  37,  36,  35,  34,  34,  33,
	  32,  31,  31,  30,  29,  29,  28,  28,
	  27,  26,  26,  25,  25,  24,  24,  23,
	  23,  22,  22,  22,  21,  21,  20,  20,
	  19,  19,  19,  18,  18,  17,  17,  17,
	  16,  16,  16,  15,  15,  15,  14,  14,
	  14,  13,  13,  13,  12,  12,  12,  11,
	  11,  11,  11,  10,  10,  10,  10,   9,
	   9,   9,   9,   8,   8,   8,   8,   7,
	   7,   7,   7,   6,   6,   6,   6,   5,
	   5,   5,   5,   5,   4,   4,   4,   4,
	   3,   3,   3,   3,   3,   2,   2,   2,
	   2,   2,   2,   1,   1,   1,   1,   1
};
