# SPDX-License-Identifier: 0BSD
#
# Hungarian translation for xz.
# This file is published under the BSD Zero Clause License.
#
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019, 2022, 2024.
msgid ""
msgstr ""
"Project-Id-Version: xz 5.6.0-pre2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2024-02-17 18:35+0100\n"
"Last-Translator: Mesk<PERSON>l<PERSON> <<EMAIL>>\n"
"Language-Team: Hungarian <<EMAIL>>\n"
"Language: hu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"plural-forms: nplurals=2; plural=(n != 1);\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.4\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Érvénytelen argumentum a --block-list kapcsolóhoz"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Túl sok argumentum a --block-list kapcsolóhoz"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "A --block-list kapcsolónál hiányzik a blokkméret a(z) „%c:” szűrőláncszám után"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "A 0 csak utolsó elemként használható a --block-list kapcsolónál"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Ismeretlen fájlformátumtípus"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Nem támogatott integritás-ellenőrzési típus"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Csak egy fájl adható meg a „--files” vagy „--files0” kapcsolóknál."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "A(z) %s környezeti változó túl sok argumentumot tartalmaz"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "A tömörítési támogatás ki lett kapcsolva fordítási időben"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "A kibontási támogatás ki lett kapcsolva fordítási időben"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Az lzip-fájlok (.lz) tömörítése nem támogatott"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "A --block-list kapcsoló csak .xz formátum esetén van figyelembe véve"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "A --format=raw esetén a --suffix=.SUF szükséges, hacsak nem a szabványosra kimenetre ír"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "A szűrők legnagyobb száma négy"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Hiba a --filters%s=SZŰRŐK kapcsolóban:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "A memóriahasználat túl alacsony a megadott szűrőbeállításokhoz."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "A --block-list használja a(z) %u. szűrőláncot, de az nincs megadva a --filters%u= kapcsolóval"

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Az előbeállítások használata nyers módban nem javasolt."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "Az előbeállítások pontos beállításai különbözhetnek a szoftververziók között."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Az .lzma formátum csak az LZMA1 szűrőt támogatja"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "Az LZMA1 nem használható az .xz formátummal"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "A(z) %u. szűrőlánc nem kompatibilis a --flush-timeout kapcsolóval"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Egyszálú módra váltás a --flush-timeout kapcsoló miatt"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Nem támogatott kapcsolók a(z) %u szűrőláncban"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Legfeljebb %<PRIu32> szál használata."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Nem támogatott szűrőlánc vagy szűrőkapcsolók"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "A kibontáshoz %s MiB memória szükséges."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "A szálak számának csökkentése erről: %s, erre: %s, hogy ne lépje túl a(z) %s MiB-os korlátot"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "A szálak számának csökkentése erről: %s, egyre. A(z) %s MiB-os automatikus memóriahasználati korlát így is túl lett lépve. %s MiB memória szükséges. Ennek ellenére folytatás mindenképpen."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Egyszálú módra váltás, hogy ne lépje túl a(z) %s MiB-os memóriahasználati korlátot"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Az LZMA%c szótár méretének módosítása erről: %s MiB, erre: %s MiB, hogy ne lépje túl a(z) %s MiB-os korlátot"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "A --filters%2$u szűrőhöz tartozó LZMA%1$c szótár méretének módosítása %3$s MiB-ról %4$s MiB-ra, hogy ne lépje túl a(z) %5$s MiB-os korlátot"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Hiba a(z) %u. szűrőlánc létrehozásakor: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Hiba a csővezeték létrehozásakor: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: poll() sikertelen: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Úgy tűnik, hogy a fájl át lett helyezve, nincs eltávolítás"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Nem távolítható el: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: A fájl tulajdonosa nem adható meg: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: A fájl csoportja nem adható meg: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: A fájl jogosultságai nem adhatók meg: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: A fájl lezárása sikertelen: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: A fájl lezárása sikertelen: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Hiba a fájl állapotjelzőinek lekérdezésekor a szabványos bemenetről: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Szimbolikus link, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Könyvtár, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Nem szabályos fájl, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: A fájlon setuid vagy setgid bit van beállítva, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: A fájlon sticky bit van beállítva, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: A bemeneti fájlhoz több mint egy hard link tartozik, kihagyás"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Üres fájlnév, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Hiba a fájl állapotjelzőinek visszaállításakor a szabványos bemenetre: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Hiba a fájl állapotjelzőinek lekérdezésekor a szabványos kimenetről: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s: A fájl lezárása sikertelen: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s: Nem szabályos fájl, kihagyás"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Hiba az O_APPEND visszaállításakor a szabványos kimenetre: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: A fájl lezárása sikertelen: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: A pozícionálás sikertelen a ritka fájl létrehozásának kísérletekor: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Olvasási hiba: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Hiba a fájlban pozícionáláskor: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Váratlan fájlvég"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Írási hiba: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Letiltva"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Fizikai memória (RAM) mennyisége:"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Processzorszálak száma:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Tömörítés:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Kibontás:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Többszálás kibontás:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "A -T0 alapértelmezése:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Hardverjellemzők:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Memóriahasználat korlátja:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Adatfolyamok:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Blokkok:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Tömörített méret:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Kibontott méret:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Arány:"

#: src/xz/list.c
msgid "Check:"
msgstr "Ellenőrzés:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Adatfolyam kerete:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Szükséges memória:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Méretek a fejlécekben:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Fájlok száma:"

#: src/xz/list.c
msgid "Stream"
msgstr "Adatfolyam"

#: src/xz/list.c
msgid "Block"
msgstr "Blokk"

#: src/xz/list.c
msgid "Blocks"
msgstr "Blokkok"

#: src/xz/list.c
msgid "CompOffset"
msgstr "Tömörített eltolás"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "Kibontott eltolás"

#: src/xz/list.c
msgid "CompSize"
msgstr "Tömörített méret"

#: src/xz/list.c
msgid "UncompSize"
msgstr "Kibontott méret"

#: src/xz/list.c
msgid "TotalSize"
msgstr "Teljes méret"

#: src/xz/list.c
msgid "Ratio"
msgstr "Arány"

#: src/xz/list.c
msgid "Check"
msgstr "Ellenőrzés"

#: src/xz/list.c
msgid "CheckVal"
msgstr "Ellenőrzőérték"

#: src/xz/list.c
msgid "Padding"
msgstr "Keret"

#: src/xz/list.c
msgid "Header"
msgstr "Fejléc"

#: src/xz/list.c
msgid "Flags"
msgstr "Jelzők"

#: src/xz/list.c
msgid "MemUsage"
msgstr "Memóriahasználat"

#: src/xz/list.c
msgid "Filters"
msgstr "Szűrők"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Nincs"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Névtelen-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Névtelen-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Névtelen-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Névtelen-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Névtelen-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Névtelen-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Névtelen-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Névtelen-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Névtelen-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Névtelen-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Névtelen-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Névtelen-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: A fájl üres"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Túl kicsi, hogy érvényes .xz fájl legyen"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Folyam  Blokkok  Tömörített Kibontott  Arány  Ellenőrzés  Fájlnév"

#: src/xz/list.c
msgid "Yes"
msgstr "Igen"

#: src/xz/list.c
msgid "No"
msgstr "Nem"

#: src/xz/list.c
#, fuzzy
#| msgid "  Minimum XZ Utils version: %s\n"
msgid "Minimum XZ Utils version:"
msgstr "  Legkisebb XZ Utils verzió: %s\n"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s fájl\n"
msgstr[1] "%s fájl\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Összesen:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "A --list csak .xz fájlokkal működik (--format=xz vagy --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Az „lzmainfo” kipróbálása az .lzma fájlok esetén."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "A --list nem támogatja a szabványos bemenetről beolvasást"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Hiba a fájlnevek olvasásakor: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: A bemenet váratlanul véget ért a fájlnevek olvasásakor"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Null karakter található a fájlnevek olvasásakor; talán a „--files0” kapcsolóra gondolt a „--files” helyett?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "A tömörítés és kibontás még nem támogatott a --robot kapcsolóval."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Az adatok nem olvashatók be a szabványos bemenetről a fájlnevek olvasásakor"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Belső hiba (bug)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "A szignálkezelők nem hozhatók létre"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Nincs integritás-ellenőrzés; a fájl épsége nem lesz ellenőrizve"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Nem támogatott integritás-ellenőrzési típus; a fájl épsége nem lesz ellenőrizve"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Memóriahasználat korlátja elérve"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "A fájlformátum nem felismert"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Nem támogatott kapcsolók"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "A tömörített adatok megsérültek"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "A bemenet váratlanul véget ért"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s MiB memória szükséges. A korlátozás letiltva."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s MiB memória szükséges. A korlát %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Szűrőlánc: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "További információkért adja ki a következő parancsot: „%s --help”."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "Usage: %s [OPTION]... [FILE]...\n"
#| "Compress or decompress FILEs in the .xz format.\n"
#| "\n"
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""
"Használat: %s [KAPCSOLÓ]... [FÁJL]...\n"
".xz formátumú FÁJLok tömörítése vagy kibontása.\n"
"\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "A hosszú kapcsolók kötelező argumentumai a rövid kapcsolók esetén is kötelezők.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr " Működési mód:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force compression"
msgstr "Kibontás:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force decompression"
msgstr "Kibontás:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
" Műveleti módosítók:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "A szabványos kimenetre írás sikertelen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --single-stream decompress only the first stream, and silently\n"
#| "                      ignore possible remaining input data"
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr ""
"      --single-stream csak az első adatfolyam kibontása, és a\n"
"                      lehetséges hátralévő bemeneti adatok mellőzése"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Basic file format and compression options:\n"
msgid "Basic file format and compression options:"
msgstr ""
"\n"
" Alapvető fájlformátum és tömörítési beállítások:\n"

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --ignore-check  don't verify the integrity check when decompressing"
msgid "don't verify the integrity check when decompressing"
msgstr "      --ignore-check  kibontáskor ne ellenőrizze az épséget"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -0 ... -9           compression preset; default is 6; take compressor *and*\n"
#| "                      decompressor memory usage into account before using 7-9!"
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""
"  -0 ... -9           tömörítési előbeállítás; alapértelmezett a 6;\n"
"                      a 7-9 használata előtt vegye figyelembe a tömörítő\n"
"                      *és* kibontó memóriahasználatát!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -e, --extreme       try to improve compression ratio by using more CPU time;\n"
#| "                      does not affect decompressor memory requirements"
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""
"  -e, --extreme       a tömörítési arány javítási kísérlete több CPU-idő\n"
"                      használatával; nincs hatással a kibontó memóriaigényére"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -T, --threads=NUM   use at most NUM threads; the default is 0 which uses\n"
#| "                      as many threads as there are processor cores"
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""
"  -T, --threads=SZÁM  legfeljebb ennyi szál használata; alapértelmezett a 0,\n"
"                      amely annyi szálat használ, amennyi processzormag áll\n"
"                      rendelkezésre"

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-size=SIZE\n"
#| "                      start a new .xz block after every SIZE bytes of input;\n"
#| "                      use this to set the block size for threaded compression"
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""
"      --block-size=MÉRET\n"
"                      új .xz blokk indítása minden MÉRETnyi bájt bemenet után;\n"
"                      a többszálas tömörítés blokkméretének megadásához"

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-list=BLOCKS\n"
#| "                      start a new .xz block after the given comma-separated\n"
#| "                      intervals of uncompressed data; optionally, specify a\n"
#| "                      filter chain number (0-9) followed by a ':' before the\n"
#| "                      uncompressed data size"
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""
"      --block-list=BLOKKOK\n"
"                      új .xz blokk indítása a vesszőkkel felsorolva megadott\n"
"                      méretű tömörítetlen adatszakaszok után; a tömörítetlen\n"
"                      adatok mérete előtt egy szűrőláncszám (0-9) is megadható\n"
"                      egy „:” karakter közbeiktatásával"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --flush-timeout=TIMEOUT\n"
#| "                      when compressing, if more than TIMEOUT milliseconds has\n"
#| "                      passed since the previous flush and reading more input\n"
#| "                      would block, all pending data is flushed out"
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""
"      --flush-timeout=IDŐTÚLLÉPÉS\n"
"                      tömörítéskor, ha több mint IDŐTÚLLÉPÉS ezredmásodperc\n"
"                      telt el az előző kiírástól, és a bemenetolvasás\n"
"                      blokkolna, akkor minden adat ki lesz írva"

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --no-adjust     if compression settings exceed the memory usage limit,\n"
#| "                      give an error instead of adjusting the settings downwards"
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr ""
"      --no-adjust     ha a tömörítési beállítások túllépik a memóriahasználati\n"
"                      korlátot, akkor hibát fog adni a beállítások lefelé\n"
"                      állítása helyett"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Custom filter chain for compression (alternative for using presets):"
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""
"\n"
" Egyéni szűrőlánc a tömörítéshez (alternatíva az előbeállításokra):"

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| "  --filters=FILTERS   set the filter chain using the liblzma filter string\n"
#| "                      syntax; use --filters-help for more information"
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""
"\n"
"  --filters=SZŰRŐK    a szűrőlánc beállítása liblzma szűrőformátummal;\n"
"                      további információkért lásd a --filters-help kapcsolót"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters1=FILTERS ... --filters9=FILTERS\n"
#| "                      set additional filter chains using the liblzma filter\n"
#| "                      string syntax to use with --block-list"
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""
"  --filters1=SZŰRŐK ... --filters9=SZŰRŐK\n"
"                      további szűrőláncok beállítása liblzma formátummal a\n"
"                      --block-list kapcsolóhoz"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters-help      display more information about the liblzma filter string\n"
#| "                      syntax and exit."
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""
"  --filters-help      további információk megjelenítése a libzma\n"
"                      szűrőformátumról, majd kilépés."

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Number of processor threads:"
msgid "number of position bits"
msgstr "Processzorszálak száma:"

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "compression mode"
msgstr "Kibontás:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
" Egyéb kapcsolók:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -Q, --no-warn       make warnings not affect the exit status"
msgid "make warnings not affect the exit status"
msgstr ""
"  -Q, --no-warn       a figyelmeztetések nem befolyásolják a kilépési\n"
"                      állapotkódot"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr ""
"      --robot         géppel értelmezhető üzenetek használata\n"
"                      (parancsfájlok esetén hasznos)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --info-memory   display the total amount of RAM and the currently active\n"
#| "                      memory usage limits, and exit"
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr ""
"      --info-memory   az összes RAM mennyiségének és a jelenlegi\n"
"                      memóriahasználati korlátok megjelenítése, és kilépés"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr "  -V, --version       a verziószám kiírása és kilépés"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"FÁJL nélkül, vagy ha a FÁJL -, olvasás a szabványos bemenetről.\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "Ide jelentse a hibákat: <%s> (angolul vagy finnül).\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "%s honlap: <%s>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "EZ EGY FEJLESZTŐI VÁLTOZAT, NEM ÉLES HASZNÁLATRA SZÁNT."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy, c-format
#| msgid ""
#| "Filter chains are set using the --filters=FILTERS or\n"
#| "--filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain\n"
#| "can be separated by spaces or '--'. Alternatively a preset <0-9>[e] can be\n"
#| "specified instead of a filter chain.\n"
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""
"A szűrőláncokat a --filters=SZŰRŐK vagy a\n"
"--filters1=SZŰRŐK ... --filters9=SZŰRŐK kapcsolókkal lehet megadni. A láncban\n"
"szereplő egyes szűrőket szóközökkel vagy „--” karakterekkel kell elválasztani.\n"
"Illetve egy <0-9>[e] előbeállítás is megadható szűrőlánc helyett.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "A támogatott szűrők és azok kapcsolói a következők:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be 'name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s: A kapcsolóknak vesszőkkel elválasztott „név=érték” pároknak kell lenniük"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Érvénytelen kapcsolónév"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s: Érvénytelen kapcsolóérték"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "Nem támogatott LZMA1/LZMA2 előbeállítás: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Az lc és lp összege nem haladhatja meg a 4-et"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: A fájlnév utótagja ismeretlen, kihagyás"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: A fájlnak már van „%s” utótagja, kihagyás"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Érvénytelen fájlnév utótag"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s: Az érték nem nemnegatív decimális egész szám"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Érvénytelen szorzó utótag"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Az érvényes utótagok: „KiB” (2^10), „MiB” (2^20) és „GiB” (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "A(z) „%s” kapcsoló értékének a(z) [%<PRIu64>, %<PRIu64>] tartományban kell lennie"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "A tömörített adatokat nem lehet beolvasni a terminálból"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "A tömörített adatokat nem lehet kiírni a terminálba"

#: src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""
"Használat: %s [--help] [--version] [FÁJL]…\n"
"Az .lzma fájl fejlécében tárolt információk megjelenítése"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Show information stored in the .lzma file header."
msgstr ""
"Használat: %s [--help] [--version] [FÁJL]…\n"
"Az .lzma fájl fejlécében tárolt információk megjelenítése"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "A fájl túl kicsi, hogy érvényes .lzma fájl legyen"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Nem .lzma fájl"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "A szabványos kimenetre írás sikertelen"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Ismeretlen hiba"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "Nem támogatott kapcsolók"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "Nem támogatott szűrőlánc vagy szűrőkapcsolók"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s: Érvénytelen kapcsolónév"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s: Érvénytelen szorzó utótag"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s: Ismeretlen fájlformátumtípus"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "Az LZMA1 nem használható az .xz formátummal"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "A szűrők legnagyobb száma négy"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -z, --compress      force compression\n"
#~ "  -d, --decompress    force decompression\n"
#~ "  -t, --test          test compressed file integrity\n"
#~ "  -l, --list          list information about .xz files"
#~ msgstr ""
#~ "  -z, --compress      kényszerített tömörítés\n"
#~ "  -d, --decompress    kényszerített kibontás\n"
#~ "  -t, --test          tömörített fájl épségének tesztelése\n"
#~ "  -l, --list          információk kiírása az .xz fájlokról"

#~ msgid ""
#~ "  -k, --keep          keep (don't delete) input files\n"
#~ "  -f, --force         force overwrite of output file and (de)compress links\n"
#~ "  -c, --stdout        write to standard output and don't delete input files"
#~ msgstr ""
#~ "  -k, --keep          bemeneti fájlok megtartása (ne törölje)\n"
#~ "  -f, --force         kimeneti fájl kényszerített felülírása,\n"
#~ "                      és a linkek tömörítése/kibontása\n"
#~ "  -c, --stdout        írás a szabványos kimenetre írás, és nem törli a\n"
#~ "                      bemeneti fájlokat"

#~ msgid ""
#~ "      --no-sparse     do not create sparse files when decompressing\n"
#~ "  -S, --suffix=.SUF   use the suffix '.SUF' on compressed files\n"
#~ "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~ "                      omitted, filenames are read from the standard input;\n"
#~ "                      filenames must be terminated with the newline character\n"
#~ "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgstr ""
#~ "      --no-sparse     ne hozzon létre ritka fájlokat kibontáskor\n"
#~ "  -S, --suffix=.SUF   a „.SUF” utótag használata a tömörített fájlokon\n"
#~ "      --files[=FÁJL]  fájlnevek beolvasása a FÁJLból; ha a FÁJL nincs\n"
#~ "                      megadva, akkor a fájlnevek a szabványos bemenetről\n"
#~ "                      lesznek beolvasva; a fájlneveket újsor karakterrel\n"
#~ "                      kell zárni\n"
#~ "      --files0[=FÁJL] mint a --files, de a null karaktert használja\n"
#~ "                      használja elválasztóként"

#~ msgid ""
#~ "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~ "                      'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'\n"
#~ "  -C, --check=CHECK   integrity check type: 'none' (use with caution),\n"
#~ "                      'crc32', 'crc64' (default), or 'sha256'"
#~ msgstr ""
#~ "  -F, --format=FMT    a kódoláshoz vagy dekódoláshoz használt fájlformátum;\n"
#~ "                      lehetséges értékek „auto” (alapértelmezett), „xz”,\n"
#~ "                      „lzma”, „lzip” és „raw”\n"
#~ "  -C, --check=ELL     integritás-ellenőrzés típusa: „none” (óvatosan használja),\n"
#~ "                      „crc32”, „crc64” (alapértelmezett) vagy „sha256”"

#, no-c-format
#~ msgid ""
#~ "      --memlimit-compress=LIMIT\n"
#~ "      --memlimit-decompress=LIMIT\n"
#~ "      --memlimit-mt-decompress=LIMIT\n"
#~ "  -M, --memlimit=LIMIT\n"
#~ "                      set memory usage limit for compression, decompression,\n"
#~ "                      threaded decompression, or all of these; LIMIT is in\n"
#~ "                      bytes, % of RAM, or 0 for defaults"
#~ msgstr ""
#~ "      --memlimit-compress=KORLÁT\n"
#~ "      --memlimit-decompress=KORLÁT\n"
#~ "      --memlimit-mt-decompress=KORLÁT\n"
#~ "  -M, --memlimit=KORLÁT\n"
#~ "                      a memóriahasználati korlát megadása tömörítéshez,\n"
#~ "                      kibontáshoz, többszálú kibontású vagy mindháromhoz; a\n"
#~ "                      KORLÁT bájtokban van megadva, a RAM %-ában, vagy 0 az\n"
#~ "                      alapértelmezéshez"

#~ msgid ""
#~ "\n"
#~ "  --lzma1[=OPTS]      LZMA1 or LZMA2; OPTS is a comma-separated list of zero or\n"
#~ "  --lzma2[=OPTS]      more of the following options (valid values; default):\n"
#~ "                        preset=PRE reset options to a preset (0-9[e])\n"
#~ "                        dict=NUM   dictionary size (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=NUM     number of literal context bits (0-4; 3)\n"
#~ "                        lp=NUM     number of literal position bits (0-4; 0)\n"
#~ "                        pb=NUM     number of position bits (0-4; 2)\n"
#~ "                        mode=MODE  compression mode (fast, normal; normal)\n"
#~ "                        nice=NUM   nice length of a match (2-273; 64)\n"
#~ "                        mf=NAME    match finder (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=NUM  maximum search depth; 0=automatic (default)"
#~ msgstr ""
#~ "\n"
#~ "  --lzma1[=KAPCS]     LZMA1 vagy LZMA2; a KAPCS nulla vagy több vesszővel\n"
#~ "  --lzma2[=KAPCS]     elválasztott kapcsoló az alábbiak közül\n"
#~ "                      (érvényes érték; alapértelmezett):\n"
#~ "                        preset=ELŐ visszaállítás egy előbeállításra (0-9[e])\n"
#~ "                        dict=SZÁM  szótárméret (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=SZÁM    literál környezeti bitek száma (0-4; 3)\n"
#~ "                        lp=SZÁM    literál pozícióbitek száma (0-4; 0)\n"
#~ "                        pb=SZÁM    pozícióbitek száma (0-4; 2)\n"
#~ "                        mode=MÓD   tömörítési mód (fast, normal; normal)\n"
#~ "                        nice=SZÁM  az egyezés „nice” hossza (2-273; 64)\n"
#~ "                        mf=NÉV     egyezéskereső (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=SZÁM legnagyobb keresési mélység; 0=automatikus\n"
#~ "                                   (alapértelmezett)"

#~ msgid ""
#~ "\n"
#~ "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~ "  --arm[=OPTS]        ARM BCJ filter\n"
#~ "  --armthumb[=OPTS]   ARM-Thumb BCJ filter\n"
#~ "  --arm64[=OPTS]      ARM64 BCJ filter\n"
#~ "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~ "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~ "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~ "  --riscv[=OPTS]      RISC-V BCJ filter\n"
#~ "                      Valid OPTS for all BCJ filters:\n"
#~ "                        start=NUM  start offset for conversions (default=0)"
#~ msgstr ""
#~ "\n"
#~ "  --x86[=KAPCS]       x86 BCJ szűrő (32 bites és 64 bites)\n"
#~ "  --arm[=KAPCS]       ARM BCJ szűrő\n"
#~ "  --armthumb[=KAPCS]  ARM-Thumb BCJ szűrő\n"
#~ "  --arm64[=KAPCS]     ARM64 BCJ szűrő\n"
#~ "  --powerpc[=KAPCS]   PowerPC BCJ szűrő (csak big endian esetén)\n"
#~ "  --ia64[=KAPCS]      IA-64 (Itanium) BCJ szűrő\n"
#~ "  --sparc[=KAPCS]     SPARC BCJ szűrő\n"
#~ "  --riscv[=KAPCS]     RISC-V BCJ szűrő\n"
#~ "                      Érvényes KAPCS az összes BCJ szűrőhöz:\n"
#~ "                        start=SZÁM  kezdési eltolás az átalakításokhoz\n"
#~ "                                    (alapértelmezett=0)"

#~ msgid ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta filter; valid OPTS (valid values; default):\n"
#~ "                        dist=NUM   distance between bytes being subtracted\n"
#~ "                                   from each other (1-256; 1)"
#~ msgstr ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta szűrő; érvényes KAPCSOLÓK\n"
#~ "                      (érvényes értékek; alapértelmezett default):\n"
#~ "                        dist=SZÁM  az egymásból kivont bájtok közti\n"
#~ "                                   távolság (1-256; 1)"

#~ msgid ""
#~ "  -q, --quiet         suppress warnings; specify twice to suppress errors too\n"
#~ "  -v, --verbose       be verbose; specify twice for even more verbose"
#~ msgstr ""
#~ "  -q, --quiet         figyelmeztetések elrejtése; adja meg kétszer, hogy a\n"
#~ "                      hibákat is elrejtse\n"
#~ "  -v, --verbose       legyen bőbeszédű; adja meg kétszer, hogy még bőbeszédűbb\n"
#~ "                      legyen"

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ "  -h, --help          a rövid súgó megjelenítése (csak az alapvető kapcsolók)\n"
#~ "  -H, --long-help     ezen hosszú súgó megjelenítése, és kilépés"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ "  -h, --help          ezen rövid súgó megjelenítése, és kilépés\n"
#~ "  -H, --long-help     a hosszú súgó megjelenítése (speciális kapcsolókhoz)"

#~ msgid "Failed to enable the sandbox"
#~ msgstr "A homokozó engedélyezése sikertelen"

#, c-format
#~ msgid "The selected match finder requires at least nice=%<PRIu32>"
#~ msgstr "A kiválasztott egyezéskeresőhöz legalább nice=%<PRIu32> szükséges"

#~ msgid "Sandbox is disabled due to incompatible command line arguments"
#~ msgstr "A homokozó ki lett kapcsolva a nem kompatibilis parancssori argumentumok miatt"

#~ msgid "Sandbox was successfully enabled"
#~ msgstr "A homokozó sikeresen engedélyezve"

#~ msgid "Memory usage limit for compression:    "
#~ msgstr "Memóriahasználat korlátja tömörítéskor: "

#~ msgid "  Streams:            %s\n"
#~ msgstr "  Adatfolyamok:       %s\n"

#~ msgid "  Blocks:             %s\n"
#~ msgstr "  Blokkok:            %s\n"

#~ msgid "  Ratio:              %s\n"
#~ msgstr "  Arány:              %s\n"

#~ msgid "  Check:              %s\n"
#~ msgstr "  Ellenőrzés:         %s\n"

#~ msgid ""
#~ "  Streams:\n"
#~ "    Stream    Blocks      CompOffset    UncompOffset        CompSize      UncompSize  Ratio  Check      Padding"
#~ msgstr ""
#~ "  Adatfolyamok:\n"
#~ "    Folyam    Blokkok      TömEltolás    KibEltolás        TömMéret      KibMéret  Arány  Ellenőrzés      Keret"

#~ msgid ""
#~ "  Blocks:\n"
#~ "    Stream     Block      CompOffset    UncompOffset       TotalSize      UncompSize  Ratio  Check"
#~ msgstr ""
#~ "  Blokkok:\n"
#~ "    Folyam    Blokkok      TömEltolás    KibEltolás        TömMéret      KibMéret  Arány  Ellenőrzés"

#~ msgid "      CheckVal %*s Header  Flags        CompSize    MemUsage  Filters"
#~ msgstr "      ÉrtékEll %*s Fejléc  Jelzők        TömMéret    MemHasználat  Szűrők"
