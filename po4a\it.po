# SPDX-License-Identifier: 0BSD
#
# Italian translations for xz-man package
# This file is published under the BSD Zero Clause License.
# <PERSON> <<EMAIL>>, 2024-2025
#
msgid ""
msgstr ""
"Project-Id-Version: xz-man 5.8.0-pre1\n"
"POT-Creation-Date: 2025-03-08 14:50+0200\n"
"PO-Revision-Date: 2025-03-10 08:02+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=n!=1;\n"
"X-Loco-Source-Locale: it_IT\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Loco-Parser: loco_parse_po\n"
"X-Generator: Loco https://localise.biz/\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr "08/03/2025"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "XZ Utils"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "NOME"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat - Comprime o decomprime file .xz e .lzma"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "SINOSSI"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<OPZIONE...>] [I<FILE...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "ALIAS DEI COMANDI"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> è equivalente a B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> è equivalente a B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> è equivalente a B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> è equivalente a B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> è equivalente a B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Quando si scrivono script che richiedono di decomprimere file, si raccomanda di utilizzare sempre il comando B<xz> con argomenti appropriati (B<xz -d> o B<xz -dc>) al posto degli alias B<unxz> e B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "DESCRIZIONE"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> è uno strumento di compressione dati generico con sintassi della riga di comando simile a B<gzip>(1)  e B<bzip2>(1).  Il formato file nativo è B<.xz>, ma sono supportati anche il formato tradizionale B<.lzma> usato dalle LZMA Utils e i flussi grezzi (raw) compressi senza intestazioni di formato contenitore. In più, è supportata la decompressione del formato B<.lz>usato da B<lzip>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> comprime o decomprime ogni I<FILE> a seconda della modalità di funzionamento selezionata. Se nessun I<FILE> è indicato o se I<FILE> è B<->, B<xz> legge dallo standard input e scrive i dati processati sullo standard output. B<xz> si rifiuterà (stamperà un errore e salterà il I<FILE>) di scrivere dati compressi sullo standard output se è il terminale. Analogamente, B<xz> si rifiuterà di leggere dati compressi dallo standard input se è il terminale."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "A meno che non sia specificato B<--stdout>, i I<FILE> diversi da B<-> vengono scritti in un nuovo file il cui nome deriva dal nome del I<FILE> sorgente:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "Quando si comprime, il suffisso del file destinazione (B<.xz> or B<.lzma>) viene accodato al nome del file sorgente per ottenere il nome del file destinazione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "Quando si decomprime, i suffissi B<.xz>, B<.lzma>, o B<.lz> vengono rimossi dal nome del file per ottenere il nome del file destinazione. B<xz> riconosce anche i suffissi B<.txz> e B<.tlz>, e li sostituisce con il suffisso B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Se il file di destinazione esiste già, viene visualizzato un errore e I<FILE> viene saltato."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "A meno che non si scriva nello standard output, B<xz> visualizzerà un avvertimento e salterà il I<FILE> se si verifica una delle seguenti condizioni:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<FILE> non è un file regolare. I collegamenti simbolici non vengono seguiti, quindi non sono considerati file regolari."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "I<FILE> ha più di un collegamento \"hard\"."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "I<FILE> ha impostati i bit setuid, setgid, o sticky."

# FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "La modalità di funzionamento è impostata sulla compressione e il I<FILE> ha già un suffisso del formato di file di destinazione (B<.xz> o B<.txz> quando si comprime nel formato B<.xz> e B<.lzma> o B<.tlz> quando si comprime nel formato B<.lzma>)."

# FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "La modalità di funzionamento è impostata sulla decompressione e il I<FILE> non ha un suffisso di nessuno dei formati di file supportati (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, o B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Dopo aver compresso o decompresso correttamente il I<FILE>, B<xz> copia il proprietario, il gruppo, le autorizzazioni, l'ora di accesso e l'ora di modifica dal I<FILE> di origine al file di destinazione. Se la copia del gruppo fallisce, le autorizzazioni vengono modificate in modo che il file di destinazione non diventi accessibile agli utenti che non disponevano dell'autorizzazione per accedere al I<FILE> di origine. B<xz> non supporta ancora la copia di altri metadati, ad esempio elenchi di controllo degli accessi o attributi estesi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Una volta che il file di destinazione è stato chiuso con successo, il I<FILE> sorgente viene rimosso, a meno che sia stato specificato B<--keep>. Il I<FILE> sorgente non viene mai rimosso se l'output è scritto sullo standard output, né se si verifica un errore."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "L'invio di B<SIGINFO> o B<SIGUSR1> al processo B<xz> comporta la stampa delle informazioni sullo stato di avanzamento sullo standard error. Questo ha solo un uso limitato poiché quando lo standard error è un terminale, utilizzando B<--verbose> verrà visualizzato un indicatore di avanzamento che si aggiorna automaticamente."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Utilizzo memoria"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "L'utilizzo della memoria di B<xz> varia da poche centinaia di kilobyte a diversi gigabyte a seconda delle impostazioni di compressione. Le impostazioni utilizzate durante la compressione di un file determinano i requisiti di memoria del decompressore. In genere il decompressore richiede dal 5\\% al 20\\% della quantità di memoria necessaria al compressore durante la creazione del file. Ad esempio, la decompressione di un file creato con B<xz -9> al momento richiede 65\\ MiB di memoria. Ancora, è possibile avere file B<.xz> che richiedono diversi gigabyte di memoria per la decompressione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "Soprattutto gli utenti di sistemi più vecchi possono trovare fastidiosa l'eventualità di un utilizzo molto elevato di memoria. Per evitare spiacevoli sorprese, B<xz> dispone di un limitatore di utilizzo della memoria incorporato, che è disabilitato per impostazione predefinita. Anche se alcuni sistemi operativi forniscono modi per limitare l'utilizzo della memoria dei processi, fare affidamento su questi non è stato ritenuto sufficientemente flessibile (ad esempio, l'uso di B<ulimit>(1) per limitare la memoria virtuale tende a paralizzare B<mmap>(2))."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "Il limitatore di utilizzo della memoria può essere abilitato con l'opzione della riga di comando B<--memlimit=>I<LIMITE>. Spesso è più conveniente abilitare il limitatore per impostazione predefinita impostando la variabile d'ambiente B<XZ_DEFAULTS>, ad esempio, B<XZ_DEFAULTS=--memlimit=150MiB>. È possibile impostare separatamente i limiti per la compressione e la decompressione utilizzando B<--memlimit-compress=>I<LIMITE> and B<--memlimit-decompress=>I<LIMITE>. L'uso di queste due opzioni al di fuori di B<XZ_DEFAULTS> è raramente utile perché una singola esecuzione di B<xz> non può eseguire sia la compressione che la decompressione e B<--memlimit=>I<LIMITE> (or B<-M> I<LIMITE>) è più breve da digitare sulla riga di comando."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Se il limite di utilizzo della memoria specificato viene superato durante la decompressione, B<xz> visualizzerà un errore e la decompressione del file fallirà. Se il limite viene superato durante la compressione, B<xz> tenterà di ridimensionare le impostazioni in modo che il limite non venga più superato (tranne quando si usa B<--format=raw> o B<--no-adjust>). In questo modo l'operazione non fallirà a meno che il limite sia molto basso. Il ridimensionamento delle impostazioni viene eseguito in piccole differenze che non corrispondono ai livelli di compressione preimpostati, ad esempio, se il limite è solo leggermente inferiore alla quantità richiesta per B<xz -9>, le impostazioni saranno ridimensionate solo un poco, non proprio fino a B<xz -8>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Concatenazione e padding con file .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "È possibile concatenare i file B<.xz> così come sono. B<xz> decomprimerà tali file come se fossero un singolo file B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "È possibile inserire un padding tra le parti concatenate o dopo l'ultima parte. Il padding deve essere costituito da byte \"null\" e la dimensione del padding deve essere un multiplo di quattro byte. Questo può essere utile, ad esempio, se il file B<.xz> è memorizzato su un supporto che misura le dimensioni dei file in blocchi di 512 byte."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Concatenazione e padding non sono permessi con file B<.lzma> o con flussi grezzi."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "OPZIONI"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Suffissi interi e valori speciali"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "Nella maggior parte dei casi in cui è previsto un argomento intero, è supportato un suffisso facoltativo per indicare facilmente numeri interi di grandi dimensioni. Non deve esserci alcuno spazio tra il numero intero e il suffisso."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Moltiplica l'intero per 1024 (2^10).   B<Ki>, B<k>, B<kB>, B<K>, e B<KB> sono accettati come sinonimi di B<KiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Moltiplica l'intero per 1.048.576 (2^20).   B<Mi>, B<m>, B<M>, e B<MB> sono accettati come sinonimi di B<MiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Moltiplica l'intero per 1.073.741.824 (2^30).   B<Gi>, B<g>, B<G>, e B<GB> sono accettati come sinonimi di B<GiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "Il valore speciale B<max> può essere utilizzato per indicare il valore intero massimo supportato dall'opzione."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Modalità operativa"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Se vengono fornite più opzioni di modalità operativa, l'ultima ha effetto."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Compressione. Questa è la modalità operativa predefinita quando non viene specificata alcuna opzione della modalità operativa e non è determinata nessun'altra modalità operativa dal nome del comando (ad esempio, B<unxz> implica B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Dopo che la compressione è terminata con successo, il file origine viene rimosso a meno che si stia scrivendo su standard output o che venga specificato B<--keep>."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Decompressione. Dopo che la decompressione è terminata con successo, il file origine viene rimosso a meno che si stia scrivendo su standard output o che venga specificato B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Testa l'integrità dei I<FILE> compressi. Questa opzione è equivalente a B<--decompress --stdout> tranne per il fatto che i dati decompressi vengono scartati invece di essere scritti nello standard output. Nessun file viene creato o rimosso."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Stampa le informazioni sul I<FILE> compresso. Non viene prodotto alcun output non compresso e non viene creato o rimosso alcun file. In modalità elenco, il programma non è in grado di leggere i dati compressi dallo standard input o da altre fonti che non permettano la ricerca."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "Il tracciato predefinito mostra le informazioni di base sui I<FILE>, un file per riga. Per ottenere informazioni più dettagliate, usare anche l'opzione B<--verbose>. Per avere ancora più informazioni, usare B<--verbose> due volte, ma si noti che questo può essere lento, perché ottenere tutte le informazioni extra richiede molte estrazioni. La larghezza dell'output dettagliato supera gli 80 caratteri, quindi il reindirizzamento dell'output, ad esempio, a B<less\\ -S> può essere utile se il terminale non è sufficientemente ampio."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "L'output esatto può variare tra le versioni di B<xz> e le diverse impostazioni locali. Per un output leggibile dalla macchina si dovrebbe utilizzare B<--robot --list>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Modificatori dell'operazione"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Non elimina i file input."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "A partire da B<xz> 5.2.6, questa opzione fa sì che B<xz> comprima o decomprima anche se l'input è un collegamento simbolico a un file regolare, ha più di un collegamento fisico o ha il bit setuid, setgid o sticky impostato. I bit setuid, setgid e sticky non vengono copiati nel file di destinazione. Nelle versioni precedenti questo veniva fatto solo con B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Questa opzione ha diverse conseguenze:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Se il file di destinazione esiste già, lo elimina prima di comprimere o decomprimere."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Comprime o decomprime anche se l'input è un collegamento simbolico a un file regolare, ha più di un collegamento \"hard\", o ha il bit setuid, setgid o sticky impostato. I bit setuid, setgid e sticky non sono copiati sul file destinazione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Quando usato con B<--decompress> B<--stdout> e B<xz> non riesce a riconoscere il tipo di file sorgente, copia il file sorgente così com'è sullo standard output. Questo permette a B<xzcat> B<--force> di essere usato come B<cat>(1) per file che non siano stati compressi con B<xz>. Si noti che in futuro B<xz> potrebbe supportare nuovi formati di file compressi, il che potrebbe portare B<xz> a decomprimere più tipi di file anziché copiarli come sono sullo standard output. B<--format=>I<FORMATO> può essere usato per forzare B<xz> a decomprimere solo un singolo formato di file."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Scrive i dati compressi o decompressi nello standard output anziché in un file. Implica B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Decomprime solo il primo flusso B<.xz>, e ignora automaticamente i possibili dati di input rimanenti che seguono il flusso. Normalmente questi dati sporchi finali portano B<xz> a visualizzare un errore."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> non decomprime mai più di un flusso dai file B<.lzma> o dai flussi grezzi, ma questa opzione fa comunque in modo che B<xz> ignori i possibili dati finali dopo il file B<.lzma> o il flusso grezzo."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Questa opzione non ha effetto se la modalità operativa non è B<--decompress> o B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr "Dalla versione B<xz> 5.7.1alpha, B<--single-stream> implica B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Disabilita la creazione di file sparsi. Per impostazione predefinita, se si esegue la decompressione in un file regolare, B<xz> tenta di rendere il file sparso se i dati decompressi contengono lunghe sequenze di zeri binari. Funziona anche quando si scrive su standard output, purché lo standard output sia collegato a un file regolare e siano soddisfatte alcune condizioni aggiuntive per renderlo sicuro. La creazione di file sparsi può risparmiare spazio su disco e velocizzare la decompressione riducendo la quantità di I/O su disco."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.SUFFISSO>, B<--suffix=>I<.SUFFISSO>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "Durante la compressione, utilizzare I<.suf> come suffisso per il file di destinazione anziché B<.xz> o B<.lzma>. Se non si scrive nello standard output e il file di origine ha già il suffisso I<.suf>, viene visualizzato un avvertimento e il file viene ignorato."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "Quando si decomprime, accetta i file con suffisso I<.suf> in aggiunta a quelli con suffisso B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, o B<.lz>. Se il file sorgente ha suffisso I<.suf>, il suffisso viene rimosso per ottenere il nome del file destinazione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "Quando si comprimono o decomprimono flussi grezzi (B<--format=raw>), il suffisso deve sempre essere specificato, a meno che si scriva sullo standard output, perché non esiste un suffisso predefinito per i flussi grezzi."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<FILE>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Legge i nomi dei file da processare da I<FILE>; se I<FILE> è omesso, i nomi dei file vengono letti dallo standard input. I nomi dei file devono essere terminati da un carattere \"a capo\". Un trattino (B<->) è considerato come un nome di file regolare; non indica lo standard input. Se vengono forniti anche dei nomi di file come argomenti della riga di comando, questi sono processati prima di quelli letti da I<FILE>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<FILE>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Questo è identico a B<--files>[B<=>I<FILE>] tranne per il fatto che ogni nome di file deve terminare con il carattere null."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Formato file di base e opzioni di compressione"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<FORMATO>, B<--format=>I<FORMATO>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Specifica il I<FORMATO> del file da comprimere o decomprimere:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Questa è l'impostazione predefinita. Quando si comprime, B<auto> è equivalente a B<xz>. Quando si decomprime, il formato del file input viene rilevato automaticamente. Si noti che per i flussi grezzi (creati con B<--format=raw>) non è possibile il rilevamento automatico."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Comprime nel formato di file B<.xz>, oppure accetta solo file B<.xz> durante la decompressione."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Comprime nel formato tradizionale B<.lzma>, oppure accetta solo file B<.lzma> per la decompressione. Il nome alternativo B<alone> è fornito per retrocompatibilità con le LZMA Utils."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Accetta solo file B<.lz> per la decompressione. La compressione non è supportata."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "Sono supportati il formato B<.lz> versione 0 e la versione 1 non estesa. I file della versione 0 sono stati prodotti da B<lzip> 1.3 e precedenti. Tali file non sono comuni, ma possono essere trovati negli archivi di file poiché alcuni pacchetti sorgente sono stati rilasciati in questo formato. Anche alcune persone potrebbero avere vecchi file personali in questo formato. Il supporto alla decompressione per il formato versione 0 è stato rimosso in B<lzip> 1.18."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip> 1.4 e successivi creano file nel formato v1. L'estensione del marcatore di scaricamento sincronizzazione al formato v1 è stata aggiunta in B<lzip> 1.6. Questa estensione è utilizzata raramente e non è supportata da B<xz>(viene segnalata come input corrotto)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Comprime o decomprime un flusso grezzo (senza intestazione). Questo è inteso solamente per utenti avanzati. Per decodificare flussi grezzi, occorre usare B<--format=raw> e specificare esplicitamente la catena dei filtri, che normalmente sarebbero conservati nell'intestazione."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<CONTROLLO>, B<--check=>I<CONTROLLO>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Specifica il tipo di controllo di integrità. Il controllo viene calcolato dai dati non compressi e memorizzato nel file B<.xz>. Questa opzione ha effetto solo quando si comprime nel formato B<.xz>; il formato B<.lzma> non supporta i controlli di integrità. Il controllo di integrità (se presente) viene verificato quando il file B<.xz> viene decompresso."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Tipi di I<CONTROLLI> supportati:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Non calcola proprio il controllo di integrità. Questo in genere è una pessima idea. Può essere utile quando l'integrità dei dati viene comunque verificata con altri mezzi."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Calcola la firma CRC32 usando il polinomio di IEEE-802.3 (Ethernet)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Calcola la firma CRC64 usando il polinomio di ECMA-182. Questa è l'impostazione predefinita, perché è leggermente migliore della CRC32 nel rilevare i file danneggiati e la differenza di velocità è trascurabile."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Calcola la firma SHA-256. Questo è un po' più lento che CRC32 e CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "L'integrità delle intestazioni B<.xz> viene sempre verificata con CRC32. Non è possibile modificarlo o disabilitarlo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Non verifica il controllo di integrità dei dati compressi durante la decompressione. Il valore CRC3 nelle intestazioni B<.xz> sarà ancora verificata normalmente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Non usare questa opzione se non si è consci di cosa si sta facendo.> Possibili ragioni per utilizzare questa opzione:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Tentativo di recupero dei dati da un file .xz corrotto."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Accelerazione della decompressione. Questo è importante soprattutto con SHA-256 o con i file che sono stati compressi molto bene. Si consiglia di non utilizzare questa opzione per questo scopo, a meno che l'integrità del file non venga verificata esternamente in altro modo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Selezionare un livello di compressione preimpostato. Il valore predefinito è B<-6>. Se vengono specificati più livelli preimpostati, l'ultimo ha effetto. Se è già stata specificata una catena di filtri personalizzata, l'impostazione di un livelli preimpostati cancella la catena di filtri personalizzata."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Le differenze tra i livelli preimpostati sono più significative rispetto a B<gzip>(1)  e B<bzip2>(1). Le impostazioni di compressione selezionate determinano i requisiti di memoria del decompressore, quindi l'utilizzo di un livello preimpostato troppo alto potrebbe rendere difficile la decompressione del file su un vecchio sistema con poca RAM. Specificamente, B<non è una buona idea utilizzare ciecamente -9 per tutto> come spesso accade per B<gzip>(1)  e B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Questi sono livelli preimpostati piuttosto veloci. B<-0-> a volte è più veloce di B<gzip -9> e comprime molto meglio. Quelli più alti hanno spesso una velocità paragonabile a B<bzip2>(1) con un rapporto di compressione comparabile o migliore, anche se i risultati dipendono molto dal tipo di dati che vengono compressi."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Compressione da buona a molto buona, mantenendo l'utilizzo della memoria del decompressore ragionevole anche per vecchi sistemi. B<-6> è il valore predefinito, che di solito è una buona scelta per distribuire file che debbano essere decompressi anche su sistemi con solo 16\\ MiB di RAM. (Potrebbe valere la pena di considerare anche B<-5e> o B<-6e>.  Si veda B<--extreme>.)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Questi sono come B<-6> ma con requisiti di memoria di compressore e decompressore più elevati. Sono utili solo quando si comprimono file di dimensioni superiori a 8\\ MiB, 16\\ MiB e 32\\ MiB, rispettivamente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "A parità di hardware, la velocità di decompressione è approssimativamente un numero costante di byte di dati compressi al secondo. In altre parole, migliore è la compressione, più veloce sarà di solito la decompressione. Ciò significa anche che la quantità di output non compresso prodotta al secondo può variare notevolmente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "La tabella seguente riassume le caratteristiche dei livelli preimpostati:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Livello preimpostato"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "DictSize"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "CompCPU"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "CompMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "DecMem"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Descrizioni delle colonne:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "DictSize è la dimensione del dizionario LZMA2. È uno spreco di memoria usare un dizionario più grande della dimensione del file non compresso. Ecco perché è una buona cosa evitare di usare i livelli preimpostati B<-7> ... B<-9> quando non c'è reale necessità. Con B<-6> e inferiori, la quantità di memoria sprecata in genere è sufficientemente bassa da essere trascurabile."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "CompCPU è una rappresentazione semplificata delle impostazioni di LZMA2 che influenzano la velocità di compressione. Anche la dimensione del dizionario influenza la velocità, quindi mentre CompCPU è lo stesso per i livelli B<-6> ... B<-9>, i livelli più alti tendono ancora ad essere un po' più lenti. Per avere una compressione ancora più lenta e quindi potenzialmente migliore, utilizzare B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr "CompMem contiene i requisiti di memoria del compressore in modalità a singola thread. Può variare leggermente tra le versioni di B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "DecMem contiene i requisiti di memoria del decompressore. In altre parole, le impostazioni di compressione determinano i requisiti di memoria del decompressore. L'utilizzo esatto della memoria del decompressore è leggermente superiore alla dimensione del dizionario LZMA2, ma i valori nella tabella sono stati arrotondati per eccesso al successivo MiB completo."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr "I requisiti di memoria per la modalità a thread multiple sono significativamente più alti che per la modalità thread singola. Con il valore predefinito di B<--block-size>, ogni thread richiede 3*3*dimensione del dizionario più CompMem oppure DecMem. Ad esempio, 4 thread con il livello preimpostato B<-6> hanno bisogno di 660\\(en670\\ MiB di memoria."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Utilizzare una variante più lenta del livello preimpostato selezionato (B<-0> ... B<-9>) nella speranza di ottenere un rapporto di compressione leggermente migliore, ma con un po' di sfortuna questo potrebbe anche renderlo peggiore. L'utilizzo della memoria del decompressore non è influenzato, ma l'utilizzo della memoria del compressore aumenta leggermente ai livelli preimpostati B<-0> ... B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Dal momento che ci sono due livelli preimpostati con dimensione del dizionario di 4\\ MiB e 8\\ MiB, i livelli preimpostati B<-3e> e B<-5e> usano impostazioni leggermente più veloci (minore CompCPU) di B<-4e> e B<-6e>, rispettivamente. In questo modo non ci sono due livelli preimpostati identici."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "Ad esempio, ci sono un totale di quattro livelli preimpostati che utilizzano un dizionario da 8\\ MiB, il cui ordine dal più veloce al più lento è B<-5>, B<-6>, B<-5e>, e B<-6e>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Questi sono alias in qualche modo fuorvianti per B<-0> e B<-9>, rispettivamente. Sono forniti solo per retrocompatibilità con le LZMA Utils. Evitare di utilizzare queste opzioni."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<DIMENSIONE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "Quando si comprime in formato B<.xz>, divide i dati input in blocchi da I<DIMENSIONE> byte. I blocchi vengo compressi indipendentemente l'uno dall'altro, cosa che aiuta con le thread multiple e rende possibile la decompressione con accessi casuali limitati. Questa opzione viene usata tipicamente per sovrascrivere la dimensione predefinita dei blocchi in modalità a thread multiple, ma può essere utilizzata anche in modalità thread singola."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr "In modalità a thread multiple verranno allocati circa il triplo di I<DIMENSIONE> di byte in ogni thread per il buffering dell'input e dell'output. La I<DIMENSIONE> predefinita è tre volte la dimensione del dizionario LZMA2 e comunque almeno 1 MiB. Tipicamente un buon valore è 2\\(en4 volte la dimensione del dizionario LZMA2 oppure almeno 1 MiB. Usare una I<DIMENSIONE> inferiore della dimensione del dizionario LZMA2 causa uno spreco di RAM, in quanto il buffer del dizionario LZMA2 non verrà mai riempito completamente. In modalità a thread multiple, la dimensione dei blocchi è conservata nelle intestazioni dei blocchi. L'informazione sulla dimensione è richiesta per la decompressione a thread multiple."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr "Nella modalità a thread singola, per impostazione predefinita non viene eseguita alcuna suddivisione in blocchi. L'impostazione di questa opzione non influisce sull'utilizzo della memoria. Nessuna informazione sulle dimensioni viene memorizzata nelle intestazioni di blocco, quindi i file creati in modalità a thread singola non saranno identici ai file creati in modalità a thread multiple. La mancanza di informazioni sulle dimensioni significa anche che B<xz> non sarà in grado di decomprimere i file in modalità a thread multiple."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-list=>I<items>"
msgstr "B<--block-list=>I<ELEMENTI>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr "Quando si comprime nel formato B<.xz>, inizia un nuovo blocco con una catena di filtri personalizzata dopo gli intervalli indicati di dati non compressi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr "Gli I<ELEMENTI> sono un elenco separato da virgole. Ogni elemento è costituito da un numero di catena di filtri opzionale compreso tra 0 e 9 seguito da due punti (B<:>) e da una dimensione richiesta di dati non compressi. L'omissione di un elemento (due o più virgole consecutive) è una scorciatoia per utilizzare le dimensioni e i filtri dell'elemento precedente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr "Se il file di input è più grande della somma delle dimensioni in I<ELEMENTI>, l'ultimo elemento viene ripetuto fino alla fine del file. Il valore speciale B<0> può essere utilizzato come ultima dimensione per indicare che il resto del file deve essere codificato come un singolo blocco."

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr "È possibile specificare una catena di filtri alternativa per ogni blocco in combinazione con le opzioni B<--filters1=>I<FILTRI> \\&...\\& B<--filters9=>I<FILTRI>. Queste opzioni definiscono catene di filtri con un identificatore compreso tra 1\\(en9. La catena di filtri 0 può essere utilizzata per indicare la catena di filtri predefinita, che equivale a non specificare una catena di filtri. L'identificatore della catena di filtri può essere utilizzato prima della dimensione non compressa, seguita da due punti (B<:>).  Ad esempio, se si specifica B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> allora i blocchi verranno creati utilizzando:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr "La catena dei filtri specificata da B<--filters1> e un input di 2 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr "La catena dei filtri specificata da B<--filters3> e un input di 2 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr "La catena dei filtri specificata da B<--filters2> e un input di 4 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr "La catena di filtri predefinita e l'input di 2 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr "La catena di filtri predefinita e l'input di 4 MiB per ogni blocco fino alla fine dell'input."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr "Se si specifica una dimensione che supera la dimensione del blocco del codificatore (il valore predefinito in modalità thread oppure il valore specificato con B<--block-size=>I<DIMENSIONE>), il codificatore creerà blocchi aggiuntivi mantenendo i limiti specificati in I<ELEMENTI>. Ad esempio, se si indica B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> e il file input è di 80 MiB, si otterranno 11 blocchi: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "In modalità a thread multiple, le dimensioni dei blocchi vengono memorizzate nelle intestazioni dei blocchi. Questo non avviene in modalità thread singola, quindi l'output codificato non sarà identico a quello della modalità a thread multiple."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<TIMEOUT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "Quando si comprime, se sono passati più di I<TIMEOUT> millisecondi (un intero positivo) dallo scaricamento precedente e la lettura di ulteriori input si bloccherebbe, tutti i dati di input in sospeso vengono scaricati dal codificatore e resi disponibili nel flusso di output. Questo può essere utile se B<xz> viene usato per comprimere dati in streaming dalla rete. Piccoli valori del I<TIMEOUT> rendono disponibili i dati al ricevente con un ritardo minimo, mentre valori di I<TIMEOUT> grandi danno un miglior rapporto di compressione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Questa funzionalità è disabilitata per impostazione predefinita. Se questa opzione viene specificata più di una volta, l'ultima ha effetto. Il valore speciale I<TIMEOUT> di B<0> può essere utilizzato per disabilitare esplicitamente questa funzionalità."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Questa funzionalità non è disponibile sui sistemi non-POSIX."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Questa funzionalità è sperimentale.> Attualmente B<xz> non è adatto per decomprimere il flusso in tempo reale, a causa di come effettua il buffering."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sync>"
msgstr "B<--no-sync>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr "Non sincronizzare il file target e la sua directory sull'unità di archiviazione prima di rimuovere il file sorgente. Questo può migliorare le performance se si sta comprimendo o decomprimendo tanti piccoli file. Tuttavia, se il sistema dovesse andare in crash subito dopo l'eliminazione, è possibile che il file destinazione non sia ancora stato scritto sull'unità di archiviazione mentre l'operazione di eliminazione sì. In questo caso non saranno più disponibili né il file sorgente originale né il file di destinazione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr "Questa opzione ha effetto solamente quando B<xz> deve rimuovere il file sorgente. Negli altri casi la sincronizzazione non viene eseguita."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr "La sincronizzazione e B<--no-sync> sono stati aggiunti nella versione B<xz> 5.7.1alpha."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<LIMITE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Imposta un limite di utilizzo della memoria per la compressione. Se questa opzione viene specificata più volte, ha effetto l'ultima."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Se le impostazioni di compressione superano il valore I<LIMITE>, B<xz> tenterà di regolare le impostazioni verso il basso in modo che il limite non venga più superato e visualizzerà un avviso che indica che è stata eseguita una regolazione automatica. Le regolazioni vengono eseguite in questo ordine: riduzione del numero di thread, passaggio alla modalità a thread singola se anche un solo thread in modalità a thread multiple supera il I<LIMITE>, e infine riducendo la dimensione del dizionario LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "Quando si comprime con B<--format=raw> oppure se si è specificato B<--no-adjust>, è possibile ridurre solo il numero di thread, poiché questo può essere fatto senza influire sull'output compresso."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Se non è possibile soddisfare il I<LIMITE> anche con le regolazioni sopra descritte, viene visualizzato un errore e B<xz> uscirà con lo stato di uscita 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "Il I<LIMITE> può essere specificato in diversi modi:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "Il I<LIMITE> può essere un valore assoluto in byte. Usare un suffisso intero come B<MiB> può essere utile. Ad esempio: B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "Il I<LIMITE> può essere specificato come percentuale della memoria fisica (RAM) totale. Questo può essere utile specialmente quando si imposta la variabile di ambiente B<XZ_DEFAULTS> in uno script di inizializzazione di shell che è condiviso tra computer diversi. In questo modo il limite è automaticamente più grande sui sistemi con più memoria. Ad esempio: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "Il I<LIMITE> può essere reimpostato al suo valore predefinito impostandolo a B<0>.Questo attualmente equivale a impostare il I<LIMITE> a B<max> (nessun limite nell'utilizzo di memoria)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "Per B<xz> a 32-bit esiste un caso particolare: se il I<LIMITE> fosse oltre B<4020\\ MiB>, il I<LIMITE> viene impostato a B<4020\\ MiB>. Su MIPS32 invece viene usato B<2000\\ MiB>. (I valori B<0> e B<max> non sono influenzati da questo. Un comportamento simile non esiste per la decompressione). Questo può essere utile quando un eseguibile a 32 bit ha accesso a uno spazio di indirizzi da 4\\ GiB (2 GiB su MIPS32), e si spera che non faccia danni in altre situazioni."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Vedere anche la sezione B<Utilizzo memoria>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<LIMITE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Imposta un limite di utilizzo della memoria per la decompressione. Questo influisce anche sulla modalità B<--list>. Se l'operazione non è possibile senza oltrepassare il I<LIMITE>, B<xz> mostrerà un errore e la decompressione del file fallirà. Si veda B<--memlimit-compress=>I<LIMITE> per possibili modi per specificare il I<LIMITE>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<LIMITE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "Imposta un limite di utilizzo della memoria per la decompressione multi-thread. Questo può influire solo sul numero di thread; non indurrà mai B<xz> a rifiutarsi di decomprimere un file. Se I<LIMITE> è troppo basso per consentire le thread multiple, il I<LIMITE> viene ignorato e B<xz> continuerà in modalità a thread singola. Si noti che se viene utilizzato anche B<--memlimit-decompress>, si applicherà sempre sia alla modalità a thread singola che a quella a thread multiple, e quindi il I<LIMITE> effettivo per le thread multiple non sarà mai superiore a quello impostato con B<--memlimit-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "In contrasto con le altre opzioni di limite di utilizzo della memoria, B<--memlimit-mt-decompress=>I<LIMITE> ha un I<LIMITE> predefinito specifico per il sistema. B<xz --info-memory> può essere utilizzato per vedere il valore corrente."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Questa opzione e il suo valore predefinito esistono perché, senza un limite, il decompressore a thread multiple potrebbe finire per allocare una quantità folle di memoria per alcuni file input. Se il valore predefinito I<LIMITE> è troppo basso sul proprio sistema, è possibile aumentarlo liberamente, ma non deve essere mai impostato a un valore più grande della quantità di RAM utilizzabile, in quanto con file di input appropriati B<xz> cercherà di utilizzare quella quantità di memoria anche con un basso numero di thread. Esaurire la memoria o fare swap non migliorerà le performance della decompressione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Vedi B<--memlimit-compress=>I<LIMITE> per possibili modi per specificare il I<LIMITE>. Impostando I<LIMITE> su B<0> si ripristina il valore predefinito I<LIMITE> specifico del sistema."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<LIMITE>, B<--memlimit=>I<LIMITE>, B<--memory=>I<LIMITE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Questo equivale a specificare B<--memlimit-compress=>I<LIMITE> B<--memlimit-decompress=>I<LIMITE> B<--memlimit-mt-decompress=>I<LIMITE>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "Visualizza un errore e esce se non è possibile soddisfare il limite di utilizzo della memoria senza regolare le impostazioni che influiscono sull'output compresso. In altre parole, ciò impedisce a B<xz> di passare il codificatore dalla modalità multi-thread alla modalità a thread singola e di ridurre le dimensioni del dizionario LZMA2. Anche quando viene usata questa opzione, il numero di thread può essere ridotto per soddisfare il limite di utilizzo della memoria, in quanto ciò non influirà sull'output compresso."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "La regolazione automatica è sempre disabilitata quando si creano flussi raw (B<--format=raw>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<THREADS>, B<--threads=>I<THREADS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "Specifica il numero di thread di lavoro da utilizzare. L'impostazione di I<THREADS> al valore speciale B<0> fa sì che B<xz> utilizzi fino a un numero di thread che il processore/i del sistema supportano. Il numero effettivo di thread può essere inferiore a I<THREADS> se il file di input non è sufficientemente grande per il threading con le impostazioni specificate o se l'utilizzo di più thread supera il limite di utilizzo della memoria."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "I compressori a thread singola e a thread multiple producono output diversi. Il compressore a thread singola produrrà la dimensione del file più piccola, ma solo l'output del compressore a thread multiple può essere decompresso utilizzando più thread. Impostando I<THREADS> su B<1> verrà utilizzata la modalità a thread singola. L'impostazione di I<THREADS> su qualsiasi altro valore, incluso B<0>, utilizzerà il compressore a thread multiple anche se il sistema supporta un solo thread hardware. (B<xz> 5.2.x utilizzava la modalità a thread singola in questa situazione.)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Per utilizzare la modalità a thread multiple con un solo thread, impostare I<THREADS> su B<+1>. Il prefisso B<+> non ha alcun effetto con valori diversi da B<1>. Un limite di utilizzo della memoria può comunque far passare B<xz> alla modalità a thread singola a meno che non venga utilizzato B<--no-adjust>. Il supporto per il prefisso B<+> è stato aggiunto in B<xz> 5.4.0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr "Se è stato richiesto un numero automatico di thread e non è stato specificato alcun limite di utilizzo della memoria, verrà utilizzato un limite \"soft\" predefinito, specifico del sistema, per limitare eventualmente il numero di thread. È un limite \"soft\" nel senso che viene ignorato se il numero di thread diventa uno, quindi un limite soft non impedirà mai a B<xz> di comprimere o decomprimere. Questo limite soft predefinito non farà passare B<xz> dalla modalità a thread multiple alla modalità thread singola. I limiti attivi possono essere visualizzati con B<xz --info-memory>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "Attualmente l'unico metodo di threading consiste nel dividere l'input in blocchi e comprimerli indipendentemente l'uno dall'altro. La dimensione predefinita del blocco dipende dal livello di compressione e può essere sovrascritta con l'opzione B<--block-size=>I<DIMENSIONE>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "La decompressione a thread multiple funziona solo su file che contengano più blocchi con l'informazione della dimensione nelle intestazioni del blocco. Tutti i file sufficientemente grandi compressi in modalità a thread multiple soddisfano questa condizione, mentre i file compressi in modalità thread singola no, neanche se si è utilizzato B<--block-size=> I<DIMENSIONE>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr "Il valore predefinito per I<THREADS> è B<0>. In B<xz> 5.4.x e precedenti il valore predefinito era B<1>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Catene di filtri di compressione personalizzate"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Una catena di filtri personalizzata consente di specificare in dettaglio le impostazioni di compressione invece di fare affidamento sulle impostazioni associate ai livelli preimpostati. Quando viene specificata una catena di filtri personalizzata, le opzioni relative ai livelli preimpostati (B<-0> \\&...\\& B<-9> e B<--extreme>) specificate in precedenza sulla riga di comando vengono dimenticate. Se un'opzione livello preimpostato viene specificata dopo una o più opzioni della catena di filtri personalizzata, il nuovo livello preimpostato ha effetto e le opzioni della catena di filtri personalizzate specificate in precedenza vengono dimenticate."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Una catena di filtri è paragonabile a una pipe sulla riga di comando. Durante la compressione, l'input non compresso va al primo filtro, il cui output va al filtro successivo (se presente). L'output dell'ultimo filtro viene scritto nel file compresso. Il numero massimo di filtri nella catena è quattro, ma in genere una catena di filtri ha solo uno o due filtri."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Molti filtri hanno limitazioni su dove possono trovarsi nella catena di filtri: alcuni filtri possono funzionare solo come ultimo filtro della catena, altri solo come filtro non ultimo e alcuni funzionano in qualsiasi posizione nella catena. A seconda del filtro, questa limitazione è inerente alla struttura del filtro oppure esiste per evitare problemi di sicurezza."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr "Una catena di filtri personalizzata può essere specificata in due modi diversi. Le opzioni B<--filters=>I<FILTRI> and B<--filters1=>I<FILTRI> \\&...\\& B<--filters9=>I<FILTRI> permettono di specificare un'intera catena di filtri in una opzione utilizzando la sintassi della stringa del filtro di lzma. In alternativa, una catena di filtri può essere specificata utilizzando una o più singole opzioni di filtro nell'ordine desiderato nella catena di filtri. Questo significa che l'ordine delle singole opzioni di filtro è importante! Quando si decodificano i flussi grezzi (B<--format=raw>), la catena di filtri deve essere specificata nello stesso ordine in cui è stata specificata durante la compressione. Qualsiasi filtro individuale o opzione livello preimpostato specificata prima dell'opzione della catena completa (B<--filters=>I<FILTRI>) verrà dimenticata. I singoli filtri specificati dopo l'opzione della catena completa reimposteranno la catena di filtri."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr "Sia l'opzione di filtro completo che quella individuale accettano I<OPZIONI> specifiche del filtro come un elenco separato da virgole. Virgole in eccesso nelle I<OPZIONI> vengono ignorate. Ogni opzione ha un valore di default, quindi occorre specificare solamente quelle che si desidera modificare."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Per vedere l'intera catena di filtri e I<OPZIONI>, usa B<xz -vv> (ossia, usa B<--verbose> due volte). Questo funziona anche per visualizzare le opzioni della catena di filtri utilizzate dai livelli preimpostati."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters=>I<filters>"
msgstr "B<--filters=>I<FILTRI>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr "Specifica l'intera catena dei filtri oppure un livello preimpostato in una singola opzione. Ogni filtro può essere separato da spazi o da due trattini (B<-->). Potrebbe essere necessario mettere tra virgolette i I<FILTRI> sulla riga di comando della shell in modo che vengano analizzati come una singola opzione. Per indicare I<OPZIONI>, usa B<:> o B<=>. Un livello preimpostato può essere preceduto da un B<-> e seguito da zero o più flag. L'unico flag supportato è B<e> per applicare le stesse opzioni di B<--extreme>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr "B<--filters1>=I<FILTRI> ... B<--filters9>=I<FILTRI>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr "Specifica fino a nove catene di filtri aggiuntive che possono essere utilizzate con B<--block-list>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr "Ad esempio, quando si comprime un archivio con file eseguibili seguiti da file di testo, la parte eseguibile potrebbe utilizzare una catena di filtri con un filtro BCJ e la parte di testo solo il filtro LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters-help>"
msgstr "B<--filters-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr "Mostra un messaggio di aiuto che descrive come specificare livelli preimpostati e catene di filtri personalizzati nelle opzioni B<--filters> e B<--filters1=>I<FILTRI> \\&...\\& B<--filters9=>I<FILTRI>, e termina con successo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<OPZIONI>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Aggiunge un filtro LZMA1 o LZMA2 alla catena dei filtri. Questi filtri possono essere usati solo come ultimo filtro della catena."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 è un filtro obsoleto, supportato quasi esclusivamente a causa del formato obsoleto di file B<.lzma>, che supporta solo LZMA1. LZMA2 è una versione aggiornata di LZMA1 che risolve alcuni problemi pratici di LZMA1. Il formato B<.xz> utilizza LZMA2 e non supporta LZMA1. La velocità e i rapporti di compressione di LZMA1 e LZMA2 sono praticamente gli stessi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 e LZMA2 condividono lo stesso insieme di I<OPZIONI>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<PRESET>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "Reimposta tutte le I<OPZIONI> LZMA1 o LZMA2 a I<PRESET>. Il I<PRESET> (livello preimpostato) consiste di un numero intero, che può essere seguito da modificatori costituiti da una singola lettera. Il numero intero può andare da B<0> a B<9>, corrispondenti alle opzioni della riga di comando B<-0> \\&...\\& B<-9>. L'unico modificatore attualmente supportato è B<e>, che corrisponde a B<--extreme>. Se non viene specificato alcun I<PRESET>, i valori predefiniti delle I<OPZIONI> LZMA1 o LZMA2 sono presi dal livello preimpostato B<6>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<DIMENSIONE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "La I<DIMENSIONE>del dizionario (buffer di cronologia) indica quanti byte dei dati non compressi elaborati di recente vengono mantenuti in memoria. L'algoritmo tenta di trovare sequenze di byte ripetute (corrispondenze) nei dati non compressi e di sostituirle con riferimenti ai dati attualmente presenti nel dizionario. Più grande è il dizionario, maggiore è la possibilità di trovare una corrispondenza. Quindi, l'aumento della I<DIMENSIONE> del dizionario di solito migliora il rapporto di compressione, ma un dizionario più grande del file non compresso è uno spreco di memoria."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "Una I<DIMENSIONE> tipica per un dizionario è da 64\\ KiB a 64\\ MiB. Il minimo è 4\\ KiB.  Il massimo per la compressione è attualmente 1.5\\ GiB (1536\\ MiB).  La decompressione supporta già dizionari fino a 4\\ GiB meno 1 byte, che è il massimo per i formati di flusso LZMA1 e LZMA2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "La I<DIMENSIONE> del dizionario e il cercatore di corrispondenze (I<CERCATORE>) insieme determinano l'utilizzo della memoria del codificatore LZMA1 o LZMA2. Per la decompressione è necessaria la stessa I<DIMENSIONE> del dizionario utilizzata durante la compressione (o più grande), quindi l'utilizzo della memoria del decodificatore è determinato dalla dimensione del dizionario utilizzato durante la compressione. Le intestazioni B<.xz> memorizzano la I<DIMENSIONE> del dizionario come 2^I<n> o 2^I<n> + 2^( I<n>-1), quindi queste I<DIMENSIONI> sono in qualche modo preferite per la compressione. Altre I<DIMENSIONI> verranno arrotondate per eccesso quando memorizzate nelle intestazioni B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Specificare il numero di bit di contesto letterali. Il minimo è 0 e il massimo è 4; Il valore predefinito è 3. Inoltre, la somma di I<lc> e I<lp> non deve superare 4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Tutti i byte che non possono essere codificati come corrispondenze vengono codificati come valori letterali. In altre parole, i valori letterali sono semplicemente byte a 8 bit codificati uno alla volta."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "La codifica letterale presuppone che i bit di I<lc> più alti del byte non compresso precedente siano correlati al byte successivo. Ad esempio, in un tipico testo inglese, una lettera maiuscola è spesso seguita da una lettera minuscola e una lettera minuscola è solitamente seguita da un'altra lettera minuscola. Nel set di caratteri US-ASCII, i tre bit più alti sono 010 per le lettere maiuscole e 011 per le lettere minuscole. Quando I<lc> è almeno 3, la codifica letterale può sfruttare questa proprietà nei dati non compressi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "Il valore predefinito (3) solitamente è buono. Se si desidera la compressione massima, provare con B<lc=4>.  A volte aiuta un po', e a volte rende la compressione peggiore. Se la rende peggiore, provare anche B<lc=2>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Specificare il numero di bit di posizione letterale. Il minimo è 0 e il massimo è 4; Il valore predefinito è 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "Il parametro I<lp> influisce sul tipo di allineamento nei dati non compressi presunto durante la codifica dei valori letterali. Vedi I<pb> di seguito per ulteriori informazioni sull'allineamento."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Specificare il numero di bit di posizione. Il minimo è 0 e il massimo è 4; Il valore predefinito è 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "Il parametro I<pb> influisce su quale tipo di allineamento nei dati non compressi sia assunto in generale. L'impostazione predefinita indica un allineamento a quattro byte (2^I<pb> =2^2=4), che è spesso una buona scelta se non c'è un'ipotesi migliore."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Quando l'allineamento è noto, impostare I<pb> concordemente può ridurre un po' le dimensioni del file. Ad esempio, con file di testo con 1 byte di allineamento (US-ASCII, ISO-8859-*, UTF-8), impostare B<pb=0> può migliorare leggermente la compressione. Per il testo UTF-16, B<pb=1> è una buona scelta. Se l'allineamento è un numero dispari, come 3 byte, B<pb=0> potrebbe essere la scelta migliore."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Anche se l'allineamento assunto può essere regolato con I<pb> e I<lp>, LZMA1 e LZMA2 favoriscono ancora leggermente l'allineamento a 16 byte. Potrebbe valere la pena tenerlo in considerazione quando si progettano formati di file che possono essere spesso compressi con LZMA1 o LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<CERCATORE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "Il cercatore di corrispondenze ha un effetto importante sulla velocità del codificatore, sull'utilizzo della memoria e sul rapporto di compressione. Di solito i cercatori di corrispondenze a catena hash sono più veloci dei cercatori di corrispondenze ad albero binario. Il valore predefinito dipende da I<PRESET> : 0 usa B<hc3>, 1\\(en3 usa B<hc4> e il resto usa B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Sono supportati i seguenti cercatori di corrispondenze. Le formule di utilizzo della memoria riportate di seguito sono approssimazioni, che sono le più vicine alla realtà quando I<DIZIONARIO> è una potenza di due."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Catena hash con hashing da 2 e 3 byte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Minimo valore per I<NICE>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Utilizzo memoria:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<DIZIONARIO> * 7.5 (se I<DIZIONARIO> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<DIZIONARIO> * 5.5 + 64 MiB (se I<DIZIONARIO> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Catena hash con hash da 2, 3 e 4 byte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Minimo valore per I<NICE>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<DIZIONARIO> * 7.5 (se I<DIZIONARIO> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<DIZIONARIO> * 6.5 (se I<DIZIONARIO> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Albero binario con hashing da 2 byte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Valore minimo per I<NICE>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Utilizzo di memoria: I<DIZIONARIO> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Albero binario con hashing da 2 e 3 byte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<DIZIONARIO> * 11.5 (se I<DIZIONARIO> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<DIZIONARIO> * 9.5 + 64 MiB (se I<DIZIONARIO> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Albero binario con hashing da 2, 3 e 4 byte"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<DIZIONARIO> * 11.5 (se I<DIZIONARIO> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<DIZIONARIO> * 10.5 (se I<DIZIONARIO> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<MODALITÀ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "La I<MODALITÀ> di compressione specifica il metodo per analizzare i dati prodotti dal cercatore di corrispondenze. Le I<MODALITÀ> supportate sono B<fast> e B<normal>. Il predefinito è B<fast> per i I<PRESET> 0\\(en3 e B<normal> per i I<PRESET> 4\\(en9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Di solito B<fast> viene utilizzato con i cercatori di corrispondenze a catena hash e B<normal> con i cercatori di corrispondenze a albero binario. Questo è anche quello che fanno i I<PRESET>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<NICE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Specifica quella che si considera una lunghezza accettabile (\"nice\") per una corrispondenza. Una volta trovata una corrispondenza di almeno I<NICE> byte, l'algoritmo smette di cercare corrispondenze potenzialmente migliori."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "I<NICE> può valere 2\\(en273 byte. Valori più alti tendono a dare un miglior rapporto di compressione ai danni della velocità. Il valore predefinito dipende dal I<PRESET>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<PROFONDITÀ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Specificare la profondità di ricerca massima nel cercatore di corrispondenze. Il valore predefinito è il valore speciale 0, che dice al compressore di determinare una I<PROFONDITÀ> ragionevole da I<CERCATORE> e I<NICE>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "Una I<PROFONDITÀ> ragionevole per le catene hash è 4\\(en100 e 16\\(en1000 per gli alberi binari. Utilizzando valori di I<PROFONDITÀ> molto alti si può rendere il codificatore estremamente lento con alcuni file. Evitare di impostare la I<PROFONDITÀ> oltre 1000 a meno che si sia preparati a interrompere la compressione nel caso in cui richieda troppo tempo."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "Quando si decomprime un flusso grezzo (B<--format=raw>), LZMA2 ha bisogno solamente della I<DIMENSIONE> del dizionario. LZMA1 ha anche bisogno di I<lc>, I<lp>, e I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<OPZIONI>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<OPZIONI>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Aggiunge un filtro branch/call/jump (BCJ) alla catena dei filtri. Questi filtri non possono essere utilizzati come ultimo filtro nella catena di filtri."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "Un filtro BCJ converte gli indirizzi relativi in codice macchina nelle loro controparti assolute. Questo non cambia la dimensione dei dati ma aumenta la ridondanza, cosa che può aiutare LZMA2 a produrre B<.xz> file 0\\(en15\\ % più piccoli. I filtri BCJ sono sempre reversibili, quindi usare un filtro BCJ per il tipo di dati sbagliato non causa perdita di dati, al massimo può determinare un rapporto di compressione leggermente peggiore. I filtri BCJ sono molto veloci e usano una quantità di memoria minima."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Questi filtri BCJ presentano problemi noti relativi al rapporto di compressione:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "Alcuni tipi di file contenenti codice eseguibile (ad esempio, file oggetto, librerie statiche e moduli del kernel Linux) hanno gli indirizzi nelle istruzioni riempiti con valori di riempimento. Questi filtri BCJ eseguiranno comunque la conversione degli indirizzi, il che peggiorerà la compressione con questi file."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Se un filtro BCJ viene applicato a un archivio, è possibile che renda il rapporto di compressione peggiore rispetto a non usare un filtro BCJ. Ad esempio, se ci sono eseguibili simili o addirittura identici, il filtraggio probabilmente renderà i file meno simili e quindi la compressione sarà peggiore. Anche il contenuto dei file non eseguibili nello stesso archivio può essere importante. In pratica bisogna provare con e senza filtro BCJ per vedere quale sia il migliore in ogni situazione."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Insiemi diversi di istruzioni hanno diversi allineamenti: il file eseguibile deve essere allineato a un multiplo di questo valore nei dati di input per far funzionare il filtro."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Filtro"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Allineamento"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Note"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "32-bit o 64-bit x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr "L'allineamento migliore è a 4096 byte"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Solo big-endian"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr "RISC-V"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr "Dal momento che i dati filtrati da BCJ solitamente sono compressi con LZMA2, il rapporto di compressione può essere migliorato leggermente se le opzioni LZMA2 sono impostate in modo che corrispondano all'allineamento del filtro BCJ selezionato. Esempi:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr "Il filtro IA-64 ha un allineamento a 16 byte, quindi B<pb=4,lp=4,lc=0> è una buona scelta per LZMA2 (2^4=16)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr "Il codice RISC-V ha un allineamento a 2 o 4 byte a seconda che il file contenga o meno istruzioni 16 bit compresse (cosiddetta estensione C). Quando sono usate istruzioni a 16 bit, va bene B<pb=2,lp=1,lc=3> o B<pb=1,lp=1,lc=3>. Quando le istruzioni a 16 bit non sono presenti, è meglio B<pb=2,lp=2,lc=2>. Si può usare B<readelf -h> per controllare se \"RVC\" appare sulla riga del \"Flag\"."

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr "ARM64 è sempre allineato a 4 byte, quindi B<pb=2,lp=2,lc=2> è la scelta migliore."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr "Il filtro x86 è un'eccezione. Di solito è bene attenersi alle impostazioni predefinite di LZMA2 (B<pb=2,lp=0,lc=3>) quando si comprimono gli eseguibili x86."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Tutti i filtri BCJ supportano le stesse I<OPZIONI>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<OFFSET>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Specifica l'I<OFFSET> iniziale utilizzato per la conversione tra indirizzi relativi e assoluti. L'I<OFFSET> deve essere un multiplo dell'allineamento del filtro (vedere la tabella sopra). Il valore predefinito è zero. In pratica, l'impostazione predefinita è buona; specificare un I<OFFSET> personalizzato non è quasi mai utile."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<OPZIONI>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Aggiunge un filtro Delta alla catena dei filtri. I filtri Delta non possono essere utilizzati come ultimo filtro nella catena di filtri."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "Al momento è supportato solo un semplice calcolo delta byte-per-byte. Può essere utile quando si comprime, per esempio, immagini bitmap non compresse o file audio PCM non compressi. Tuttavia, speciali algoritmi ad-hoc potrebbero dare risultati significativamente migliori che Delta + LZMA2. Questo è vero specialmente per l'audio, che comprime più velocemente e meglio, ad esempio, con B<flac>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "I<OPZIONI> supportate:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<DISTANZA>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "Specifica la I<DISTANZA> del calcolo delta in byte. I<DISTANZA> deve essere nel range 1\\(en256. Il valore predefinito è 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "Per esempio, con B<dist=2> e un input di 8 byte A1 B1 A2 B3 A3 B5 A4 B7, l'output sarà A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Altre opzioni"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Sopprime avvertimenti e avvisi. Specificarlo due volte per eliminare anche gli errori. Questa opzione non ha alcun effetto sullo stato di uscita. In altre parole, anche se un avvertimento è stato eliminato, lo stato di uscita che indica un avvertimento viene comunque utilizzato."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Sii prolisso. Se lo standard error è collegato a un terminale, B<xz> visualizzerà un indicatore di avanzamento. Specificando B<--verbose> due volte si otterrà un output ancora più dettagliato."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "L'indicatore di avanzamento mostra le seguenti informazioni:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "La percentuale di avanzamento è mostrata se la dimensione del file input è nota. Quindi, la percentuale non può essere mostrata nei pipe."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Quantità di dati compressi prodotti (in compressione) o utilizzati (in decompressione)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Quantità di dati non compressi consumati (in compressione) o prodotti (in decompressione)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Rapporto di compressione, calcolato dividendo la quantità di dati compressi processati finora con la quantità di dati non compressi processati finora."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Velocità di compressione o decompressione. Questa è misurata come la quantità di dati non compressi consumati (in compressione) o prodotti (in decompressione) al secondo. Viene mostrata dopo che è trascorso qualche secondo da quando B<xz> ha iniziato a processare il file."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Tempo trascorso nel formato M:SS o H:MM:SS."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "Il tempo residuo stimato è mostrato solo quando la dimensione del file in ingresso è nota e sono già passati un paio di secondi da quando B<xz> ha iniziato a processare il file. Il tempo è mostrato in un formato meno preciso che non ha mai i due punti, ad esempio 2 min 30 s."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Quando lo standard error non è un terminale, B<--verbose> farà stampare a B<xz> il nome del file, la dimensione compressa, la dimensione non compressa, il rapporto di compressione, e potendo anche la velocità e il tempo trascorso, su una singola riga dello standard error, dopo aver compresso o decompresso il file. La velocità e il tempo trascorso sono inclusi solo se l'operazione è durata almeno un paio di secondi. Se l'operazione non è conclusa, ad esempio a causa dell'interruzione da parte dell'utente, viene stampata anche la percentuale di completamento, a patto che la dimensione del file input sia nota."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Non impostare lo stato di uscita a 2 anche se è stata rilevata una condizione che merita un avvertimento. Questa opzione non influisce sul livello di dettaglio, quindi sia B<--quiet> che B<--no-warn> devono essere utilizzati per non visualizzare avvertimenti e per non alterare lo stato di uscita."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Stampa i messaggi in un formato analizzabile dal computer. Questo ha lo scopo di facilitare la scrittura dei frontend che vogliono usare B<xz> invece di liblzma, che potrebbe essere il caso di vari script. Si intende che con questa opzione abilitata l'output dovrebbe rimanere stabile tra le versioni di B<xz>. Per ulteriori informazioni, vedere la sezione B<MODALITÀ ROBOT>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "Mostra, in un formato leggibile da umani, quanta memoria fisica (RAM) e quante thread B<xz> pensa che il sistema abbia e i limiti di utilizzo di memoria per la compressione e la decompressione, quindi termina con successo."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Mostra un messaggio di aiuto che descrive le opzioni usate più comunemente, e termina con successo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Mostra un messaggio di aiuto che descrive tutte le funzionalità di B<xz>, e termina con successo"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Mostra il numero di versione di B<xz> e liblzma in un formato leggibile dagli umani. Per ottenere un output analizzabile da una macchina specificare B<--robot> prima di B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "MODALITÀ ROBOT"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr "La \"modalità robot\" viene attivata con l'opzione B<--robot>. Rende l'output di B<xz> più facile da analizzare da altri programmi. Attualmente B<--robot> è supportato solo insieme a B<--list>, B<--filters-help>, B<--info-memory> e B<--version>. In futuro sarà supportato per la compressione e la decompressione."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Modalità stampa"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> usa un output separato da tabulazione. La prima colonna di ogni riga contiene una stringa che indica il tipo di informazione contenuta in quella riga:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Questa è sempre la prima riga quando si inizia a elencare un file. La seconda colonna della riga è il nome del file."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Questa riga contiene informazioni generali sul file B<.xz>. Questa riga viene sempre stampata dopo la riga B<name>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Questo tipo di riga viene utilizzato solo quando è stato specificato B<--verbose>. Sono presenti tante righe B<stream> quanti sono i flussi nel file B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Questo tipo di riga viene utilizzato solo quando è stato specificato B<--verbose>. Ci sono tante righe B<block> quanti sono i blocchi nel file B<.xz>. Le righe B<block> vengono visualizzate dopo tutte le righe B<stream>; i diversi tipi di riga non vengono interlacciati."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Questo tipo di riga viene utilizzato solo quando B<--verbose> è stato specificato due volte. Questa riga viene stampata dopo tutte le righe B<block>. Come la riga B<file>, la riga B<summary> contiene informazioni generali sul file B<.xz>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Questa riga è sempre l'ultima riga dell'output dell'elenco. Mostra i conteggi totali e le dimensioni."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Le colonne delle righe B<file>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Numero di flussi nel file"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Numero totale di blocchi nel flusso/i."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Dimensione compressa del file"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Dimensione non compressa del file"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Rapporto di compressione, es. B<0.123>. Se il rapporto è oltre 9999, al posto del rapporto vengono mostrati tre trattini (B<--->)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Elenco separato da virgole dei nomi dei controlli di integrità. Le stringhe seguenti vengono utilizzate per i tipi di controllo conosciuti: B<None>, B<CRC32>, B<CRC64>, e B<SHA-256>. Per i tipi di controllo non conosciuti viene utilizzato B<Unknown->I<N>, dove I<N> è l'ID del controllo come numero decimale (una o due cifre)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Dimensione totale del padding del flusso nel file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Le colonne delle righe B<stream>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Numero di flusso (il primo è 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Numero di blocchi nel flusso"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Offset iniziale compressione"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Offset iniziale decompressione"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Dimensione compressa (non include il padding del flusso)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Dimensione non compressa"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Rapporto di compressione"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Nome del controllo di integrità"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Dimensione del padding del flusso"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Le colonne delle righe B<block>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Numero di flussi che contengono questo blocco"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Numero del blocco relativo all'inizio del flusso (il primo blocco è 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Numero del blocco relativo all'inizio del file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Offset iniziale compressione relativo all'inizio del file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Offset iniziale decompressione relativo all'inizio del file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Dimensione totale compressa del blocco (incluse le intestazioni)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Se B<--verbose> viene specificato due volte, sono incluse colonne aggiuntive nelle righe B<block>. Queste non sono mostrate con un B<--verbose> singolo, perché recuperare queste informazioni richiede molte ricerche e quindi può essere lento:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Valore del controllo di integrità in formato esadecimale"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Dimensione intestazione blocco"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Flag del blocco: B<c>indica che è presente la dimensione compressa, B<u> indica che è presente la dimensione non compressa. Se il flag non è impostato, viene mostrato un trattino (B<->) per mantenere fissa la lunghezza della stringa. Nuovi flag potrebbero essere aggiunti alla fine della stringa in futuro."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Dimensione dei dati compressi effettivi nel blocco (sono esclusi l'intestazione del blocco, il padding del blocco e i campi di controllo)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Quantità di memoria (in byte) necessaria per decomprimere questo blocco con questa versione B<xz>"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Catena di filtri. Si noti che la maggior parte delle opzioni utilizzate al momento della compressione non è nota, perché solo le opzioni necessarie per la decompressione sono memorizzate nelle intestazioni B<.xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Le colonne delle righe B<summary>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Quantità di memoria (in byte) necessaria per decomprimere questo file con questa versione B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> o B<no> indica se tutte le intestazioni di blocco contengono all'interno sia dimensioni compresse che dimensioni non compresse"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<A partire da> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Versione minima di B<xz> richiesta per decomprimere il file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Le colonne delle righe B<totali>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Numero di flussi"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Numero di blocchi"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Dimensione compressa"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Rapporto di compressione medio"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Elenco separato da virgole dei nomi dei controlli di integrità presenti nei file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Dimensione del padding dello stream"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Numero di file. Questo serve a mantenere l'ordine delle colonne precedenti uguale a quello delle righe del B<file>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Se B<--verbose> viene specificato due volte, sono incluse colonne aggiuntive nella riga B<totali>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Quantità massima di memoria (in byte) necessaria per decomprimere i file con questa versione B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Versioni future potrebbero aggiungere nuovi tipi di riga e nuove colonne possono essere aggiunte ai tipi di riga esistenti, ma le colonne esistenti non verranno modificate."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Filters help"
msgstr "Aiuto sui filtri"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr "B<xz --robot --filters-help> stampa i filtri supportati nel seguente formato:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr "I<FILTRO>B<:>I<OPZIONE>B<=E<lt>>I<VALORE>B<E<gt>,>I<OPZIONE>B<=E<lt>>I<VALORE>B<E<gt>>..."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<filter>"
msgstr "I<FILTRO>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the filter"
msgstr "Nome del filtro"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<option>"
msgstr "I<OPZIONE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr "Nome di un'opzione specifica del filtro"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr "I<VALORE>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr "Gli intervalli del I<VALORE> numerico appaiono come B<E<lt>>I<MIN>B<->I<MAX>B<E<gt>>. Le scelte per i I<VALORI> stringa sono mostrati in B<E<lt> E<gt>> e separati dal carattere B<|>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr "Ogni filtro è mostrato su una riga dedicata."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Informazione limite memoria"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr "B<xz --robot --info-memory> stampa una singola riga con più colonne separate da tabulazione:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Quantità totale di memoria fisica (RAM) in byte."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limite utilizzo memoria per la compressione in byte (B<--memlimit-compress>). Il valore speciale B<0> indica l'impostazione predefinita, che in modalità thread singola equivale a nessun limite."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Limite utilizzo memoria per la decompressione in byte (B<--memlimit-decompress>). Il valore speciale B<0> indica l'impostazione predefinita, che in modalità thread singola equivale a nessun limite."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "A partire da B<xz> 5.3.4alpha: Utilizzo della memoria per la decompressione a thread multiple in byte (B<--memlimit-mt-decompress>). Questo valore non è mai zero perché viene utilizzato un valore predefinito specifico del sistema mostrato nella colonna 5, se non è stato specificato alcun limite in modo esplicito. Inoltre, non è mai maggiore del valore nella colonna 3, anche se è stato specificato un valore maggiore con B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "A partire da B<xz> 5.3.4alpha: un limite di utilizzo della memoria predefinito specifico del sistema, che viene utilizzato per limitare il numero di thread durante la compressione con un numero automatico di thread (B<--threads=0>) e non è stato specificato alcun limite di utilizzo della memoria (B<--memlimit-compress>). Questo viene utilizzato anche come valore predefinito per B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "A partire da B<xz> 5.3.4alpha: Numero di thread del processore disponibili."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "In futuro, l'output di B<xz --robot --info-memory> potrebbe avere più colonne, ma mai più di una singola riga."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Versione"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr "B<xz --robot --version> stampa il numero di versione di B<xz> e liblzma nel seguente formato:"

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Versione major."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Versione minor. I numeri pari sono stabili. I numeri dispari sono versioni alfa o beta."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Livello di patch per le versioni stabili o solo un contatore per le versioni di sviluppo."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Stabilità. 0 è alfa, 1 è beta e 2 è stabile. I<S> dovrebbe essere sempre 2 quando I<YYY> è pari."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> sono uguali su entrambe le righe se B<xz> e liblzma appartengono allo stesso rilascio delle XZ Utils."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Esempi: 4.999.9beta è B<49990091> e 5.0.0 è B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "CODICE DI USCITA"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Tutto bene."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "È avvenuto un errore."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Si è verificato qualcosa che merita un avvertimento, ma non si sono verificati errori effettivi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Gli avvisi (non gli avvertimenti o gli errori) stampati sullo standard error non influiscono sullo stato di uscita."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "AMBIENTE"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> analizza elenchi di opzioni separate da spazi dalle variabili d'ambiente B<XZ_DEFAULTS> e B<XZ_OPT>, in questo ordine, analizzando prima le opzioni dalla riga di comando. Si noti che solo le opzioni vengono analizzate dalle variabili d'ambiente; tutte le non-opzioni vengono ignorate silenziosamente. L'analisi viene eseguita con B<getopt_long>(3) che viene utilizzato anche per gli argomenti della riga di comando."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr "B<Attenzione:> Impostando queste variabili di ambiente, si sta di fatto modificando programmi e script che lanciano B<xz>.  La maggior parte delle volte va bene impostare i limiti di utilizzo della memoria, il numero di thread e le opzioni di compressione tramite variabili d'ambiente.  Tuttavia, alcune opzioni possono rompere degli script.  Un esempio banale è B<--help> che forza B<xz> a mostrare la pagina di aiuto anziché comprimere o decomprimere file. Esempi meno ovvi sono B<--quiet> e B<--verbose>.  In molti casi funziona bene abilitare l'indicatore di avanzamento usando B<--verbose>, ma in alcune situazioni i messaggi extra creano problemi.  Il livello di prolissità influisce anche sul comportamento di B<--list>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Opzioni predefinite specifiche dell'utente o a livello di sistema. In genere questo viene impostato in uno script di inizializzazione della shell per abilitare il valore predefinito del limitatore di utilizzo della memoria di B<xz>, o per impostare il numero di thread predefinito. Escludendo gli script di inizializzazione della shell e analoghi casi particolari, gli script non dovrebbero mai impostare o annullare l'impostazione di B<XZ_DEFAULTS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Questo serve per passare le opzioni a B<xz> quando non sia possibile impostare le opzioni direttamente sulla riga di comando di B<xz>. Questo è il caso quando B<xz> viene eseguito da uno script o da uno strumento, ad esempio GNU B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Gli script possono usare B<XZ_OPT>, per esempio, per impostare opzioni di compressione predefinite specifiche per lo script. Si raccomanda comunque di permettere agli utenti di sovrascrivere B<XZ_OPT>se questo è ragionevole. Ad esempio, negli script B<sh>(1) si può usare qualcosa come questo:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "COMPATIBILITÀ LZMA UTILS"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "La sintassi della riga di comando di B<xz> è essenzialmente un sopra-insieme di B<lzma>, B<unlzma>, e B<lzcat> come trovati nelle LZMA Utils 4.32.x. Nella maggior parte dei casi è possibili sostituire le LZMA Utils con le  XZ Utils senza rompere gli script esistenti. Ci sono però alcune incompatibilità, che in alcuni casi potrebbero causare problemi."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Livelli di compressione preimpostati"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "La numerazione dei livelli di compressione preimpostati non è identica in B<xz> e nelle LZMA Utils. La differenza più importante è il modo in cui le dimensioni del dizionario vengono mappate sulle diverse preimpostazioni. La dimensione del dizionario è approssimativamente uguale all'utilizzo della memoria del decompressore."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Livello"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "N/A"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Le differenze di dimensione del dizionario influiscono anche sull'utilizzo della memoria del compressore, ma ci sono alcune altre differenze tra le LZMA Utils e le XZ Utils, che rendono la differenza ancora più grande:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Il livello preimpostato predefinito nelle LZMA Utils è B<-7> mentre nelle XZ Utils è B<-6>, quindi entrambi utilizzano un dizionario da 8 MiB per impostazione predefinita."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "File .lzma con flussi vs. senza flussi"

# FIXME end-of-payload
#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "La dimensione non compressa del file può essere memorizzata nell'intestazione B<.lzma>. Le LZMA Utils lo fanno durante la compressione di file regolari. L'alternativa consiste nel memorizzare che la dimensione non compressa è sconosciuta e utilizzare l'indicatore di fine carico utile per indicare il punto in cui il decompressore deve fermarsi. Le LZMA Utils utilizzano questo metodo quando le dimensioni non compresse non sono note, come nel caso, ad esempio, delle pipe."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> supporta la decompressione di file B<.lzma> con o senza il marcatore di fine payload, ma tutti i file B<.lzma> creati da B<xz> utilizzeranno il marcatore di fine payload e avranno dimensioni non compresse contrassegnate come sconosciute nell'intestazione B<.lzma>. Questo può essere un problema in alcune situazioni non comuni. Ad esempio, un decompressore B<.lzma> in un dispositivo embedded potrebbe funzionare solo con file con dimensioni non compresse note. Se si incorre in questo problema, occorre utilizzare le LZMA Utils oppure l'LZMA SDK per creare dei file B<.lzma> con dimensioni non compresse note."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "File .lzma non supportati"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "Il formato B<.lzma> permette valori I<lc> fino a 8, e valori I<lp> fino a 4. Le LZMA Utils possono decomprimere file con qualunque I<lc> e I<lp>, ma creeranno sempre file con B<lc=3> e B<lp=0>. Creare file con altri I<lc> e I<lp> è possibile con B<xz> e con l'LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "L'implementazione del filtro LZMA1 in liblzma richiede che la somma di I<lc> e I<lp> non debba superare 4. Pertanto, i file B<.lzma> che superano questa limitazione non possono essere decompressi con B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "Le LZMA Utils creano solo file B<.lzma> con dimensione del dizionario 2^I<n> (una potenza di 2) ma accettano file con qualsiasi dimensione di dizionario. liblzma accetta solo file B<.lzma> con dimensione del dizionario 2^I<n> o 2^I<n> + 2^(I<n>-1). Questo serve a limitare i falsi positivi quando si cerca di identificare i file B<.lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "All'atto pratico queste limitazioni non dovrebbero essere un problema, perché praticamente tutti i file B<.lzma> sono stati compressi con impostazioni che liblzma accetterà."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Dati sporchi finali"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Quando si decomprime, le LZMA Utils ignorano automaticamente tutto quello che c'è dopo il primo flusso B<.lzma>. Nella maggior parte delle situazioni questo è un baco. Questo significa anche che le LZMA Utils non supportano la decompressione di file B<.lzma> concatenati."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Se sono rimasti dati dopo il primo flusso B<.lzma>, B<xz> considera il file corrotto a meno che sia stato utilizzato B<--single-stream>. Questo può far rompere script incomprensibili che hanno assunto che i dati sporchi finali vengano ignorati."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "NOTE"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "L'output compresso può variare"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "L'esatto output compresso prodotto dallo stesso file di input non compresso può variare tra le versioni delle XZ Utils, anche se le opzioni di compressione sono le stesse. Questo perché il codificatore può essere stato migliorato (compressione più veloce o migliore) senza influire sul formato del file. L'output può variare anche tra diverse build della stessa versione delle XZ Utils, se vengono utilizzate opzioni di build differenti."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Quanto sopra significa che una volta che B<--rsyncable> è stato implementato, i file risultanti non saranno necessariamente rsync-abili a meno che sia i vecchi che i nuovi file non siano stati compressi con la stessa versione di xz. Questo problema può essere risolto se una parte dell'implementazione del codificatore viene congelata per mantenere stabile l'output rsync-abile tra le versioni di xz."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Abilita i decompressori .xz"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "Le implementazioni dei decompressori B<.xz> embedded, come XZ Embedded, non necessariamente supportano file creati con tipi di integrità I<CONTROLLO> diversi da B<none> e B<crc32>. Dal momento che il valore predefinito è B<--check=crc64>, occorre specificare B<--check=none> o B<--check=crc32> quando si creano file per sistemi embedded."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "Al di fuori dei sistemi embedded, tutti i decompressori in formato B<.xz> supportano tutti i tipi di I<CONTROLLO>, o almeno sono in grado di decomprimere il file senza verificare il controllo di integrità se il particolare I<CONTROLLO> non è supportato."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded supporta i filtri BCJ, ma solo con offset iniziale predefinito."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "ESEMPI"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Informazioni di base"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Comprime il file I<foo> in I<foo.xz> utilizzando il livello di compressione predefinito (B<-6>) e rimuove I<foo> se la compressione ha esito positivo:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz foo\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Decomprime I<bar.xz> in I<bar> e non rimuove I<bar.xz> anche se la decompressione ha successo:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "Crea I<baz.tar.xz> con il preset B<-4e> (B<-4 --extreme>), che è più lenta della predefinita B<-6>, ma ha bisogno di meno memoria per la compressione e decompressione (48\\ MiB e 5\\ MiB, rispettivamente):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Una combinazione di file compressi e non compressi può essere decompressa sullo output standard con un singolo comando:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr ""

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Compressione parallela di più file"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "Su GNU e *BSD, B<find>(1)  e B<xargs>(1) possono essere usati per parallelizzare la compressione di più file:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "L'opzione B<-p> di B<xargs>(1) imposta il numero di processi B<xz> paralleli. Il valore migliore per l'opzione B<-n> dipende dal numero di file da comprimere. Se sono presenti solo un paio di file, il valore dovrebbe probabilmente essere 1; con decine di migliaia di file, 100 o anche di più può essere appropriato per ridurre il numero di processi B<xz> che B<xargs>(1) alla fine creerà."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "L'opzione B<-T1> per B<xz> serve a forzare la modalità a thread singola, perché B<xargs> (1) viene utilizzato per controllare la quantità di parallelizzazione."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Modalità robot"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Calcola quanti byte sono stati salvati in totale dopo la compressione di più file:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Uno script potrebbe voler sapere se si sta utilizzando una versione di B<xz> sufficientemente recente. Il seguente script B<sh>(1) controlla che il numero di versione dello strumento B<xz> sia almeno 5.0.0. Questo metodo è compatibile con le vecchie versioni beta, che non supportavano l'opzione B<--robot>:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Imposta un limite di utilizzo della memoria per la decompressione utilizzando B<XZ_OPT>, ma se è già stato impostato un limite, non lo aumenta:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "L'uso più semplice delle catene di filtri personalizzate è la personalizzazione di un preset di LZMA2. Questo può essere utile, perché i preset coprono solamente un sottoinsieme di tutte le combinazioni di impostazioni di compressione potenzialmente utili."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "Le colonne CompCPU delle tabelle dalle descrizioni delle opzioni B<-0> ... B<-9> e B<--extreme> sono utili quando si personalizzano i preset di LZMA2. Di seguito sono riportate le parti rilevanti raccolte da queste due tabelle:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Se si sa che un file richiede un dizionario piuttosto grande (ad esempio, 32\\ MiB) per essere compresso bene, ma si desidera comprimerlo più velocemente di quanto farebbe B<xz -8>, è possibile modificare un preset con un basso valore CompCPU (ad esempio, 1) per utilizzare un dizionario più grande:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Con alcuni file, il comando sopra potrebbe essere più veloce di B<xz -6> e la compressione significativamente migliore. Tuttavia, si deve sottolineare che solo alcuni file traggono beneficio da un dizionario grande e mantengono basso il valore di CompCPU. La situazione più ovvia in cui un dizionario grande può aiutare molto è un archivio contenente file molto simili di almeno un paio di megabyte ciascuno. La dimensione del dizionario deve essere significativamente più grande di ogni singolo file per permettere a LZMA2 di trarre pieno vantaggio dalle somiglianze tra file consecutivi."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Se l'utilizzo molto elevato della memoria del compressore e del decompressore è accettabile, e il file da comprimere è almeno di diverse centinaia di megabyte, potrebbe essere utile utilizzare un dizionario ancora più grande dei 64 MiB che B<xz -9> utilizzerebbe:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "L'uso di B<-vv> (B<--verbose --verbose>) come nell'esempio precedente può essere utile per vedere i requisiti di memoria del compressore e del decompressore. Tenere presente che l'utilizzo di un dizionario più grande della dimensione del file non compresso è uno spreco di memoria, quindi il comando precedente non è utile per i file di piccole dimensioni."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "A volte il tempo di compressione non importa, ma l'utilizzo di memoria del decompressore deve essere tenuta bassa, per esempio, per permettere di decomprimere il file in un sistema embedded. Il comando seguente usa B<-6e> (B<-6 --extreme>)  come base e imposta il dizionario a soli 64\\ KiB. Il file risultante può essere decompresso con XZ Embedded (ecco perché c'è B<--check=crc32>) usando circa 100\\ KiB di memoria."

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Se si desidera spremere il maggior numero possibile di byte, a volte può essere utile regolare il numero di bit di contesto letterale (I<lc>) e il numero di bit di posizione (I<pb>). Anche la regolazione del numero di bit di posizione letterale (I<lp>) potrebbe essere d'aiuto, ma di solito I<lc> e I<pb> sono più importanti. Per esempio, un archivio di codici sorgente contiene principalmente testo US-ASCII, quindi qualcosa come il seguente potrebbe produrre un file leggermente (0,1\\ %) più piccolo di B<xz -6e> (provare anche senza B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "Usare un altro filtro insieme a LZMA2 può migliorare la compressione per alcuni tipi di file. Ad esempio, per comprimere una libreria condivisa x86-32 o x86-64 usare il filtro BCJ:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Si noti che l'ordine delle opzioni di filtro è significativo. Se viene specificato B<--x86> dopo B<--lzma2>, B<xz> darà un errore, perché non può esserci alcun filtro dopo LZMA2 e anche perché il filtro BCJ x86 non può essere utilizzato come ultimo filtro della catena."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Il filtro Delta insieme con LZMA2 può dare buoni risultati sulle immagini bitmap. Di solito dovrebbe battere il PNG, il quale ha alcuni filtri più avanzati rispetto al semplice delta ma utilizza Deflate per la compressione effettiva."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "L'immagine deve essere  salvata in un formato non compresso, ad esempio un TIFF non compresso. Il parametro distanza del filtro Delta è impostato in modo che corrisponda al numero di byte per pixel nell'immagine. Per esempio, un bitmap a 24 bit richiede B<dist=3>, e va anche bene passare B<pb=0> a LZMA2 per adattarsi all'allineamento a tre byte:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr ""

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Se più immagini sono state inserite in un singolo archivio (ad esempio, B<.tar>), il filtro Delta funzionerà anche su questo purché tutte le immagini abbiano lo stesso numero di byte per pixel."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "VEDERE ANCHE"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr "XZ Utils: E<lt>https://xz.tukaani.org/xz-utils/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr "XZ Embedded: E<lt>https://xz.tukaani.org/xz-embedded/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr "08/04/2024"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec - Piccoli compressori .xz e .lzma"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<OPZIONE...>] [I<FILE...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<OPZIONE...>] [I<FILE...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> è uno strumento di sola decompressione basato su liblzma per i file B<.xz> (e solo B<.xz>). B<xzdec> è destinato a funzionare come sostituto drop-in di B<xz> (1) nelle situazioni più comuni in cui è stato scritto uno script che utilizza B<xz --decompress --stdout> (e possibilmente alcune altre opzioni di uso comune) per decomprimere i file B<.xz>. B<lzmadec> è identico a B<xzdec> tranne nel fatto che supporta file B<.lzma> invece di B<.xz>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "Per ridurre la dimensione dell'eseguibile, B<xzdec> non supporta né il multi-thread né la localizzazione, e non legge le opzioni dalle variabili di ambiente B<XZ_DEFAULTS> e  B<XZ_OPT>. B<xzdec> non supporta la visualizzazione delle informazioni di avanzamento: inviare B<SIGINFO> a B<xzdec> non fa nulla mentre inviare B<SIGUSR1> termina il processo anziché mostrare le informazioni di avanzamento."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Ignorato per compatibilità con B<xz>(1). B<xzdec> supporta solo la decompressione."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Ignorato per compatibilità con B<xz>(1). B<xzdec> non crea né rimuove mai file."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Ignorato per compatibilità con B<xz>(1). B<xzdec> scrive sempre i dati decompressi sullo standard output."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Specificare questa opzione una sola volta non ha effetto, poiché B<xzdec> non visualizza mai alcun avvertimento né avviso. Specificare questa opzione due volte per eliminare gli errori."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Ignorato per compatibilità con B<xz>(1). B<xzdec> non utilizza mai il codice di uscita 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Mostra un messaggio di aiuto e termina con successo."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Mostra il numero di versione di B<xzdec> e liblzma."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Andava tutto bene."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> non ha alcun messaggio di avvertimento come B<xz> (1), quindi lo stato di uscita 2 non viene utilizzato da B<xzdec>."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Utilizzare B<xz>(1)  al posto di B<xzdec> o B<lzmadec> per l'utilizzo normale abituale. B<xzdec> e B<lzmadec> sono pensati solo per situazioni in cui sia importante avere un decompressore più piccolo rispetto a quello con funzionalità complete B<xz>(1)."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> e B<lzmadec> non sono veramente così piccoli. La dimensione può essere ridotta ulteriormente eliminando delle funzionalità da liblzma al momento della compilazione, ma questo di solito non dovrebbe essere fatto per gli eseguibili distribuiti nelle tipiche distribuzioni di sistemi operativi non embedded. Se c'è bisogno di un decompressore B<.xz> veramente piccolo, si consideri l'utilizzo di XZ Embedded."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30/06/2013"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo - mostra le informazioni nell'intestazione di file .lzma"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<FILE...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> mostra le informazioni contenute nell'intestazione del file B<.lzma>. Legge i primi 13 byte del I<FILE> specificato, decodifica l'intestazione, e la stampa su standard output in formato leggibile dall'uomo. Se nessun I<FILE> è specificato o se I<FILE> è B<->, viene letto lo standard input."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "Di solito le informazioni più interessanti sono la dimensione non compressa e la dimensione del dizionario. Le dimensioni non compresse possono essere visualizzate solo se il file è nella variante di formato B<.lzma> \"senza flussi\". La quantità di memoria necessaria per decomprimere il file è di alcune dozzine di kilobyte più la dimensione del dizionario."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> è incluso nelle XZ Utils principalmente per retrocompatibilità con le LZMA Utils."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "BUG"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> usa B<MB> mentre il suffisso corretto dovrebbe essere B<MiB> (2^20 byte).  Questo serve per rendere l'output compatibile con quello delle LZMA Utils."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "2025-03-06"
msgstr "06/03/2025"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff - confronto tra file compressi"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xzcmp> [I<OPZIONE...>] I<FILE1> [I<FILE2>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr "B<xzdiff> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzcmp> \\&... (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzdiff> \\&... (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr "B<xzcmp> e B<xzdiff> confrontano i contenuti non compressi di due file.  I dati non compressi e le opzioni sono passate a B<cmp>(1)  o B<diff>(1)  a meno che sia specificato B<--help> o B<--version>."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr "Se sono indicati sia I<FILE1> che I<FILE2>, essi possono essere file non compressi oppure file nei formati che B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), o B<lz4>(1) possono decomprimere. I comandi di decompressione necessari sono dedotti dai suffissi dei nomi dei file I<FILE1> e I<FILE2>. Un file con un suffisso non riconosciuto viene considerato o non compresso oppure in un formato che B<xz>(1) può decomprimere."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr "Se viene fornito un solo nome di file, I<FILE1> deve avere un suffisso di un formato di compressione supportato e il nome di I<FILE2> si assume che sia I<FILE1> senza il suffisso del formato di compressione."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "I comandi B<lzcmp>, e B<lzdiff> sono forniti per retrocompatibilità con le LZMA Utils. Sono deprecati e saranno rimosso in una versione futura."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr "Se avviene un errore nella decompressione, lo stato di uscita è B<2>. Altrimenti viene usato lo stato di uscita di B<cmp>(1)  o B<diff>(1)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr "xzgrep - Cerca pattern in file potenzialmente compressi"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xzgrep> [I<OPZIONE...>] [I<LISTA_PATTERN>] [I<FILE...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&... (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&... (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&... (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr "B<xzgrep> invoca B<grep>(1) sui contenuti non compressi dei file. I formati dei I<FILE> sono dedotti dai suffissi dei nomi dei file. Sarà decompresso qualsiasi file con un suffisso supportato da B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), o B<lz4>(1); tutti gli altri file saranno considerati non compressi."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr "Se non sono specificati I<FILE>, o se I<FILE> è B<->, viene letto lo standard input. Quando si legge da standard input vengono decompressi solamente i file supportati da B<xz>(1). Gli altri file sono considerati già non compressi."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr "Sono supportate la maggior parte delle I<OPZIONI> di B<grep>(1). Tuttavia, le opzioni seguenti non sono supportate:"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-r>, B<--recursive>"
msgstr "B<-r>, B<--recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-R>, B<--dereference-recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-d>, B<--directories=>I<AZIONE>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-Z>, B<--null>"
msgstr "B<-Z>, B<--null>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-z>, B<--null-data>"
msgstr "B<-z>, B<--null-data>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--include=>I<glob>"
msgstr "B<--include=>I<GLOB>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr "B<--exclude=>I<GLOB>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-from=>I<file>"
msgstr "B<--exclude-from=>I<FILE>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr "B<--exclude-dir=>I<GLOB>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr "B<xzegrep> è un alias per B<xzgrep -E>.  B<xzfgrep> è un alias per B<xzgrep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "I comandi B<lzgrep>, B<lzegrep>, e B<lzfgrep> sono forniti per retrocompatibilità con le LZMA Utils. Sono deprecati e saranno rimosso in una versione futura."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "Almeno una corrispondenza è stata trovata in almeno uno dei file input. Nessun errore è avvenuto."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "Non sono state trovate corrispondenze in nessuno dei file di input. Non si sono verificati errori."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "Sono avvenuti uno o più errori. Non è noto se sono state trovate delle corrispondenze."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr "Se B<GREP> è impostato a un valore non vuoto, verrà usato al posto di B<grep>, B<grep -E>, o B<grep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless - mostra file (testuali) compressi xz o lzma"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<FILE>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<FILE>...] (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr "B<xzless> è un filtro che visualizza testo da file compressi a un terminale.  I file supportati da B<xz>(1) vengono decompressi; gli altri file sono considerati già non compressi.  Se non viene dato alcun I<FILE>, B<xzless> legge dallo standard input."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> usa B<less>(1)  per mostrare il suo output. A differenza di B<xzmore>, la scelta del paginatore non può essere modificata impostando una variabile d'ambiente. I comandi si basano sia su B<more> (1) che su B<vi> (1) e consentono il movimento in avanti e all'indietro e la ricerca. Per ulteriori informazioni vedere il manuale di B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Il comando chiamato B<lzless> è fornito per retrocompatibilità con le LZMA Utils. È deprecato e sarà rimosso in una versione futura."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Un elenco di caratteri speciali per la shell. Impostato da B<xzless> a meno che sia già impostato nell'ambiente."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Valorizzare con una riga di comando per lanciare il decompressore B<xz>(1) per preprocessare i file input per B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore - mostra file (testuali) compressi xz o lzma"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzmore> [I<FILE>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzmore> [I<FILE>...] (DEPRECATO)"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr "B<xzmore> visualizza il testo da file non compressi a un terminale usando B<more>(1).  I file supportati da B<xz>(1) vengono decompressi; gli altri file sono considerati non compressi.  Se non viene dato alcun I<FILE>, B<xzmore> legge dallo standard input. Si veda il manuale di B<more>(1) per i comandi da tastiera."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr "Si noti che lo scorrimento all'indietro potrebbe non essere possibile a seconda dell'implementazione di B<more>(1). Ciò è dovuto al fatto che B<xzmore> utilizza una pipe per passare i dati decompressi a B<more>(1). B<xzless>(1) utilizza B<less>(1) che fornisce funzionalità più avanzate."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Il comando B<lzmore> è fornito per retrocompatibilità con le LZMA Utils. È deprecato e sarà rimosso in una versione futura."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr "B<PAGER>"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr "Se B<PAGER> è impostato, il suo valore è usato come paginatore al posto di B<more>(1)."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
