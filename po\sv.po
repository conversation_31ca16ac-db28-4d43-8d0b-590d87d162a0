# SPDX-License-Identifier: 0BSD
#
# Swedish translation for xz.
# Copyright (C) The XZ Utils authors and contributors
# This file is published under the BSD Zero Clause License.
#
# <PERSON> <<EMAIL>>, 2019.
# <PERSON> <and<PERSON>.j<PERSON><PERSON>@norsjovallen.se>, 2024.
# <PERSON> <<EMAIL>>, 2022, 2023, 2024, 2025.
# #
msgid ""
msgstr ""
"Project-Id-Version: xz 5.7.1-dev1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2025-01-24 11:03+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Swedish <<EMAIL>>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Bookmarks: -1,10,-1,-1,-1,-1,-1,-1,-1,-1\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Ogiltigt argument till --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: För många argument till --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "I --block-list saknas blockstorlek efter filterkedjenummer ”%c:”"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 kan endast användas som det sista elementet i --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Okänd filformatstyp"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Integritetskontrolltyp stöds inte"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Endast en fil kan anges med ”--files” eller ”--files0”."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Miljövariabeln %s innehåller för många argument"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Komprimeringsstöd inaktiverades vid byggtid"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Dekomprimeringsstöd inaktiverades vid byggtid"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Komprimering av lzip-filer (.lz) stöds inte"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list ignoreras såvida du inte komprimerar till .xz-formatet"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Med --format=raw krävs --suffix=.SUF om data inte skrivs till standard ut"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Maximalt antal filter är fyra"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Fel i flagga för --filters%s=FILTER:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Begränsning av minnesanvändning är allt för låg för den angivna filteruppsättningen."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "filterkedja %u används av --block-list men inte specificerad med --filters%u="

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Det avråds från att använda en förinställning i rått läge."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "De exakta flaggorna för förinställningar kan variera mellan programversioner."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Formatet .lzma har endast stöd för LZMA1-filtret"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA1 kan inte användas tillsammans med .xz-formatet"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "Filterkedja %u är inkompatibel med --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Växlar till entrådsläge på grund av --flush-timeout"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Flaggor som inte stöds i filterkedjan %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Använder upp till %<PRIu32> trådar."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Filterkedja eller filterflaggor stöds inte"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Dekomprimering kommer att kräva %s MiB minne."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Reducerade antalet trådar från %s till %s för att inte överstiga begränsningen av minnesanvändning på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Reducerade antalet trådar från %s till en. Den automatiska minnesanvändningsgränsen på %s MiB överskrids fortfarande. %s MiB minne krävs. Fortsätter i alla fall."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Ändrar till enkeltrådat läge för att inte överskrida minnesanvändningsgränsen på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Justerade storlek för LZMA%c-lexikon från %s MiB till %s MiB för att inte överstiga begränsningen av minnesanvändning på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Justerade storlek för LZMA%c-lexikon för --filters%u från %s MiB till %s MiB för att inte överskrida minnesanvändningsgränsen på %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Fel vid ändring till filterkedja %u: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Fel vid skapande av rörledning: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: poll() misslyckades: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Filen verkar ha flyttats, tar inte bort"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Kan inte ta bort: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Kan inte sätta filägaren: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Kan inte sätta filgruppen: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Kan inte sätta filrättigheterna: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Synkroniseringen av filen misslyckades: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s:Synkroniseringen av filens katalog misslyckades:: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Fel vid hämtning av filstatusflaggor från standard in: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Är en symbolisk länk, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Är en katalog, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Är inte en vanlig fil, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Filen har setuid- eller setgid-biten satt, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Filen har stickybiten satt, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Indatafilen har mer än en hårdlänk, hoppar över"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Tomt filnamn, hoppar över"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Fel vid återställning av statusflaggorna för standard in: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Fel vid hämtning av filstatusflaggorna från standard ut: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Att öppna katalogen misslyckades: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Destination is not a regular file"
msgstr "%s: Destinationen är inte en vanlig fil"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Fel vid återställning av O_APPEND-flaggan till standard ut: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Stängning av filen misslyckades: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Sökning misslyckades vid skapande av gles fil: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Läsfel: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Fel vid sökning i fil: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Oväntat filslut"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Skrivfel: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Inaktiverad"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Totalt mängd fysiskt minne (RAM):"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Antal processortrådar:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Komprimering:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Dekomprimering:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Flertrådad dekomprimering:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Standard för -T0:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Hårdvaruinformation:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Minnesanvändningsgränser:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Strömmar:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Block:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Komprimerad storlek:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Okomprimerad storlek:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Förhållande:"

#: src/xz/list.c
msgid "Check:"
msgstr "Kontroll:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Strömfyllnad:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Minne som behövs:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Storlek i huvuden:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Antal filer:"

#: src/xz/list.c
msgid "Stream"
msgstr "Ström"

#: src/xz/list.c
msgid "Block"
msgstr "Block"

#: src/xz/list.c
msgid "Blocks"
msgstr "Block"

#: src/xz/list.c
msgid "CompOffset"
msgstr "Komprimerad position"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "Okomprimerad position"

#: src/xz/list.c
msgid "CompSize"
msgstr "Komprimerad storlek"

#: src/xz/list.c
msgid "UncompSize"
msgstr "Okomprimerad storlek"

#: src/xz/list.c
msgid "TotalSize"
msgstr "Total storlek"

#: src/xz/list.c
msgid "Ratio"
msgstr "Förhållande"

#: src/xz/list.c
msgid "Check"
msgstr "Kontroll"

#: src/xz/list.c
msgid "CheckVal"
msgstr "Kontrollvärde"

#: src/xz/list.c
msgid "Padding"
msgstr "Fyllnad"

#: src/xz/list.c
msgid "Header"
msgstr "Huvud"

#: src/xz/list.c
msgid "Flags"
msgstr "Flaggor"

#: src/xz/list.c
msgid "MemUsage"
msgstr "Minnesanvändning"

#: src/xz/list.c
msgid "Filters"
msgstr "Filters"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Ingen"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Okänd-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Okänd-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Okänd-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Okänd-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Okänd-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Okänd-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Okänd-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Okänd-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Okänd-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Okänd-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Okänd-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Okänd-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Fil är tom"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: För liten för att vara en giltig .xz-fil"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Strmr  Block    Komprimerd  Okomprimerd  Förh.  Kntrll  Filnamn"

#: src/xz/list.c
msgid "Yes"
msgstr "Ja"

#: src/xz/list.c
msgid "No"
msgstr "Nej"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr "Minsta XZ Utils version:"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s fil\n"
msgstr[1] "%s filer\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Total:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list fungerar endast med .xz-filer (--format=xz eller --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Pröva ”lzmainfo” med .lzma-filer."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list saknar stöd för att läsa från standard in"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Fel vid läsning av filnamn: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Oväntat slut av indata vid läsning av filnamn"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Nulltecken hittades vid läsning av filnamn; kanske du menade att använda ”--files0” istället för ”--files”?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Komprimering och dekomprimering med --robot stöds inte än."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Kan inte läsa data från standard in när filnamn läses från standard in"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Internt fel"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Kan inte etablera signalhanterare"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Ingen integritetskontroll; kan inte verifiera filintegritet"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Typ av integritetskontroll stöds inte; verifierar inte filintegritet"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Begränsning av minnesanvändning uppnådd"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Filformat okänt"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Flaggor stöds inte"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Komprimerad data är korrupt"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Oväntat avslut av indata"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s MiB minne krävs. Begränsaren inaktiverad."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s MiB minne krävs. Begränsningen är %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Filterkedja: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "Testa ”%s --help” för mer information."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr "Fel vid utskrift av hjälptexten (felkod %d)"

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr "Användning: %s [ALTERNATIV]... [FIL]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr "Komprimera eller dekomprimera FILER i formatet .xz."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "Obligatoriska argument för långa alternativ är också obligatoriska för korta alternativ."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation mode:"
msgstr "Operationsläge:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force compression"
msgstr "tvinga komprimering"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force decompression"
msgstr "tvinga dekomprimering"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr "testa den komprimerade filens integritet"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr "lista information om .xz-filer"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation modifiers:"
msgstr "Operationsmodifierare:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr "behåll (ta inte bort) indatafiler"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr "tvinga överskrift av utdatafilen och (av)komprimera länkar"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "write to standard output and don't delete input files"
msgstr "skriv till standardutdata och ta inte bort indatafiler"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr "synkronisera inte utdatafilen till lagringsenheten innan du tar bort indatafilen"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "dekomprimera endast den första strömmen och ignorera eventuellt kvarvarande indata i tysthet"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr "skapa inte glesa filer vid dekomprimering"

#: src/xz/message.c
msgid ".SUF"
msgstr ".SUF"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr "använd suffixet '.SUF' på komprimerade filer"

#: src/xz/message.c
msgid "FILE"
msgstr "FIL"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr "läs filnamn att bearbeta från FIL; om FIL utelämnas läses filnamnen från standardinmatningen; filnamn måste avslutas med nyradstecknet"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr "som --filer men använd null-tecknet som terminator"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr "Grundläggande filformat och komprimeringsalternativ:"

#: src/xz/message.c
msgid "FORMAT"
msgstr "FORMAT"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr "filformat för kodning eller avkodning; möjliga värden är 'auto' (standard), 'xz', 'lzma', 'lzip' och 'raw'"

#: src/xz/message.c
msgid "NAME"
msgstr "NAMN"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr "integritetskontrolltyp: 'ingen' (använd med försiktighet), 'crc32', 'crc64' (standard) eller 'sha256'"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr "verifiera inte integritetskontrollen vid dekomprimering"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr "förinställd komprimering; standard är 6; ta hänsyn till användningen av kompressor *och* dekomprimeringsminne innan du använder 7-9!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr "försök att förbättra kompressionsförhållandet genom att använda mer CPU-tid; påverkar inte dekomprimeringsminneskraven"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr "NUM"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr "använd som mest NUM trådar; standard är 0 som använder lika många trådar som det finns processorkärnor"

#: src/xz/message.c
msgid "SIZE"
msgstr "STORLEK"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr "starta ett nytt .xz-block efter varje STORLEK-byte med inmatning; använd detta för att ställa in blockstorleken för flertrådad kompression"

#: src/xz/message.c
msgid "BLOCKS"
msgstr "BLOCK"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr "starta ett nytt .xz-block efter de givna kommaseparerade intervallen av okomprimerad data; ange eventuellt ett filterkedjenummer (0-9) följt av ett ':' före den okomprimerade datastorleken"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr "vid komprimering, om mer än NUM millisekunder har gått sedan föregående tömning och läsning av mer indata skulle blockeras, rensas all väntande data ut"

#: src/xz/message.c
msgid "LIMIT"
msgstr "GRÄNSVÄRDE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr "ställ in minnesanvändningsgräns för komprimering, dekompression, flertrådad dekompression eller alla dessa; GRÄNSVÄRDE är i byte, % av RAM-minnet eller 0 för standardinställningar"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "om komprimeringsinställningarna överskrider minnesanvändningsgränsen, ge ett felmeddelande istället för att justera inställningarna nedåt"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr "Anpassad filterkedja för komprimering (ett alternativ till förinställningar):"

#: src/xz/message.c
msgid "FILTERS"
msgstr "FILTER"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr "ställ in filterkedjan med hjälp av liblzma-filtersträngsyntaxen; använd --filters-help för mer information"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr "ställ in ytterligare filterkedjor med hjälp av liblzma-filtersträngsyntaxen att använda med --block-list"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr "visa mer information om liblzma filtersträngsyntax och avsluta"

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr "ALTERNATIV"

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr "LZMA1 eller LZMA2; OPTS är en kommaseparerad lista med noll eller fler av följande alternativ (giltiga värden; standard):"

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr "FÖRINSTÄLLNING"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr "återställ alternativ till en förinställning"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr "ordboksstorlek"

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr "antal bokstavliga kontextbitar"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr "antal bokstavliga positionsbitar"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr "antal positionsbitar"

#: src/xz/message.c
msgid "MODE"
msgstr "LÄGE"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression mode"
msgstr "komprimeringsläge"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr "fin längd på en match"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr "träffsökare"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr "maximalt sökdjup; 0=automatisk (standard)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr "x86 BCJ-filter (32-bitars och 64-bitars)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr "ARM BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr "ARM-Thumb BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr "ARM64 BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr "PowerPC BCJ-filter (endast big endian)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr "IA-64 (Itanium) BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr "SPARC BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr "RISC-V BCJ-filter"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr "Giltiga ALTERNATIV för alla BCJ-filter:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr "startoffset för omvandlingar (standard=0)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr "Delta filter; giltiga ALTERNATIV (giltiga värden; standard):"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr "avståndet mellan byte som subtraheras från varandra"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Other options:"
msgstr "Andra alternativ:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr "dämpa varningar; ange två gånger för att dämpa fel också"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr "var utförlig; specificera två gånger för ännu mer utförlighet"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr "åt inte varningar påverka avslutningsstatus"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use machine-parsable messages (useful for scripts)"
msgstr "använd maskintolkningsbara meddelanden (användbara för skript)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "visa den totala mängden RAM och de för närvarande aktiva minnesanvändningsgränserna och avsluta"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr "visa den korta hjälpen (visar endast de grundläggande alternativen)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr "visa den långa hjälpen och avsluta"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr "visa den korta hjälpen och avsluta"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr "visa den långa hjälpen (visar även de avancerade alternativen)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the version number and exit"
msgstr "visa versionsnumret och avsluta"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "With no FILE, or when FILE is -, read standard input."
msgstr "Med FIL, eller när FIL är -, läs standard in."

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr ""
"Rapportera fel till <%s> (på engelska eller finska).\n"
"Rapportera översättningsfel till <<EMAIL>>"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s home page: <%s>"
msgstr "%s hemsida: <%s>"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "DETTA ÄR EN UTVECKLINGSVERSION SOM INTE ÄR AVSEDD FÖR PRODUKTIONSANVÄNDNING."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr "Filterkedjor ställs in med alternativen --filters=FILTERS eller --filters1=FILTER ... --filters9=FILTER. Varje filter i kedjan kan separeras med mellanslag eller '--'. Alternativt kan en förinställd %s specificeras istället för en filterkedja."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "Filtren som stöds och flaggorna för dem är:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "Alternativ måste vara ”namn=värde”-par separerade med kommatecken"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Ogiltigt flaggnamn"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Invalid option value"
msgstr "Ogiltigt alternativvärde"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "LZMA1/LZMA2-förinställning stöds inte: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Summan av lc och lp får inte överstiga 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Filnamn har okänd filändelse, hoppar över"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Filen har redan ändelsen ”%s”, hoppar över"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Ogiltig filnamnsändelse"

#: src/xz/util.c src/liblzma/common/string_conversion.c
msgid "Value is not a non-negative decimal integer"
msgstr "Värdet är inte ett icke-negativt, decimalt heltal"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Ogiltig multipeländelse"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Giltiga suffix är ”KiB” (2^10), ”MiB” (2^20) och ”GiB” (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Värdet för flaggan ”%s” måste vara inom intervallet [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Komprimerad data kan inte läsas från en terminal"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Komprimerad data kan inte skrivas till en terminal"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr "Användning: %s [--help] [--version] [FIL]…\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr "Visa information som lagrats i .lzma-filhuvudet"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "Filen är för liten för att vara en giltig .lzma-fil"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Inte en .lzma-fil"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Skrivning till standard ut misslyckades"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Okänt fel"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported preset"
msgstr "Förinställning stöds inte"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported flag in the preset"
msgstr "Flagga som inte stöds i förinställningen"

#: src/liblzma/common/string_conversion.c
msgid "Unknown option name"
msgstr "Okänt alternativnamn"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr "Alternativvärdet får inte vara tomt"

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr "Värde utanför intervallet"

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr "Det här alternativet stöder inte några multiplikatorsuffix"

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "Ogiltigt multiplikatorsuffix (KiB, MiB eller GiB)"

#: src/liblzma/common/string_conversion.c
msgid "Unknown filter name"
msgstr "Okänt filternamn"

#: src/liblzma/common/string_conversion.c
msgid "This filter cannot be used in the .xz format"
msgstr "Detta filter kan inte användas med .xz-formatet"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr "Minnesallokering misslyckades"

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr "Tom sträng är inte tillåten, försök '6' om ett standardvärde behövs"

#: src/liblzma/common/string_conversion.c
msgid "The maximum number of filters is four"
msgstr "Det maximala antalet filter är fyra"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr "Filternamn saknas"

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr "Ogiltig filterkedja ('lzma2' saknas på slutet?)"
