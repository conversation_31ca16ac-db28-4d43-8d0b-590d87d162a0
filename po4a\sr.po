# SPDX-License-Identifier: 0BSD
#
# Serbian translation of xz-man.
# Copyright (©) The XZ Utils authors and contributors
# This file is distributed under the BSD Zero Clause License.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: xz-man 5.8.0-pre1\n"
"POT-Creation-Date: 2025-03-08 14:50+0200\n"
"PO-Revision-Date: 2025-05-18 07:01+0200\n"
"Last-Translator: Мирослав Николић <<EMAIL>>\n"
"Language-Team: Serbian <(nothing)>\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.5\n"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "XZ"
msgstr "XZ"

#. type: TH
#: ../src/xz/xz.1
#, no-wrap
msgid "2025-03-08"
msgstr "08.03.2025."

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "Tukaani"
msgstr "Tukaani"

#. type: TH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZ Utils"
msgstr "XZ Utils"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "NAME"
msgstr "НАЗИВ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "xz, unxz, xzcat, lzma, unlzma, lzcat - Compress or decompress .xz and .lzma files"
msgstr "xz, unxz, xzcat, lzma, unlzma, lzcat – Запакује или распакује „.xz“ и „.lzma датотеке"

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SYNOPSIS"
msgstr "САЖЕТАК"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> [I<option...>] [I<file...>]"
msgstr "B<xz> [I<опција...>] [I<датотека...>]"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "COMMAND ALIASES"
msgstr "АЛИЈАСИ НАРЕДБЕ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unxz> is equivalent to B<xz --decompress>."
msgstr "B<unxz> је исто као и B<xz --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzcat> is equivalent to B<xz --decompress --stdout>."
msgstr "B<xzcat> је исто као и B<xz --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzma> is equivalent to B<xz --format=lzma>."
msgstr "B<lzma> је исто као и B<xz --format=lzma>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<unlzma> is equivalent to B<xz --format=lzma --decompress>."
msgstr "B<unlzma> је исто као и B<xz --format=lzma --decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzcat> is equivalent to B<xz --format=lzma --decompress --stdout>."
msgstr "B<lzcat> је исто као и B<xz --format=lzma --decompress --stdout>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When writing scripts that need to decompress files, it is recommended to always use the name B<xz> with appropriate arguments (B<xz -d> or B<xz -dc>)  instead of the names B<unxz> and B<xzcat>."
msgstr "Приликом писања скрипти које треба да распакују датотеке, препоручује се да се увек користи назив B<xz> са одговарајућим аргументима (B<xz -d> или B<xz -dc>) уместо назива B<unxz> и B<xzcat>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "DESCRIPTION"
msgstr "ОПИС"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> is a general-purpose data compression tool with command line syntax similar to B<gzip>(1)  and B<bzip2>(1).  The native file format is the B<.xz> format, but the legacy B<.lzma> format used by LZMA Utils and raw compressed streams with no container format headers are also supported.  In addition, decompression of the B<.lz> format used by B<lzip> is supported."
msgstr "B<xz> је алат опште употребе за запакивање података са синтаксом линије наредби сличан као B<gzip>(1)  и B<bzip2>(1).  Изворни формат датотеке је B<.xz> формат, али стари B<.lzma> формат који користи LZMA Utils и сирови запаковани токови без заглавља формата су такође подржани.  Уз то, распакивање B<.lz> формата који користи B<lzip> је подржано."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> compresses or decompresses each I<file> according to the selected operation mode.  If no I<files> are given or I<file> is B<->, B<xz> reads from standard input and writes the processed data to standard output.  B<xz> will refuse (display an error and skip the I<file>)  to write compressed data to standard output if it is a terminal.  Similarly, B<xz> will refuse to read compressed data from standard input if it is a terminal."
msgstr "B<xz> запакује или распакује сваку I<датотеку> у складу са изабраним режимом рада.  Ако није дата ни једна I<датотека> или је датотека I<file> B<->, B<xz> чита са стандардног улаза и записује обрађене податке на стандардни излаз.  B<xz> ће одбити (приказаће грешку и прескочити I<датотеку>) да пише запаковане податке на стандардни излаз ако је то терминал.  Слично, B<xz> ће одбити да чита запаковане податке са стандардног улаза ако је то терминал."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless B<--stdout> is specified, I<files> other than B<-> are written to a new file whose name is derived from the source I<file> name:"
msgstr "Осим ако није наведено B<--stdout>, I<датотеке> које нису B<-> се пишу у нову датотеку чији назив је проистекао из назива изворне I<датотеке>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "\\(bu"
msgstr "\\(bu"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, the suffix of the target file format (B<.xz> or B<.lzma>)  is appended to the source filename to get the target filename."
msgstr "Приликом запакивања, суфикс формата циљне датотеке (B<.xz> или B<.lzma>) се додаје на назив изворне датотеке да би се добио назив циљне датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, the B<.xz>, B<.lzma>, or B<.lz> suffix is removed from the filename to get the target filename.  B<xz> also recognizes the suffixes B<.txz> and B<.tlz>, and replaces them with the B<.tar> suffix."
msgstr "Приликом распакивања, суфикс B<.xz>, B<.lzma> или B<.lz> се уклања из назива датотеке да би се добио назив циљне датотеке.  B<xz> такође препознаје суфиксе B<.txz> и B<.tlz> и замењује их суфиксом B<.tar>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, an error is displayed and the I<file> is skipped."
msgstr "Ако циљна датотека већ пстоји, приказује се грешка а I<датотека> се прескаче."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Unless writing to standard output, B<xz> will display a warning and skip the I<file> if any of the following applies:"
msgstr "Осим ако не пише на стандардни излаз, B<xz> ће приказати упозорење и прескочити I<датотеку> ако се нешто од следећег примењује:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> is not a regular file.  Symbolic links are not followed, and thus they are not considered to be regular files."
msgstr "I<Датотека> није обична датотека.  Симболичке везе се не прате, и стога се не сматра да су обичне датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has more than one hard link."
msgstr "I<Датотека> има више од једне чврсте везе."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<File> has setuid, setgid, or sticky bit set."
msgstr "I<Датотека> има постављен „setuid, setgid“ или лепљиви бит."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to compress and the I<file> already has a suffix of the target file format (B<.xz> or B<.txz> when compressing to the B<.xz> format, and B<.lzma> or B<.tlz> when compressing to the B<.lzma> format)."
msgstr "Режим рада је постављен на запакивање а I<датотека> већ има суфикс формата циљне датотеке (B<.xz> или B<.txz> приликом запакивања у B<.xz> формат, а B<.lzma> или B<.tlz> приликом запакивања у B<.lzma> формат)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The operation mode is set to decompress and the I<file> doesn't have a suffix of any of the supported file formats (B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>)."
msgstr "Режим радње је постављен за распакивање а I<датотека> нема суфикс ни једног од подржаних формата датотеке (B<.xz>, B<.txz>, B<.lzma>, B<.tlz> или B<.lz>)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "After successfully compressing or decompressing the I<file>, B<xz> copies the owner, group, permissions, access time, and modification time from the source I<file> to the target file.  If copying the group fails, the permissions are modified so that the target file doesn't become accessible to users who didn't have permission to access the source I<file>.  B<xz> doesn't support copying other metadata like access control lists or extended attributes yet."
msgstr "Након успешног сажимања или распакивања I<датотеке>, B<xz> умножава власника, групу, дозволе, време приступа и време измене из изворне I<датотеке> у циљну датотеку.  Ако умножавање групе не успе, дозволе су измењене тако да циљна датотека не постане доступна корисницима који нису имали дозволу за приступ изворној I<датотеци>. B<xz> не подржава умножавање других метаподатака попут списка контрола приступа или проширених атрибута."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Once the target file has been successfully closed, the source I<file> is removed unless B<--keep> was specified.  The source I<file> is never removed if the output is written to standard output or if an error occurs."
msgstr "Једном када је циљна датотека успешно затворена, изворна I<датотека> се уклања осим ако се B<-keep> не наведе. Изворна I<датотека> се никада не уклања ако се излаз пише на стандардни излаз или ако дође до грешке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sending B<SIGINFO> or B<SIGUSR1> to the B<xz> process makes it print progress information to standard error.  This has only limited use since when standard error is a terminal, using B<--verbose> will display an automatically updating progress indicator."
msgstr "Слање B<SIGINFO> или B<SIGUSR1> ка B<xz> процесу чини да испише информације напредовања на стандардну грешку.  Ово има ограничено коришћење све док стандардна грешка јесте терминал, коришћење B<--verbose> приказаће аутоматски указивач напретка освежавања."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory usage"
msgstr "Коришћење меморије"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage of B<xz> varies from a few hundred kilobytes to several gigabytes depending on the compression settings.  The settings used when compressing a file determine the memory requirements of the decompressor.  Typically the decompressor needs 5\\ % to 20\\ % of the amount of memory that the compressor needed when creating the file.  For example, decompressing a file created with B<xz -9> currently requires 65\\ MiB of memory.  Still, it is possible to have B<.xz> files that require several gigabytes of memory to decompress."
msgstr "Коришћење меморије B<xz> се мења од неколико стотина килобајта до неколико гигабајта у зависности од поставки запакивања.  Поставке коришћене приликом запакивања датотеке одређују захтеве меморије распакивача.  Обично распакивачу треба 5\\ % до 20\\ % количине меморије која је потребна запакивачу приликом прављења датотеке.  На пример, распакивање датотеке направљене са B<xz -9> тренутно захтева 65\\ MiB меморије.  Још увек, могуће је имати B<.xz> датотеке које захтевају неколико гигабајта меморије за распакивање."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Especially users of older systems may find the possibility of very large memory usage annoying.  To prevent uncomfortable surprises, B<xz> has a built-in memory usage limiter, which is disabled by default.  While some operating systems provide ways to limit the memory usage of processes, relying on it wasn't deemed to be flexible enough (for example, using B<ulimit>(1)  to limit virtual memory tends to cripple B<mmap>(2))."
msgstr "Нарочито корисници старијих система могу наћи досадном могућност коришћења врло велике меморије.  Да би се спречила нежељена изненађења, B<xz> има уграђен ограничавач коришћења меморије, који је искључен по основи.  Док неки оперативни системи пружају начин за ограничавање коришћење меморије за процесе, ослањање на то сматра се да није довољно прилагодљиво (на пример, коришћење B<ulimit>(1) за ограничавање виртуелне меморије тежи да обогаљи B<mmap>(2))."

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS.
#.  It's a name of an environment variable.
#. type: Plain text
#: ../src/xz/xz.1
msgid "The memory usage limiter can be enabled with the command line option B<--memlimit=>I<limit>.  Often it is more convenient to enable the limiter by default by setting the environment variable B<XZ_DEFAULTS>, for example, B<XZ_DEFAULTS=--memlimit=150MiB>.  It is possible to set the limits separately for compression and decompression by using B<--memlimit-compress=>I<limit> and B<--memlimit-decompress=>I<limit>.  Using these two options outside B<XZ_DEFAULTS> is rarely useful because a single run of B<xz> cannot do both compression and decompression and B<--memlimit=>I<limit> (or B<-M> I<limit>)  is shorter to type on the command line."
msgstr "Ограничавач коришћења меморије се може укључити опцијом линије наредби B<--memlimit=>I<ограничење>.  Често је најпогодније укључити ограничавач по основи постављањем променљиве окружења B<XZ_DEFAULTS>, на пример, B<XZ_DEFAULTS=--memlimit=150MiB>.  Могуће је поставити ограничења засебно за запакивање и распакивање коришћењем B<--memlimit-compress=>I<ограничење> и B<--memlimit-decompress=>I<ограничење>.  Коришћење ове две опције ван B<XZ_DEFAULTS> је ретко корисно јер једно покретање B<xz> не може да ради и запакивање и распакивање а B<--memlimit=>I<ограничење> (или B<-M> I<ограничење>) је краће за куцање на линији наредби."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the specified memory usage limit is exceeded when decompressing, B<xz> will display an error and decompressing the file will fail.  If the limit is exceeded when compressing, B<xz> will try to scale the settings down so that the limit is no longer exceeded (except when using B<--format=raw> or B<--no-adjust>).  This way the operation won't fail unless the limit is very small.  The scaling of the settings is done in steps that don't match the compression level presets, for example, if the limit is only slightly less than the amount required for B<xz -9>, the settings will be scaled down only a little, not all the way down to B<xz -8>."
msgstr "Ако је прекорачено наведено ограничење коришћења меморије, B<xz> ће приказати грешку а распакивање датотеке неће успети.  Ако је ограничење прекорачено приликом запакивања, B<xz> ће покушати да умањи поставке тако да се ограничење више не прекорачује (оси када се користи B<--format=raw> или B<--no-adjust>).  На тај начин радња неће бити неуспешна осим ако је ограничење врло мало.  Смањење поставке се ради у корацима који се не подударају са предподешеностима нивоа запакивања, на пример, ако је ограничење само незнатно мање од износа потребног за B<xz -9>, поставке ће бити смањене само мало, а не све до B<xz -8>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Concatenation and padding with .xz files"
msgstr "Надовезивање и попуњавање са „.xz“ датотекама"

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to concatenate B<.xz> files as is.  B<xz> will decompress such files as if they were a single B<.xz> file."
msgstr "Могуће је додати B<.xz> датотеке као то.  B<xz> ће распаковати такве датотеке као да су биле једна B<.xz> датотека."

#. type: Plain text
#: ../src/xz/xz.1
msgid "It is possible to insert padding between the concatenated parts or after the last part.  The padding must consist of null bytes and the size of the padding must be a multiple of four bytes.  This can be useful, for example, if the B<.xz> file is stored on a medium that measures file sizes in 512-byte blocks."
msgstr "Могуће је уметнути попуњавање између придодатих делова или након последњег дела.  Попуњавање мора да се састоји од празних бајтова а величина попуњавања мора бити умножак четири бајта.  Ово може бити корисно, на пример, ако је B<.xz> датотека смештена на медијуму који мери величине датотека у блоковима од 512 бајтова."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Concatenation and padding are not allowed with B<.lzma> files or raw streams."
msgstr "Надовезивање и попуњавање нису дозвољени са B<.lzma> датотекама или сировим токовима."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "OPTIONS"
msgstr "ОПЦИЈЕ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Integer suffixes and special values"
msgstr "Суфикси целог броја и специјалне вредности"

#. type: Plain text
#: ../src/xz/xz.1
msgid "In most places where an integer argument is expected, an optional suffix is supported to easily indicate large integers.  There must be no space between the integer and the suffix."
msgstr "На већини места где се очекује аргумент целог броја, опционални суфикс је подржан да би се лако назначили велики цели бројеви. Не сме бити размака између целог броја и суфикса."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<KiB>"
msgstr "B<KiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, and B<KB> are accepted as synonyms for B<KiB>."
msgstr "Множи цео број са 1.024 (2^10).  B<Ki>, B<k>, B<kB>, B<K>, и B<KB> се прихватају као синоними за B<KiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<MiB>"
msgstr "B<MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,048,576 (2^20).  B<Mi>, B<m>, B<M>, and B<MB> are accepted as synonyms for B<MiB>."
msgstr "Множи цео број са 1.048.576 (2^20).  B<Mi>, B<m>, B<M>, и B<MB> се прихватају као синоними за B<MiB>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<GiB>"
msgstr "B<GiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Multiply the integer by 1,073,741,824 (2^30).  B<Gi>, B<g>, B<G>, and B<GB> are accepted as synonyms for B<GiB>."
msgstr "Множи цео број са 1.073.741.824 (2^30).  B<Gi>, B<g>, B<G>, и B<GB> се прихватају као синоними за B<GiB>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The special value B<max> can be used to indicate the maximum integer value supported by the option."
msgstr "Посебна вредност B<max> се може користити да укаже на највећу вредност целог броја подржану опцијом."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation mode"
msgstr "Режим рада"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple operation mode options are given, the last one takes effect."
msgstr "Ако је дато више опција режима рада, последња има дејства."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-z>, B<--compress>"
msgstr "B<-z>, B<--compress>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress.  This is the default operation mode when no operation mode option is specified and no other operation mode is implied from the command name (for example, B<unxz> implies B<--decompress>)."
msgstr "Запакује.  Ово је основни режим радње када није наведена опција режима радње и ниједан други режим радње се не подразумева са линије радње (на пример, B<unxz> подразумева B<--decompress>)."

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "After successful compression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Након успешног запакивања, изворна датотека се уклања осим ако се не пише на стандардни излаз или је наведено B<--keep>."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-d>, B<--decompress>, B<--uncompress>"
msgstr "B<-d>, B<--decompress>, B<--uncompress>"

#.  The DESCRIPTION section already says this but it's good to repeat it
#.  here because the default behavior is a bit dangerous and new users
#.  in a hurry may skip reading the DESCRIPTION section.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress.  After successful decompression, the source file is removed unless writing to standard output or B<--keep> was specified."
msgstr "Распакује.  Након успешног распакивања, изворна датотека се уклања осим ако се не пише на стандардни излаз или је наведено B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-t>, B<--test>"
msgstr "B<-t>, B<--test>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Test the integrity of compressed I<files>.  This option is equivalent to B<--decompress --stdout> except that the decompressed data is discarded instead of being written to standard output.  No files are created or removed."
msgstr "Тестира целовитост запакованих I<датотека>.  Ова опција је исто што и B<--decompress --stdout> осим тога што се распаковани подаци одбацују уместо да се пишу на стандардни излаз.  Датотеке се не праве нити уклањају."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-l>, B<--list>"
msgstr "B<-l>, B<--list>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print information about compressed I<files>.  No uncompressed output is produced, and no files are created or removed.  In list mode, the program cannot read the compressed data from standard input or from other unseekable sources."
msgstr "Исписује информације о запакованим I<датотекама>.  Никакав незапакован излаз се не добија, а ниједна датотека није направљена или уклоњена.  У режиму списка, програм не може читати запаковане податке са стандардног улаза или из других нетраживих извора."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default listing shows basic information about I<files>, one file per line.  To get more detailed information, use also the B<--verbose> option.  For even more information, use B<--verbose> twice, but note that this may be slow, because getting all the extra information requires many seeks.  The width of verbose output exceeds 80 characters, so piping the output to, for example, B<less\\ -S> may be convenient if the terminal isn't wide enough."
msgstr "Основни списак приказује основне информације о I<датотекама>, једна датотека у једном реду.  Да добијете подробније информације, користите такође опцију B<--verbose>.  За чак и више информација, користите B<--verbose> два пуа, али знајте да то може бити споро, јер да би се добиле све додатне информације потребно је много тражења.  Ширина опширног излаза премашује 80 знакова, тако да преспајање излаза на, на пример, B<less\\ -S> може бити прикладно ако терминал није довољно широк."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact output may vary between B<xz> versions and different locales.  For machine-readable output, B<--robot --list> should be used."
msgstr "Тачан излаз може варирати између B<xz> издања и различитих језика.  За излаз читљив машинама треба да користите B<--robot --list>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Operation modifiers"
msgstr "Измењивачи рада"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-k>, B<--keep>"
msgstr "B<-k>, B<--keep>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't delete the input files."
msgstr "Не брише улазне датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.2.6, this option also makes B<xz> compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file.  In earlier versions this was only done with B<--force>."
msgstr "Од B<xz> 5.2.6, ова опција такође чини да B<xz> запакује или распакује чак и ако је улаз симболичка веза ка обичној датотеци, има више од једне чврсте везе или има постављен „setuid“, „setgid“ или лепљиви бит.  „setuid“, „setgid“ и лепљиви битови се не умножавају у циљну датотеку.  У ранијим издањима ово се могло урадити само са B<--force>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-f>, B<--force>"
msgstr "B<-f>, B<--force>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has several effects:"
msgstr "Ова опција има неколико дејстава:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the target file already exists, delete it before compressing or decompressing."
msgstr "Ако циљна датотека већ постоји, брише је пре запакивања или распакивања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or decompress even if the input is a symbolic link to a regular file, has more than one hard link, or has the setuid, setgid, or sticky bit set.  The setuid, setgid, and sticky bits are not copied to the target file."
msgstr "Запакује или распакује чак и ако је улаз симболичка веза ка обичној датотеци, има више од једне чврсте везе или има постављен „setgid“, „setgid“ или лепљиви бит.  „setgid“, „setgid“ или лепљиви бит се не умножавају у циљну датотеку."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When used with B<--decompress> B<--stdout> and B<xz> cannot recognize the type of the source file, copy the source file as is to standard output.  This allows B<xzcat> B<--force> to be used like B<cat>(1)  for files that have not been compressed with B<xz>.  Note that in future, B<xz> might support new compressed file formats, which may make B<xz> decompress more types of files instead of copying them as is to standard output.  B<--format=>I<format> can be used to restrict B<xz> to decompress only a single file format."
msgstr "Када се користи са B<--decompress> B<--stdout> и B<xz> не може да препозна врсту изворне датотеке, умножава изворну датотеку као такву на стандардни излаз.  Ово омогућава да се B<xzcat> B<--force> користи као B<cat>(1) за датотекекоје нису запаковане са B<xz>.  Знајте да ће у будућности, B<xz> моћи да подржава нове формате запаковане датотеке, који могу учинити да B<xz> распакује још врста датотека уместо да их умножи какве јесу на стандардни излаз.  B<--format=>I<формат> се може користити да ограничи B<xz> да распакује само формат једне датотеке."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-c>, B<--stdout>, B<--to-stdout>"
msgstr "B<-c>, B<--stdout>, B<--to-stdout>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Write the compressed or decompressed data to standard output instead of a file.  This implies B<--keep>."
msgstr "Пише запаковане или распаковане податке на стандардни излаз уместо у датотеку,  Ово подразумева B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--single-stream>"
msgstr "B<--single-stream>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress only the first B<.xz> stream, and silently ignore possible remaining input data following the stream.  Normally such trailing garbage makes B<xz> display an error."
msgstr "Распакује само први B<.xz> ток, и тихо занемарује могуће преостале улазне податке који следе ток.  Нормално такво пратеће смеће чини да B<xz> прикаже грешку."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> never decompresses more than one stream from B<.lzma> files or raw streams, but this option still makes B<xz> ignore the possible trailing data after the B<.lzma> file or raw stream."
msgstr "B<xz> никада не распакује више од једног тока из B<.lzma> датотека или сирових токова, али ова опција чини да B<xz> још увек занемари могуће пратеће податке након B<.lzma> датотеке или сировог тока."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has no effect if the operation mode is not B<--decompress> or B<--test>."
msgstr "Ова опција нема дејства ако режим рада није B<--decompress> или B<--test>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.7.1alpha, B<--single-stream> implies B<--keep>."
msgstr "Од B<xz> 5.7.1alpha, B<--single-stream> подразумева B<--keep>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sparse>"
msgstr "B<--no-sparse>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Disable creation of sparse files.  By default, if decompressing into a regular file, B<xz> tries to make the file sparse if the decompressed data contains long sequences of binary zeros.  It also works when writing to standard output as long as standard output is connected to a regular file and certain additional conditions are met to make it safe.  Creating sparse files may save disk space and speed up the decompression by reducing the amount of disk I/O."
msgstr "Искључује стварање развучених датотека. По основи, ако распакује у обичну датотеку, B<xz> покушава да учини датотеку развученом ако распаковани подаци садрже дуге низове бинарних нула.  Такође ради приликом писања на стандардни излаз све док је стандардни излаз повезан са обичном датотеком а одређени додатни услови су испуњени да је учине безбедном.  Стварање развучених датотека може уштедети простор на диску и убрзати распакивање смањењем количине У/И диска."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-S> I<.suf>, B<--suffix=>I<.suf>"
msgstr "B<-S> I<.suf>, B<--suffix=>I<.suf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, use I<.suf> as the suffix for the target file instead of B<.xz> or B<.lzma>.  If not writing to standard output and the source file already has the suffix I<.suf>, a warning is displayed and the file is skipped."
msgstr "Приликом запакивања, користите I<.suf> као суфикс за циљну датотеку уместо B<.xz> или B<.lzma>.  Ако се не пише на стандардни излаз а изворна датотека већ има суфикс I<.suf>, приказује се упозорење и датотека се прескаче."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, recognize files with the suffix I<.suf> in addition to files with the B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz> suffix.  If the source file has the suffix I<.suf>, the suffix is removed to get the target filename."
msgstr "Приликом распакивања, препознаје датотеке са суфиксом I<.suf> поред датотека са суфиксом B<.xz>, B<.txz>, B<.lzma>, B<.tlz>, or B<.lz>.  Ако изворна датотека има суфикс I<.suf>, суфикс се уклања да би се добио назив циљне датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing or decompressing raw streams (B<--format=raw>), the suffix must always be specified unless writing to standard output, because there is no default suffix for raw streams."
msgstr "Приликом запакивања или распакивања сирових токова (B<--format=raw>), суфикс мора увек бити наведен осим ако се не пише на стандардни излаз, јер не постоји основни суфикс за сирове токове."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files>[B<=>I<file>]"
msgstr "B<--files>[B<=>I<датотека>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Read the filenames to process from I<file>; if I<file> is omitted, filenames are read from standard input.  Filenames must be terminated with the newline character.  A dash (B<->)  is taken as a regular filename; it doesn't mean standard input.  If filenames are given also as command line arguments, they are processed before the filenames read from I<file>."
msgstr "Чита називе датотека за обраду из I<датотеке>; ако се I<датотека> изостави, називи датотека се читају са стандардног улаза. Називи датотека се морају завршавати знаком новог реда. Цртица (B<->) се узима као редован назив датотеке; а не стандардни улаз.  Ако су називи датотека дати такође као аргументи линије наредби, обрађују се пре него што се називи датотека прочитају из I<датотеке>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--files0>[B<=>I<file>]"
msgstr "B<--files0>[B<=>I<датотека>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is identical to B<--files>[B<=>I<file>] except that each filename must be terminated with the null character."
msgstr "Ово је исто као и B<--files>[B<=>I<датотека>] изузев тога што сваки назив датотеке мора да се завршава „null“ знаком."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basic file format and compression options"
msgstr "Основни формат датотеке и опције запакивања"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-F> I<format>, B<--format=>I<format>"
msgstr "B<-F> I<формат>, B<--format=>I<формат>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the file I<format> to compress or decompress:"
msgstr "Наводи I<формат> датотеке за запакивање или распакивање:"

#.  TRANSLATORS: Don't translate bold string B<auto>.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<auto>"
msgstr "B<auto>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is the default.  When compressing, B<auto> is equivalent to B<xz>.  When decompressing, the format of the input file is automatically detected.  Note that raw streams (created with B<--format=raw>)  cannot be auto-detected."
msgstr "Ово је основно.  Приликом запакивања, B<auto> је исто што и B<xz>.  Приликом распакивања, формат улазне датотеке се аутоматски препознаје.  Знајте да сирови токови (направљени са B<--format=raw>) не могу бити аутоматски препознати."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<xz>"
msgstr "B<xz>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the B<.xz> file format, or accept only B<.xz> files when decompressing."
msgstr "Пакује у B<.xz> формат датотеке, или прихвата само B<.xz> датотеке приликом распакивања."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzma>, B<alone>"
msgstr "B<lzma>, B<alone>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress to the legacy B<.lzma> file format, or accept only B<.lzma> files when decompressing.  The alternative name B<alone> is provided for backwards compatibility with LZMA Utils."
msgstr "Пакује у стари B<.lzma> формат датотеке, или прихвата само B<.lzma> датотеке приликом распакивања.  Алтернативни назив B<alone> се доставља зарад повратне сагласности са LZMA помагалима."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lzip>"
msgstr "B<lzip>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Accept only B<.lz> files when decompressing.  Compression is not supported."
msgstr "Прихвата само B<.lz> датотеке приликом распакивања.  Запакивање није подржано."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lz> format version 0 and the unextended version 1 are supported.  Version 0 files were produced by B<lzip> 1.3 and older.  Such files aren't common but may be found from file archives as a few source packages were released in this format.  People might have old personal files in this format too.  Decompression support for the format version 0 was removed in B<lzip> 1.18."
msgstr "Формат B<.lz> издање 0 и непроширено издање 1 су подржани.  Датотеке издања 0 су настале са B<lzip> 1.3 и старијим.  Такве датотеке нису уобичајене али се могу наћи у архивама датотека јер је неколико пакета извора издато у овом формату.  Такође можда неко има старе личне датотеке у овом формату.  Подршка распакивања за формат издања 0 је уклоњена у B<lzip> 1.18."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<lzip> 1.4 and later create files in the format version 1.  The sync flush marker extension to the format version 1 was added in B<lzip> 1.6.  This extension is rarely used and isn't supported by B<xz> (diagnosed as corrupt input)."
msgstr "B<lzip> 1.4 и новији праве датотеке у формату издања 1.  Проширење означавача испирања усклађивања за формат издања 1 додато је у B<lzip> 1.6.  Ово проширење се ретко користи и не подржава га B<xz> (препознаје се као оштећени улаз)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<raw>"
msgstr "B<raw>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress or uncompress a raw stream (no headers).  This is meant for advanced users only.  To decode raw streams, you need use B<--format=raw> and explicitly specify the filter chain, which normally would have been stored in the container headers."
msgstr "Запакује или распакује сирови ток (без заглавља).  Ово је замишљено само за напредне кориснике.  Да дешифрујете сирове токове, треба да користите B<--format=raw> и да изричито наведете ланац филтера, што би обично требало да буде смештено у заглављима контејнера."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-C> I<check>, B<--check=>I<check>"
msgstr "B<-C> I<провера>, B<--check=>I<провера>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the type of the integrity check.  The check is calculated from the uncompressed data and stored in the B<.xz> file.  This option has an effect only when compressing into the B<.xz> format; the B<.lzma> format doesn't support integrity checks.  The integrity check (if any) is verified when the B<.xz> file is decompressed."
msgstr "Наводи врсту провере целовитости.  Провера се израчунава из незапакованих података и смештених у B<.xz> датотеци.  Ова опција има дејства само приликом запакивања у B<.xz> формат; B<.lzma> формат не подржава провере целовитости.  Провера целовитости (ако је има) се проверава када је B<.xz> датотека распакована."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<check> types:"
msgstr "Подржане врсте I<провере>:"

#.  TRANSLATORS: Don't translate the bold strings B<none>, B<crc32>,
#.  B<crc64>, and B<sha256>. The command line option --check accepts
#.  only the untranslated strings.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<none>"
msgstr "B<none>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't calculate an integrity check at all.  This is usually a bad idea.  This can be useful when integrity of the data is verified by other means anyway."
msgstr "Уопште не израчунава проверу целовитости.  Ово је обично лоша идеја.  Ово може бити корисно када се целовитост података ипак проверава на друге начине."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc32>"
msgstr "B<crc32>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC32 using the polynomial from IEEE-802.3 (Ethernet)."
msgstr "Израчунава CRC32 користећи полином из IEEE-802.3 (Етернет)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<crc64>"
msgstr "B<crc64>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate CRC64 using the polynomial from ECMA-182.  This is the default, since it is slightly better than CRC32 at detecting damaged files and the speed difference is negligible."
msgstr "Израчунава CRC64 користећи полином из ECMA-182.  Ово је основно, јер је незнатно боље од CRC32 у откривању оштећених датотека а разлика брзине је занемарљива."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<sha256>"
msgstr "B<sha256>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate SHA-256.  This is somewhat slower than CRC32 and CRC64."
msgstr "Израчунава SHA-256.  Ово је нешто спорије од CRC32 и CRC64."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Integrity of the B<.xz> headers is always verified with CRC32.  It is not possible to change or disable it."
msgstr "Целовитост B<.xz> заглавља се увек проверава са CRC32.  Не може се изменити нити искључити."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ignore-check>"
msgstr "B<--ignore-check>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't verify the integrity check of the compressed data when decompressing.  The CRC32 values in the B<.xz> headers will still be verified normally."
msgstr "Не проверава проверу целовитости запакованих података приликом распакивања.  CRC32 вредности у B<.xz> заглављима биће још увек нормално проверене."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Do not use this option unless you know what you are doing.> Possible reasons to use this option:"
msgstr "B<Немојте користити ову опцију осим ако стварно знате шта радите.> Могући разлози за коришћење ове опције:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Trying to recover data from a corrupt .xz file."
msgstr "Покушава да опорави податке из оштећене „.xz“ датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Speeding up decompression.  This matters mostly with SHA-256 or with files that have compressed extremely well.  It's recommended to not use this option for this purpose unless the file integrity is verified externally in some other way."
msgstr "Убрзава распакивање.  Ово има значаја углавном са SHA-256 или са датотекама које су запаковане стварно добро.  Препоручује се да не користите ову опцију за ову сврху осим ако је целовитост датотеке проверена споља на неки други начин."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-9>"
msgstr "B<-0> ... B<-9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Select a compression preset level.  The default is B<-6>.  If multiple preset levels are specified, the last one takes effect.  If a custom filter chain was already specified, setting a compression preset level clears the custom filter chain."
msgstr "Бира ниво преподешености запакивања.  Подразумевано је B<-6>. Ако је наведено више нивоа преподешености, последња има дејства.  Ако је произвољни ланац филтера већ наведен, постављање нивоа преподешености запакивања уклања произвољни ланац филтера."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The differences between the presets are more significant than with B<gzip>(1)  and B<bzip2>(1).  The selected compression settings determine the memory requirements of the decompressor, thus using a too high preset level might make it painful to decompress the file on an old system with little RAM.  Specifically, B<it's not a good idea to blindly use -9 for everything> like it often is with B<gzip>(1)  and B<bzip2>(1)."
msgstr "Разлике између предподешености су још значајније него са B<gzip>(1) и B<bzip2>(1).  Изабране поставке запакивања одређују захтеве меморије распакивача, стога коришћење превисоког нивоа предподешености може отежати распакивање датотеле на старом систему са мало RAM-а.  Нарочито, B<није добра замисао слепо користити -9 за било шта> као што је често са B<gzip>(1) и B<bzip2>(1)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-0> ... B<-3>"
msgstr "B<-0> ... B<-3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat fast presets.  B<-0> is sometimes faster than B<gzip -9> while compressing much better.  The higher ones often have speed comparable to B<bzip2>(1)  with comparable or better compression ratio, although the results depend a lot on the type of data being compressed."
msgstr "Ово су некако брзе предподешености.  B<-0> је понекад брже од B<gzip -9> док запакује много боље.  Оне више често имају брзину упоредиву са B<bzip2>(1) са упоредивом или бољом стопом запакивања, иако резултати зависе много од врсте података који се запакују."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-4> ... B<-6>"
msgstr "B<-4> ... B<-6>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Good to very good compression while keeping decompressor memory usage reasonable even for old systems.  B<-6> is the default, which is usually a good choice for distributing files that need to be decompressible even on systems with only 16\\ MiB RAM.  (B<-5e> or B<-6e> may be worth considering too.  See B<--extreme>.)"
msgstr "Добро до врло добро запакивање које одржава коришћење меморије распакивача разумним чак и за старе системе.  B<-6> је основно, што је обично добар избор за расподелу датотека које треба да могу да се распакују чак и на системима са само 16\\ MiB RAM-а.  (B<-5e> или B<-6e> може бити вредно узимања у обзир.  Видите B<--extreme>.)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-7 ... -9>"
msgstr "B<-7 ... -9>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are like B<-6> but with higher compressor and decompressor memory requirements.  These are useful only when compressing files bigger than 8\\ MiB, 16\\ MiB, and 32\\ MiB, respectively."
msgstr "Као B<-6> али са већим захтевима меморије запакивача и распакивача.  Корисне су само приликом запакивања датотека већих од 8\\ MiB, 16\\ MiB и 32\\ MiB."

#. type: Plain text
#: ../src/xz/xz.1
msgid "On the same hardware, the decompression speed is approximately a constant number of bytes of compressed data per second.  In other words, the better the compression, the faster the decompression will usually be.  This also means that the amount of uncompressed output produced per second can vary a lot."
msgstr "На истом хардверу, брзина распакивања је приближно сталан број бајтова запакованих података у секунди.  Другим речима, биће боље запакивање, брже распакивање.  То такође значи да количина незапакованог излаза произведеног у секунди може много да се разликује."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following table summarises the features of the presets:"
msgstr "Следећа табела резимира функције предподешености:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Preset"
msgstr "Преподешеност"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DictSize"
msgstr "ВлчРчнка"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompCPU"
msgstr "ЗапакЦПЈ"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "CompMem"
msgstr "ЗапакМем"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "DecMem"
msgstr "РаспМем"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0"
msgstr "-0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "256 KiB"
msgstr "256 KiB"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "0"
msgstr "0"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3 MiB"
msgstr "3 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "1 MiB"
msgstr "1 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1"
msgstr "-1"

#. type: TP
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "1"
msgstr "1"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "9 MiB"
msgstr "9 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2 MiB"
msgstr "2 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2"
msgstr "-2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "2"
msgstr "2"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "17 MiB"
msgstr "17 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3"
msgstr "-3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4 MiB"
msgstr "4 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "3"
msgstr "3"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32 MiB"
msgstr "32 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5 MiB"
msgstr "5 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4"
msgstr "-4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4"
msgstr "4"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "48 MiB"
msgstr "48 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5"
msgstr "-5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8 MiB"
msgstr "8 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "5"
msgstr "5"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "94 MiB"
msgstr "94 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6"
msgstr "-6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "6"
msgstr "6"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7"
msgstr "-7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16 MiB"
msgstr "16 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "186 MiB"
msgstr "186 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8"
msgstr "-8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "370 MiB"
msgstr "370 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "33 MiB"
msgstr "33 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9"
msgstr "-9"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 MiB"
msgstr "64 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "674 MiB"
msgstr "674 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "65 MiB"
msgstr "65 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Column descriptions:"
msgstr "Описи колона:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "DictSize is the LZMA2 dictionary size.  It is waste of memory to use a dictionary bigger than the size of the uncompressed file.  This is why it is good to avoid using the presets B<-7> ... B<-9> when there's no real need for them.  At B<-6> and lower, the amount of memory wasted is usually low enough to not matter."
msgstr "ВлчРчнка је величина LZMA2 речника.  Коришћење речника већег од величине незапаковане датотеке је губитак меморије.  Зато је добро избегавати коришћење предподешености B<-7> ... B<-9> када за њима нема стварне потребе.  Са B<-6> и нижим, количина изгубљене меморије је обично довољно ниска да нема значаја."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompCPU is a simplified representation of the LZMA2 settings that affect compression speed.  The dictionary size affects speed too, so while CompCPU is the same for levels B<-6> ... B<-9>, higher levels still tend to be a little slower.  To get even slower and thus possibly better compression, see B<--extreme>."
msgstr "ЗапакЦПЈ је поједностављено представљање LZMA2 поставки које утичу на брзину запакивања.  Величина речника такође утиче на брзину, тако док је ЗапакЦПЈ исто за нивое B<-6> ... B<-9>, виши нивои још увек теже да буду спорији.  Да добијете још спорије и самим тим можда боље запакивање, видите B<--extreme>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "CompMem contains the compressor memory requirements in the single-threaded mode.  It may vary slightly between B<xz> versions."
msgstr "ЗапакМем садржи захтеве меморије запакивача у режиму једне нити.  Може незнатно да се разликује између B<xz> издања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "DecMem contains the decompressor memory requirements.  That is, the compression settings determine the memory requirements of the decompressor.  The exact decompressor memory usage is slightly more than the LZMA2 dictionary size, but the values in the table have been rounded up to the next full MiB."
msgstr "РаспМем садржи захтеве меморије распакивача.  Тако је, поставке запакивања одређују захтеве меморије распакивача.  Тачно коришћење меморије распакивача је незнатно веће од величине LZMA2 речника, али су вредности у табели заокружене на следећи цео MiB."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory requirements of the multi-threaded mode are significantly higher than that of the single-threaded mode.  With the default value of B<--block-size>, each thread needs 3*3*DictSize plus CompMem or DecMem.  For example, four threads with preset B<-6> needs 660\\(en670\\ MiB of memory."
msgstr "Захтеви меморије режима са више нити су значајно већи него ли режима једне нити.  Са основном вредношћу B<--block-size>, свакој нити треба 3*3*ВлчРчнка плус ЗапакМем или РаспМем.  На пример, за четири нити са предподешавањем B<-6> потребно је 660\\(en670\\ MiB меморије."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-e>, B<--extreme>"
msgstr "B<-e>, B<--extreme>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Use a slower variant of the selected compression preset level (B<-0> ... B<-9>)  to hopefully get a little bit better compression ratio, but with bad luck this can also make it worse.  Decompressor memory usage is not affected, but compressor memory usage increases a little at preset levels B<-0> ... B<-3>."
msgstr "Користите спорију варијанту изабраног нивоа предподешености запакивања (B<-0> ... B<-9>) у нади да ћете добити мало бољу стопу запакивања, али уз лошу срећу ово је може учинити гором.  Не утиче на коришћење меморије распакивача, али се коришћење меморије запакивача мало повећава на нивоима предподешености B<-0> ... B<-3>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since there are two presets with dictionary sizes 4\\ MiB and 8\\ MiB, the presets B<-3e> and B<-5e> use slightly faster settings (lower CompCPU) than B<-4e> and B<-6e>, respectively.  That way no two presets are identical."
msgstr "Како постоје две предподешености са величинама речника од 4\\ MiB и 8\\ MiB, предподешености B<-3e> и B<-5e> користе незнатно брже поставке (ниже CompCPU) него B<-4e> и B<-6e>.  На тај начин нема две истоветне предподешености."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-0e"
msgstr "-0e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "8"
msgstr "8"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-1e"
msgstr "-1e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "13 MiB"
msgstr "13 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-2e"
msgstr "-2e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "25 MiB"
msgstr "25 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-3e"
msgstr "-3e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "7"
msgstr "7"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-4e"
msgstr "-4e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-5e"
msgstr "-5e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-6e"
msgstr "-6e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-7e"
msgstr "-7e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-8e"
msgstr "-8e"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "-9e"
msgstr "-9e"

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, there are a total of four presets that use 8\\ MiB dictionary, whose order from the fastest to the slowest is B<-5>, B<-6>, B<-5e>, and B<-6e>."
msgstr "На пример, има укупно четири предподешавања која користе 8\\ MiB речник, чији поредак од најбржег до најспоријег је B<-5>, B<-6>, B<-5e> и B<-6e>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--fast>"
msgstr "B<--fast>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--best>"
msgstr "B<--best>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "These are somewhat misleading aliases for B<-0> and B<-9>, respectively.  These are provided only for backwards compatibility with LZMA Utils.  Avoid using these options."
msgstr "Ово су помало погрешни алијаси за B<-0> и B<-9>.  Достављени су само зарад повратне сагласности са LZMA Utils-ом.  Избегавајте коришћење ових опција."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-size=>I<size>"
msgstr "B<--block-size=>I<величина>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, split the input data into blocks of I<size> bytes.  The blocks are compressed independently from each other, which helps with multi-threading and makes limited random-access decompression possible.  This option is typically used to override the default block size in multi-threaded mode, but this option can be used in single-threaded mode too."
msgstr "Приликом запакивања у B<.xz> формат, дели улазне податке на блокове I<величина> бајта.  Блокови се запакују независно један од другог, што помаже у раду са више нити и чини ограничено распакивање насумичног приступа могућим.  Ова опција се обично користи да се препише основна величина блока у режиму са више нити, али се ова опција може такође користити и у режиму једне нити."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode about three times I<size> bytes will be allocated in each thread for buffering input and output.  The default I<size> is three times the LZMA2 dictionary size or 1 MiB, whichever is more.  Typically a good value is 2\\(en4 times the size of the LZMA2 dictionary or at least 1 MiB.  Using I<size> less than the LZMA2 dictionary size is waste of RAM because then the LZMA2 dictionary buffer will never get fully used.  In multi-threaded mode, the sizes of the blocks are stored in the block headers.  This size information is required for multi-threaded decompression."
msgstr "У режиму више нити око три пута I<величина> бајтова биће додељено у свакој нити за међумеморисање улаза и излаза.  Основна I<величина> је три пута величине LZMA2 речника или 1 MiB, шта год да је више.  Обично добра вредност је 2\\(en4 пута величина LZMA2 речника или барем 1 MiB.  Коришћење I<величине> мање од величине LZMA2 речника је трошење RAM-а јер тада међумеморија LZMA2 речника никада неће бити потпуно коришћена.  У режиму више нити, величине блокова се чувају у заглављима блока.  Ова информација величине је потребна за распакивање са више нити."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In single-threaded mode no block splitting is done by default.  Setting this option doesn't affect memory usage.  No size information is stored in block headers, thus files created in single-threaded mode won't be identical to files created in multi-threaded mode.  The lack of size information also means that B<xz> won't be able decompress the files in multi-threaded mode."
msgstr "У режиму једне нити никаква подела блока се не ради по основи.  Постављање ове опције не утиче на коришћење меморије.  Никакве информације о величини се не чувају у заглављима блока, стога датотеке направљене у режиму једне нити неће бити исте као датотеке направљене у режиму више нити.  Недостатак информација о величини такође значи да B<xz> неће моћи да распакује датотеке у режиму више нити."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--block-list=>I<items>"
msgstr "B<--block-list=>I<ставке>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing to the B<.xz> format, start a new block with an optional custom filter chain after the given intervals of uncompressed data."
msgstr "Приликом запакивања у B<.xz> формат, почиње нови блок са изборним произвољним ланцем филтера након датих интервала незапакованих података."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<items> are a comma-separated list.  Each item consists of an optional filter chain number between 0 and 9 followed by a colon (B<:>)  and a required size of uncompressed data.  Omitting an item (two or more consecutive commas) is a shorthand to use the size and filters of the previous item."
msgstr "I<Ставке> су списак одвојен зарезом.  Свака ставка се састоји од изборног броја ланца филтера између 0 и 9 за којим следи двотачка (B<:>) и захтевана величина незапакованих података .  Изостављање неке ставке (два или више узастопна зареза) је пречица за коришћење величине и филтера претходне ставке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the input file is bigger than the sum of the sizes in I<items>, the last item is repeated until the end of the file.  A special value of B<0> may be used as the last size to indicate that the rest of the file should be encoded as a single block."
msgstr "Ако је улазна датотека већа од збира величина у I<ставкама>, последња ставка се понавља све до краја датотеке.  Специјална вредност B<0> може се користити као последња величина да назначи да остатак датотеке треба да буде шифрован као један блок."

#. type: Plain text
#: ../src/xz/xz.1
msgid "An alternative filter chain for each block can be specified in combination with the B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options.  These options define filter chains with an identifier between 1\\(en9.  Filter chain 0 can be used to refer to the default filter chain, which is the same as not specifying a filter chain.  The filter chain identifier can be used before the uncompressed size, followed by a colon (B<:>).  For example, if one specifies B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> then blocks will be created using:"
msgstr "Алтернативни ланац филтера за сваки блок се може навести у комбинацији са опцијама B<--filters1=>I<филтера> \\&...\\& B<--filters9=>I<филтери>.  Ове опције дефинишу ланце филтера са одредником између 1\\(en9.  Ланац филтера 0 се може користити да упућује на основно ланац филтера, који је исти јер се ланац филтера не наводи.  Одредник ланца филтера се може користити пре незапаковане величине, за којим следи двотачка (B<:>).  На пример, ако наведемо B<--block-list=1:2MiB,3:2MiB,2:4MiB,,2MiB,0:4MiB> тада ће се за прављење блокова користити:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters1> and 2 MiB input"
msgstr "Ланац филтера наведен са B<--filters1> и 2 MiB улазом"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters3> and 2 MiB input"
msgstr "Ланац филтера наведен са B<--filters3> и 2 MiB улазом"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The filter chain specified by B<--filters2> and 4 MiB input"
msgstr "Ланац филтера наведен са B<--filters2> и 4 MiB улазом"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 2 MiB input"
msgstr "Основни ланац филтера и 2 MiB улаз"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default filter chain and 4 MiB input for every block until end of input."
msgstr "Основни ланац филтера и 4 MiB улаз за сваки блок до краја улаза."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If one specifies a size that exceeds the encoder's block size (either the default value in threaded mode or the value specified with B<--block-size=>I<size>), the encoder will create additional blocks while keeping the boundaries specified in I<items>.  For example, if one specifies B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> and the input file is 80 MiB, one will get 11 blocks: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, and 1 MiB."
msgstr "Ако наведемо величину која премашује величину блока шифрера (било основну вредност у режиму нити или вредност наведену са B<--block-size=>I<величина>), шифрер ће направити додатне блокове док ће задржати границе наведене у I<ставкама>.  На пример, ако наведемо B<--block-size=10MiB> B<--block-list=5MiB,10MiB,8MiB,12MiB,24MiB> а улазна датотека је 80 MiB, добићемо 11 блокова: 5, 10, 8, 10, 2, 10, 10, 4, 10, 10 и 1 MiB."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In multi-threaded mode the sizes of the blocks are stored in the block headers.  This isn't done in single-threaded mode, so the encoded output won't be identical to that of the multi-threaded mode."
msgstr "У режиму са више нити величине блокова се чувају у заглављима блока.  Ово се не ради у режиму једне нити, тако да шифровани излаз неће бити истоветан ономе у режиму са више нити."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--flush-timeout=>I<timeout>"
msgstr "B<--flush-timeout=>I<истек_времена>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing, if more than I<timeout> milliseconds (a positive integer) has passed since the previous flush and reading more input would block, all the pending input data is flushed from the encoder and made available in the output stream.  This can be useful if B<xz> is used to compress data that is streamed over a network.  Small I<timeout> values make the data available at the receiving end with a small delay, but large I<timeout> values give better compression ratio."
msgstr "Приликом запакивања, ако је више од I<време_истека> милисекунди (позитиван цео број) прошло од претходног испирања и читање више улаза ће блокирати, сви заказани улазни подаци се испирају из шифрера и чине доступним у излазном току.  Ово може бити корисно ако је B<xz> коришћен за запакивање података који су слати преко мреже.  Мала вредност I<временског_истека> чини податке доступним на пријемни крај са малим закашњењем, али велика вредност I<времена_истека> даје бољу стопу запакивања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is disabled by default.  If this option is specified more than once, the last one takes effect.  The special I<timeout> value of B<0> can be used to explicitly disable this feature."
msgstr "Ова функција је искључена по основи.  Ако је ова опција наведена више пута, последња има дејства.  Нарочита вредност I<временског истека> B<0> може се користити за изричито искључивање ове функције."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This feature is not available on non-POSIX systems."
msgstr "Ова функција је доступна само на POSIX системима."

#.  FIXME
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<This feature is still experimental.> Currently B<xz> is unsuitable for decompressing the stream in real time due to how B<xz> does buffering."
msgstr "B<Ова функција је још увек експериментална.> Тренутно B<xz> није погодан за распакивање тока у реалном времену због начина на који B<xz> ради међумеморисање."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-sync>"
msgstr "B<--no-sync>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Do not synchronize the target file and its directory to the storage device before removing the source file.  This can improve performance if compressing or decompressing many small files.  However, if the system crashes soon after the deletion, it is possible that the target file was not written to the storage device but the delete operation was.  In that case neither the original source file nor the target file is available."
msgstr "Не усклађује циљну датотеку и њену фасциклу на смештајном уређају пре уклањања изворне датотеке.  Ово може побољшати учинковитост ако се запакује или распакује много малих датотека.  Међутим, ако се систем уруши одмах након брисања, може бити да циљна датотека не буде уписана на смештајном уређају али радња брисања јесте.  У том случају неће бити доступна ни оригинална изворна ни циљна датотека."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option has an effect only when B<xz> is going to remove the source file.  In other cases synchronization is never done."
msgstr "Ова опција има дејство само када се B<xz> спрема да уклони изворну датотеку.  У осталим случајевима усклађивање се не ради никада."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The synchronization and B<--no-sync> were added in B<xz> 5.7.1alpha."
msgstr "Синхронизација и B<--no-sync> су додате у B<xz> 5.7.1alpha."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-compress=>I<limit>"
msgstr "B<--memlimit-compress=>I<ограничење>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for compression.  If this option is specified multiple times, the last one takes effect."
msgstr "Поставља ограничење коришћења меморије запакивања.  Ако је ова опција наведена више пута, последња ступа у дејство."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the compression settings exceed the I<limit>, B<xz> will attempt to adjust the settings downwards so that the limit is no longer exceeded and display a notice that automatic adjustment was done.  The adjustments are done in this order: reducing the number of threads, switching to single-threaded mode if even one thread in multi-threaded mode exceeds the I<limit>, and finally reducing the LZMA2 dictionary size."
msgstr "Ако поставке запакивања премаше I<ограничење>, B<xz> ће покушати да врати назад поставке тако да ограничење више није прекорачено и приказаће обавештење да је урађено аутоматско дотеривање.  Дотеривања се раде следећим редом: смањење броја нити, пребацивање на режим једне нити ако чак и једна нит у режиму више нити премаши I<ограничење> и на крају смањење величине LZMA2 речника."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When compressing with B<--format=raw> or if B<--no-adjust> has been specified, only the number of threads may be reduced since it can be done without affecting the compressed output."
msgstr "Приликом запакивања са B<--format=raw> или ако је наведено B<--no-adjust>, само број нити може бити умањен јер може да се уради а да се не делује на излаз запакованог."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If the I<limit> cannot be met even with the adjustments described above, an error is displayed and B<xz> will exit with exit status 1."
msgstr "Ако I<ограничење> не може бити задовољено чак и са поравнањима описаним изнад, приказује се грешка и B<xz> ће изаћи са излазним стањем 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified in multiple ways:"
msgstr "I<Ограничење> се може навести на више начина:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be an absolute value in bytes.  Using an integer suffix like B<MiB> can be useful.  Example: B<--memlimit-compress=80MiB>"
msgstr "I<Ограничење> може бити апсолутна величина у бајтима.  Коришћење суфикса целог броја као B<MiB> може бити корисно.  Пример: B<--memlimit-compress=80MiB>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be specified as a percentage of total physical memory (RAM).  This can be useful especially when setting the B<XZ_DEFAULTS> environment variable in a shell initialization script that is shared between different computers.  That way the limit is automatically bigger on systems with more memory.  Example: B<--memlimit-compress=70%>"
msgstr "I<Ограничење> се може навести као проценат укупне физичке меморије (RAM).  Ово може бити корисно нарочито приликом постављања променљиве окружења B<XZ_DEFAULTS> у скрпти покретања конзоле која се дели између различитих рачунара.  На тај начин ограничење је аутоматски веће на системима са више меморије.  Пример: B<--memlimit-compress=70%>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The I<limit> can be reset back to its default value by setting it to B<0>.  This is currently equivalent to setting the I<limit> to B<max> (no memory usage limit)."
msgstr "I<Ограничење> се може вратити на своју основну вредност његовим постављањем на B<0>.  Ово је тренутно исто што и постављање I<ограничења> на B<max> (без ограничења коришћења меморије)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For 32-bit B<xz> there is a special case: if the I<limit> would be over B<4020\\ MiB>, the I<limit> is set to B<4020\\ MiB>.  On MIPS32 B<2000\\ MiB> is used instead.  (The values B<0> and B<max> aren't affected by this.  A similar feature doesn't exist for decompression.)  This can be helpful when a 32-bit executable has access to 4\\ GiB address space (2 GiB on MIPS32)  while hopefully doing no harm in other situations."
msgstr "За B<xz> 32-бита имамо посебан случај: ако I<ограничење> треба да буде преко B<4020\\ MiB>, I<ограничење> се поставља на B<4020\\ MiB>.  Али на MIPS32 користи се B<2000\\ MiB>.  (На вредности B<0> и B<max> ово не утиче.  Слична функција не постоји за распакивање.)  Ово може бити од помоћи када извршна од 32-бита има приступ адресном простору од 4\\ GiB (2 GiB на MIPS32) док срећом не чини ништа лоше у осталим приликама."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See also the section B<Memory usage>."
msgstr "Видите такође одељак B<Коришћење меморије>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-decompress=>I<limit>"
msgstr "B<--memlimit-decompress=>I<ограничење>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression.  This also affects the B<--list> mode.  If the operation is not possible without exceeding the I<limit>, B<xz> will display an error and decompressing the file will fail.  See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>."
msgstr "Поставља ограничење коришћења меморије за распакивање.  Ово такође утиче на B<--list> режим.  Ако радња није могућа а да се не премаши I<ограничење>, B<xz> ће приказати грешку а распакивање датотеке неће успети.  Видите B<--memlimit-compress=>I<ограничење> за могуће начине о навођењу I<ограничења>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--memlimit-mt-decompress=>I<limit>"
msgstr "B<--memlimit-mt-decompress=>I<ограничење>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for multi-threaded decompression.  This can only affect the number of threads; this will never make B<xz> refuse to decompress a file.  If I<limit> is too low to allow any multi-threading, the I<limit> is ignored and B<xz> will continue in single-threaded mode.  Note that if also B<--memlimit-decompress> is used, it will always apply to both single-threaded and multi-threaded modes, and so the effective I<limit> for multi-threading will never be higher than the limit set with B<--memlimit-decompress>."
msgstr "Поставља ограничење коришћења меморије за распакивање са више нити.  Ово може утицати само на број нити; ово никада неће учинити да B<xz> одбије да распакује датотеке.  Ако је I<ограничење> превише ниско да омогући било који рад са више нити, I<ограничење> се занемарује а B<xz> ће наставити у режиму једне нити.  Знајте да ако се такође користи B<--memlimit-decompress>, увек ће се применити и на режим једне и на режим више нити, и тако да стварно I<ограничење> за више нити никада неће бити више од ограничења постављеног са B<--memlimit-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In contrast to the other memory usage limit options, B<--memlimit-mt-decompress=>I<limit> has a system-specific default I<limit>.  B<xz --info-memory> can be used to see the current value."
msgstr "У супротности са другим опцијама ограничења коришћења меморије, B<--memlimit-mt-decompress=>I<ограничење> има систему специфично основно I<ограничење>.  B<xz --info-memory> се може користити да видите тренутну вредност."

#. type: Plain text
#: ../src/xz/xz.1
msgid "This option and its default value exist because without any limit the threaded decompressor could end up allocating an insane amount of memory with some input files.  If the default I<limit> is too low on your system, feel free to increase the I<limit> but never set it to a value larger than the amount of usable RAM as with appropriate input files B<xz> will attempt to use that amount of memory even with a low number of threads.  Running out of memory or swapping will not improve decompression performance."
msgstr "Ова опција и њена основна вредност постоје јер без икаквог ограничења нитни распакивач ће завршити са додељивањем неразумног износа меморије са неким улазним датотекама.  Ако је основно I<ограничење> превише ниско на вашем систему, слободно повећајте I<ограничење> али га никада не постављајте на вредност већу од износа употребљивог RAM-а јер са одговарајућим улазним датотекама B<xz> ће покушати да користи тај износ меморије чак и са малим бројем нити.  Остајући без меморије или разменом неће се побољшати учинковитост распакивања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "See B<--memlimit-compress=>I<limit> for possible ways to specify the I<limit>.  Setting I<limit> to B<0> resets the I<limit> to the default system-specific value."
msgstr "Видите B<--memlimit-compress=>I<ограничење> за могуће начине за навођење I<ограничења>.  Постављање I<ограничења> на B<0> враћа I<ограничење> на систему специфичну основну вредност."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-M> I<limit>, B<--memlimit=>I<limit>, B<--memory=>I<limit>"
msgstr "B<-M> I<ограничење>, B<--memlimit=>I<ограничење>, B<--memory=>I<ограничење>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is equivalent to specifying B<--memlimit-compress=>I<limit> B<--memlimit-decompress=>I<limit> B<--memlimit-mt-decompress=>I<limit>."
msgstr "Ово је исто као и навођење B<--memlimit-compress=>I<ограничење> B<--memlimit-decompress=>I<ограничење> B<--memlimit-mt-decompress=>I<ограничење>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--no-adjust>"
msgstr "B<--no-adjust>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display an error and exit if the memory usage limit cannot be met without adjusting settings that affect the compressed output.  That is, this prevents B<xz> from switching the encoder from multi-threaded mode to single-threaded mode and from reducing the LZMA2 dictionary size.  Even when this option is used the number of threads may be reduced to meet the memory usage limit as that won't affect the compressed output."
msgstr "Приказује грешку и излази ако ограничење коришћења меморије не може бити задовољено без дотеривања поставки које утичу на запаковани излаз.  Тако је, ово спречава B<xz> да промени шифрера из режима више нити у режим једне нити и да смањи величину LZMA2 речника.  Чак и када се ова опција користи број нити се може смањити да задовољи ограничење коришћења меморије јер то неће утицати на запаковани излаз."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Automatic adjusting is always disabled when creating raw streams (B<--format=raw>)."
msgstr "Аутоматско дотеривање је увек искључено приликом стварања сирових токова (B<--format=raw>)."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-T> I<threads>, B<--threads=>I<threads>"
msgstr "B<-T> I<нити>, B<--threads=>I<нити>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of worker threads to use.  Setting I<threads> to a special value B<0> makes B<xz> use up to as many threads as the processor(s) on the system support.  The actual number of threads can be fewer than I<threads> if the input file is not big enough for threading with the given settings or if using more threads would exceed the memory usage limit."
msgstr "Наводи број нити радника за коришћење.  Постављање I<нити> на посебну вредност B<0> чини да B<xz> користи онолико нити колико процесор на систему подржава.  Стварни број нити може бити мањи од I<нити> ако улазна датотека није довољно велика за нитисање са датим поставкама или ако ће коришћење више нити премашити ограничење коришћења меморије."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The single-threaded and multi-threaded compressors produce different output.  Single-threaded compressor will give the smallest file size but only the output from the multi-threaded compressor can be decompressed using multiple threads.  Setting I<threads> to B<1> will use the single-threaded mode.  Setting I<threads> to any other value, including B<0>, will use the multi-threaded compressor even if the system supports only one hardware thread.  (B<xz> 5.2.x used single-threaded mode in this situation.)"
msgstr "Запакивачи једне нити и више нити дају различите излазе.  запакивач једне нити даће мању величину датотеке али само излаз из запакивача више нити може бити распакован коришћењем више нити. Постављање I<нити> на B<1> користиће режим једне нити.  Постављање I<нити> на неку другу вредност, укључујући B<0>, користиће запакивач више нити чак и ако систем подржава само једну нит хардвера.  (B<xz> 5.2.x је користио режим једне нити у овој прилици.)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "To use multi-threaded mode with only one thread, set I<threads> to B<+1>.  The B<+> prefix has no effect with values other than B<1>.  A memory usage limit can still make B<xz> switch to single-threaded mode unless B<--no-adjust> is used.  Support for the B<+> prefix was added in B<xz> 5.4.0."
msgstr "Да користите вишенитни режим са само једном нити, поставите I<нити> на B<+1>.  Префикс B<+> нема дејства са вредностима које нису B<1>.  Ограничење коришћења меморије може још увек учинити да се B<xz> пребаци на режим једне нити осим ако се не користи B<--no-adjust>.  Подршка за префикс B<+> је додата у B<xz> 5.4.0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If an automatic number of threads has been requested and no memory usage limit has been specified, then a system-specific default soft limit will be used to possibly limit the number of threads.  It is a soft limit in sense that it is ignored if the number of threads becomes one, thus a soft limit will never stop B<xz> from compressing or decompressing.  This default soft limit will not make B<xz> switch from multi-threaded mode to single-threaded mode.  The active limits can be seen with B<xz --info-memory>."
msgstr ""
"Ако је затражен аутоматски број нити и није наведено ограничење коришћења меморије, тада ће се користити основно меко ограничење специфично за систем за могуће ограничење броја нити.  То је меко ограничење у смислу да се занемарује ако број нити постане један, дакле меко ограничење никада неће зауставити B<xz> од запакивања или распакивања. Ово основно меко ограничење неће учинити да се B<xz> пребаци из режима више нити у режим једне нити.  Активно ограничење се може видети са\n"
" B<xz --info-memory>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently the only threading method is to split the input into blocks and compress them independently from each other.  The default block size depends on the compression level and can be overridden with the B<--block-size=>I<size> option."
msgstr "Тренутно једина метода нитисања је да се подели улаз на блокове и да се запакују независно један од другог.  Основна величина блока зависи од нивоа запакивања и може се преписати опцијом B<--block-size=>I<величина>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Threaded decompression only works on files that contain multiple blocks with size information in block headers.  All large enough files compressed in multi-threaded mode meet this condition, but files compressed in single-threaded mode don't even if B<--block-size=>I<size> has been used."
msgstr "Нитно распакивање ради само на датотекама које садрже више блокова са информацијом величине у заглављима блока.  Све довољно велике датотеке запаковане у вишенитном режиму задовољавају овај услов, али датотеке запаковане у једнонитном режиму не чак и ако је коришћено B<--block-size=>I<величина>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value for I<threads> is B<0>.  In B<xz> 5.4.x and older the default is B<1>."
msgstr "Основна вредност за I<нити> је B<0>.  У B<xz> 5.4.x и старијим основно је B<1>."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Custom compressor filter chains"
msgstr "Произвољни ланци филтера запакивача"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain allows specifying the compression settings in detail instead of relying on the settings associated to the presets.  When a custom filter chain is specified, preset options (B<-0> \\&...\\& B<-9> and B<--extreme>)  earlier on the command line are forgotten.  If a preset option is specified after one or more custom filter chain options, the new preset takes effect and the custom filter chain options specified earlier are forgotten."
msgstr "Произвољни ланац филтера омогућава детаљно одређивање подешавања сажимања уместо да се ослања на подешавања повезана са предподешавањима. Када је произвољни ланац филтера наведен, опције предподешавања (B<-0> \\&...\\& B<-9> и B<-extreme>) раније на линији наредби се заборављају. Ако је опција предподешавања наведена након једне или више опција произвољног ланца филтера, ново предподешавање ступа на снагу а раније наведене опције произвољног ланца филтера се заборављају."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A filter chain is comparable to piping on the command line.  When compressing, the uncompressed input goes to the first filter, whose output goes to the next filter (if any).  The output of the last filter gets written to the compressed file.  The maximum number of filters in the chain is four, but typically a filter chain has only one or two filters."
msgstr "Ланац филтера је упоредив са спојкама на линији наредби. Приликом сажимања, несажети улаз иде на први филтер, чији излаз иде на следећи филтер (ако фа има). Излаз последњег филтера бива записан у сажету датотеку. Највећи број филтера у ланцу је четири, али обично ланац филтера има само један или два филтера."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Many filters have limitations on where they can be in the filter chain: some filters can work only as the last filter in the chain, some only as a non-last filter, and some work in any position in the chain.  Depending on the filter, this limitation is either inherent to the filter design or exists to prevent security issues."
msgstr "Многи филтери имају ограничења о томе где могу бити у ланцу филтера: неки филтери могу радити само као последњи филтер у ланцу, неки само као не-последњи филтер, а неки раде на било ком месту у ланцу.  Овисно о филтеру, ово ограничење је или својствено дизајну филтера или постоји како би се спречили проблеми безбедности."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A custom filter chain can be specified in two different ways.  The options B<--filters=>I<filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> allow specifying an entire filter chain in one option using the liblzma filter string syntax.  Alternatively, a filter chain can be specified by using one or more individual filter options in the order they are wanted in the filter chain.  That is, the order of the individual filter options is significant! When decoding raw streams (B<--format=raw>), the filter chain must be specified in the same order as it was specified when compressing.  Any individual filter or preset options specified before the full chain option (B<--filters=>I<filters>)  will be forgotten.  Individual filters specified after the full chain option will reset the filter chain."
msgstr "Произвољни ланац филтера се може навести на два различита начина.  Опције B<--filters=>I<филтери> и B<--filters1=>I<филтери> \\&...\\& B<--filters9=>I<филтери> омогућавају навођење читавог ланца опција у једној опцији коришћењем синтаксу ниске „liblzma“ филтера.  Другачије, ланац филтера се може навести коришћењем једне или више појединачних опција филтера редом како се траже у ланцу филтера.  То је то, редослед појединачних опција филтера је важан! Приликом дешифровања сирових токова (B<--format=raw>), ланац филтера мора бити наведен истим редом као када је био наведен приликом сажимања.  Било који појединачни филтер или опције предподешавања наведене пре опције читавог ланца (B<--filters=>I<филтери>) биће заборављене.  Појединачни филтери наведени после опције читавог ланца ће поништити поставку ланца филтера."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Both the full and individual filter options take filter-specific I<options> as a comma-separated list.  Extra commas in I<options> are ignored.  Every option has a default value, so specify those you want to change."
msgstr "Обе опције и пуног и појединачног филтера узимају филтеру специфичне I<опције> као зарезом раздвојен списак.  Додатни зарези у I<опцијама> се занемарују.  Свака опција има основну вредност, тако да треба да наведете оне које желите да измените."

#. type: Plain text
#: ../src/xz/xz.1
msgid "To see the whole filter chain and I<options>, use B<xz -vv> (that is, use B<--verbose> twice).  This works also for viewing the filter chain options used by presets."
msgstr "Да видите читав ланац филтера и I<опције>, користите B<xz -vv> (тако је, користите B<--verbose> два пута).  Ово ради такође за преглед опција ланца филтера коришћених од стране предподешености."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters=>I<filters>"
msgstr "B<--filters=>I<филтери>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the full filter chain or a preset in a single option.  Each filter can be separated by spaces or two dashes (B<-->).  I<filters> may need to be quoted on the shell command line so it is parsed as a single option.  To denote I<options>, use B<:> or B<=>.  A preset can be prefixed with a B<-> and followed with zero or more flags.  The only supported flag is B<e> to apply the same options as B<--extreme>."
msgstr "Наводи цео ланац филтера или предподешеност у једној опцији.  Сваки филтер се може одвојити размацима или са две цртице (B<-->).  I<Филтери> можда треба да буду под наводницима на линији наредби конзоле тако да се прослеђују као једна опција.  Да означите I<опције>, користите B<:> или B<=>.  Предподешености се може додати префикс B<-> и може га пратити нула или још заставица.  Једина подржана заставица је B<e> за примену истих опција као B<--extreme>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters1>=I<filters> ... B<--filters9>=I<filters>"
msgstr "B<--filters1>=I<филтери> ... B<--filters9>=I<филтери>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify up to nine additional filter chains that can be used with B<--block-list>."
msgstr "Наводи до девет додатних ланаца филтера који се могу користити са B<--block-list>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, when compressing an archive with executable files followed by text files, the executable part could use a filter chain with a BCJ filter and the text part only the LZMA2 filter."
msgstr "На пример, приликом запакивања архиве са извршним датотекама за којима следе текстуалне датотеке, извршни део треба да користи ланац филтера са BCJ филтером а текстуални део само LZMA2 филтер."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--filters-help>"
msgstr "B<--filters-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing how to specify presets and custom filter chains in the B<--filters> and B<--filters1=>I<filters> \\&...\\& B<--filters9=>I<filters> options, and exit successfully."
msgstr "Приказује поруку помоћи описујући како се наводе предподешавања и произвољни ланци филтера у опцијама B<--filters> и B<--filters1=>I<филтери> \\&...\\& B<--filters9=>I<филтери>, и успешно излази."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma1>[B<=>I<options>]"
msgstr "B<--lzma1>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--lzma2>[B<=>I<options>]"
msgstr "B<--lzma2>[B<=>I<опције>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add LZMA1 or LZMA2 filter to the filter chain.  These filters can be used only as the last filter in the chain."
msgstr "Додаје LZMA1 или LZMA2 филтер у ланац филтера.  Ови филтери се могу користити само као последњи филтер у ланцу."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 is a legacy filter, which is supported almost solely due to the legacy B<.lzma> file format, which supports only LZMA1.  LZMA2 is an updated version of LZMA1 to fix some practical issues of LZMA1.  The B<.xz> format uses LZMA2 and doesn't support LZMA1 at all.  Compression speed and ratios of LZMA1 and LZMA2 are practically the same."
msgstr "LZMA1 је стари филтер, који је подржан углавном само због старог формата B<.lzma> датотеке, који подржава само LZMA1.  LZMA2 је освежено издање LZMA1 које поправља неке практичне проблеме LZMA1.  B<.xz> формат користи LZMA2 и не подржава LZMA1 уопште.  Брзина запакивања и односи LZMA1 и LZMA2 су практично исти."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA1 and LZMA2 share the same set of I<options>:"
msgstr "LZMA1 и LZMA2 деле исти скуп I<опција>:"

#.  TRANSLATORS: Don't translate bold strings like B<preset>, B<dict>,
#.  B<mode>, B<nice>, B<fast>, or B<normal> because those are command line
#.  options. On the other hand, do translate the italic strings like
#.  I<preset>, I<size>, and I<mode>, because such italic strings are
#.  placeholders which a user replaces with an actual value.
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<preset=>I<preset>"
msgstr "B<preset=>I<предподешавање>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reset all LZMA1 or LZMA2 I<options> to I<preset>.  I<Preset> consist of an integer, which may be followed by single-letter preset modifiers.  The integer can be from B<0> to B<9>, matching the command line options B<-0> \\&...\\& B<-9>.  The only supported modifier is currently B<e>, which matches B<--extreme>.  If no B<preset> is specified, the default values of LZMA1 or LZMA2 I<options> are taken from the preset B<6>."
msgstr "Враћа све LZMA1 или LZMA2 I<опције> на I<предподешеност>.  I<Предпдешеност> се састоји од целог броја, за којим може да следи једнословни измењивач предподешености.  Цео број може бити од B<0> до B<9>, поклапајући опције линије наредби B<-0> \\&...\\& B<-9>.  Једини тренутно подржани измењивач је B<e>, који се поклапа са B<--extreme>.  Ако B<preset> није наведено, основне вредности LZMA1 или LZMA2 I<опција> се узимају из предподешености B<6>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dict=>I<size>"
msgstr "B<dict=>I<величина>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary (history buffer)  I<size> indicates how many bytes of the recently processed uncompressed data is kept in memory.  The algorithm tries to find repeating byte sequences (matches) in the uncompressed data, and replace them with references to the data currently in the dictionary.  The bigger the dictionary, the higher is the chance to find a match.  Thus, increasing dictionary I<size> usually improves compression ratio, but a dictionary bigger than the uncompressed file is waste of memory."
msgstr "I<Величина> речника (међумеморија историјата) указује на то колико је бајтова недавно обрађених нераспакованих података задржано у меморији.  Алгоритам покушава да нађе понављајуће низове бајтова (поклапања) у нераспакованим подацима, и да их замени са упутама ка подацима који су тренутно у речнику.  Што је већи речник, већа је вероватноћа за налажење поклапања.  Стога, повећање I<величине > речника обично побољшава стопу запакивања, али речник већи од незапаковане датотеке јесте утрошак меморије."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Typical dictionary I<size> is from 64\\ KiB to 64\\ MiB.  The minimum is 4\\ KiB.  The maximum for compression is currently 1.5\\ GiB (1536\\ MiB).  The decompressor already supports dictionaries up to one byte less than 4\\ GiB, which is the maximum for the LZMA1 and LZMA2 stream formats."
msgstr "Уобичајена I<величина> речника је од 64\\ KiB до 64\\ MiB.  Најмање је 4\\ KiB.  Највише за запакивање тренутно је 1.5\\ GiB (1536\\ MiB).  Распакивач већ подржава речнике до једног бајта мање од 4\\ GiB, што је максимум за формате LZMA1 и LZMA2 тока."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Dictionary I<size> and match finder (I<mf>)  together determine the memory usage of the LZMA1 or LZMA2 encoder.  The same (or bigger) dictionary I<size> is required for decompressing that was used when compressing, thus the memory usage of the decoder is determined by the dictionary size used when compressing.  The B<.xz> headers store the dictionary I<size> either as 2^I<n> or 2^I<n> + 2^(I<n>-1), so these I<sizes> are somewhat preferred for compression.  Other I<sizes> will get rounded up when stored in the B<.xz> headers."
msgstr "I<Величина> речника и налазач поклапања (I<mf>) заједно одређују коришћење меморије за LZMA1 или LZMA2 шифрере.  Иста (или већа) I<величина> речника је потребна за распакивање као она која је коришћена за запаквање, стога се коришћење меморије дешифрера одређује према величини речника коришћеног за запакивање.  B<.xz> заглавља чувају I<величину> речника или као 2^I<n> или 2^I<n> + 2^(I<n>-1), тако да су те I<величине> некако пожељније за запакивање.  Друге I<величине> биће заокружене приликом чувања у B<.xz> заглављима."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lc=>I<lc>"
msgstr "B<lc=>I<lc>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal context bits.  The minimum is 0 and the maximum is 4; the default is 3.  In addition, the sum of I<lc> and I<lp> must not exceed 4."
msgstr "Наводи број битова контекста литерала.  Најмање је 0 а највише је 4; основно је 3.  Као додатак, збир I<lc> и I<lp> не сме да премаши 4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All bytes that cannot be encoded as matches are encoded as literals.  That is, literals are simply 8-bit bytes that are encoded one at a time."
msgstr "Сви бајтови који се не могу шифровати јер су поклапања шифрована као литерали. То је то, литерали су једноставно 8-битни бајтови који су шифровани један по један."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The literal coding makes an assumption that the highest I<lc> bits of the previous uncompressed byte correlate with the next byte.  For example, in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter.  In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters.  When I<lc> is at least 3, the literal coding can take advantage of this property in the uncompressed data."
msgstr "Кодирање литерала врши претпоставку да највећи I<lc> битови претходног незапакованог бајта су у узајамној вези са следећим бајтом.  На пример, у уобичајеном енглеском тексту, за великим словом често следи мало слово, а за малим словом обично следи још једно мало слово.  У скупу US-ASCII знакова, највиша три бита су 010 за велика слова и 011 за мала слова.  Када је I<lc> барем 3, кодирање литерала може имати предност овог својства у незапакованим подацима."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default value (3) is usually good.  If you want maximum compression, test B<lc=4>.  Sometimes it helps a little, and sometimes it makes compression worse.  If it makes it worse, test B<lc=2> too."
msgstr "Основна вредност (3) је обично добра.  Ако желите највеће запакивање, испробајте B<lc=4>.  Некад то мало помогне а некад чини запакивање горим.  Ако га учини горим, испробајте такође B<lc=2>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<lp=>I<lp>"
msgstr "B<lp=>I<lp>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of literal position bits.  The minimum is 0 and the maximum is 4; the default is 0."
msgstr "Наводи број битова положаја литерала.  Најмање је 0 а највише је 4; основно је 0."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Lp> affects what kind of alignment in the uncompressed data is assumed when encoding literals.  See I<pb> below for more information about alignment."
msgstr "I<Lp> утиче на то која врста поравнања у незапакованим подацима се подразумева приликом шифровања литерала.  Видите I<pb> испод за више информација о поравнању."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<pb=>I<pb>"
msgstr "B<pb=>I<pb>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the number of position bits.  The minimum is 0 and the maximum is 4; the default is 2."
msgstr "Наводи број битова положаја.  Најмање је 0 а највише је 4; основно је 2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Pb> affects what kind of alignment in the uncompressed data is assumed in general.  The default means four-byte alignment (2^I<pb>=2^2=4), which is often a good choice when there's no better guess."
msgstr "I<Pb> утиче на то која врста поравнања у незапакованим подацима се подразумева у опште.  Основно значи четворобајтно поравнање (2^I<pb>=2^2=4), што је често добар избор када нема бољег решења."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When the alignment is known, setting I<pb> accordingly may reduce the file size a little.  For example, with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting B<pb=0> can improve compression slightly.  For UTF-16 text, B<pb=1> is a good choice.  If the alignment is an odd number like 3 bytes, B<pb=0> might be the best choice."
msgstr "Када је поравнање познато, постављање одговарајућег I<pb> може мало да смањи величину датотеке.  На пример, са датотекама текста које имају поравнање једног бајта (US-ASCII, ISO-8859-*, UTF-8), постављање B<pb=0> може незнатно да побољша запакивање.  За UTF-16 текст, B<pb=1> је добар избор.  Ако је поравнање непаран број као 3 бајта, B<pb=0> може бити најбољи избор."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Even though the assumed alignment can be adjusted with I<pb> and I<lp>, LZMA1 and LZMA2 still slightly favor 16-byte alignment.  It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2."
msgstr "Иако се претпостављено поравнање може подесити са I<pb> и I<lp>, LZMA1 и LZMA2 и даље незнатно фаворизују 16-бајтно поравнање.  То би могло бити вредно узети у обзир приликом дизајнирања формата датотека које ће вероватно бити често запаковане са LZMA1 или LZMA2."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mf=>I<mf>"
msgstr "B<mf=>I<mf>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Match finder has a major effect on encoder speed, memory usage, and compression ratio.  Usually Hash Chain match finders are faster than Binary Tree match finders.  The default depends on the I<preset>: 0 uses B<hc3>, 1\\(en3 use B<hc4>, and the rest use B<bt4>."
msgstr "Налазач поклапања има главни утицај на брзину шифрера, коришћење меморије и стопу запакивања.  Обично Hash Chain налазачи поклапања су бржи од Binary Tree налазача поклапања.  Основност зависи од I<предподешености>: 0 користи B<hc3>, 1\\(en3 користи B<hc4>, а остало користи B<bt4>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The following match finders are supported.  The memory usage formulas below are rough approximations, which are closest to the reality when I<dict> is a power of two."
msgstr "Следећи налазачи поклапања су подржани.  Формуле коришћења меморије испод су грубе апроксимације које су ближе стварности када је I<речник> степен двојке."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc3>"
msgstr "B<hc3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2- and 3-byte hashing"
msgstr "Ланац хеша са хеширањем 2- и 3-бајта"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 3"
msgstr "Најмања вредност за I<фино>: 3"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage:"
msgstr "Коришћење меморије:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<речник> * 7.5 (ако је I<речник> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 5.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<речник> * 5.5 + 64 MiB (if I<речник> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<hc4>"
msgstr "B<hc4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Hash Chain with 2-, 3-, and 4-byte hashing"
msgstr "Ланац хеша са хеширањем 2-, 3- и 4-бајта"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 4"
msgstr "Најмања вредност за I<фино>: 4"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 7.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<речник> * 7.5 (ако је I<речник> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 6.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<речник> * 6.5 (ако је I<речник> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt2>"
msgstr "B<bt2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-byte hashing"
msgstr "Бинарно стабло са 2-бајта хеширања"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum value for I<nice>: 2"
msgstr "Најмања вредност за I<фино>: 2"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage: I<dict> * 9.5"
msgstr "Коришћење меморије: I<речник> * 9.5"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt3>"
msgstr "B<bt3>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2- and 3-byte hashing"
msgstr "Бинарно стабло са 2- и 3-бајта хеширања"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 16 MiB);"
msgstr "I<речник> * 11.5 (if I<речник> E<lt>= 16 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 9.5 + 64 MiB (if I<dict> E<gt> 16 MiB)"
msgstr "I<речник> * 9.5 + 64 MiB (ако је I<речник> E<gt> 16 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<bt4>"
msgstr "B<bt4>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Binary Tree with 2-, 3-, and 4-byte hashing"
msgstr "Бинарно стабло са 2-, 3-, и 4-бајта хеширања"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 11.5 (if I<dict> E<lt>= 32 MiB);"
msgstr "I<речник> * 11.5 (if I<речник> E<lt>= 32 MiB);"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<dict> * 10.5 (if I<dict> E<gt> 32 MiB)"
msgstr "I<речник> * 10.5 (if I<речник> E<gt> 32 MiB)"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<mode=>I<mode>"
msgstr "B<mode=>I<режим>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression I<mode> specifies the method to analyze the data produced by the match finder.  Supported I<modes> are B<fast> and B<normal>.  The default is B<fast> for I<presets> 0\\(en3 and B<normal> for I<presets> 4\\(en9."
msgstr "I<Режим> запакивања наводи методу за анализу података које произведе налазач поклапања.  Подржани I<режими> су B<fast> и B<normal>.  Подразумева се B<fast> за I<предподешавања> 0\\(en3 и B<normal> за I<предподешавања> 4\\(en9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Usually B<fast> is used with Hash Chain match finders and B<normal> with Binary Tree match finders.  This is also what the I<presets> do."
msgstr "Обично се B<fast> користи са налазачима Hash Chain поклапања а B<normal> са налазачима Binary Tree поклапања.  Ово је исто што раде и I<предподешености>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<nice=>I<nice>"
msgstr "B<nice=>I<фино>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify what is considered to be a nice length for a match.  Once a match of at least I<nice> bytes is found, the algorithm stops looking for possibly better matches."
msgstr "Наводи оно што се сматра да је фина дужина за поклапање.  Када се поклапање од барем I<nice> бајтова нађе, алгоритам зауставља тражење могућих бољих поклапања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Nice> can be 2\\(en273 bytes.  Higher values tend to give better compression ratio at the expense of speed.  The default depends on the I<preset>."
msgstr "I<Фино> може бити 2\\(en273 бајта.  Веће вредности теже да дају бољу стопу запакивања на уштрб брзине.  Основно зависи од I<предподешавања>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<depth=>I<depth>"
msgstr "B<depth=>I<дубина>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the maximum search depth in the match finder.  The default is the special value of 0, which makes the compressor determine a reasonable I<depth> from I<mf> and I<nice>."
msgstr "Наводи највећу дубину претраге у налазачу поклапања.  Основно је посебна вредност 0, која чини да запакивач одреди разумљиву I<дубину> из I<mf> и I<nice>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Reasonable I<depth> for Hash Chains is 4\\(en100 and 16\\(en1000 for Binary Trees.  Using very high values for I<depth> can make the encoder extremely slow with some files.  Avoid setting the I<depth> over 1000 unless you are prepared to interrupt the compression in case it is taking far too long."
msgstr "Разумљива I<дубина> за ланце хеша је 4\\(en100 и 16\\(en1000 за стабло извршних.  Коришћење врло великих вредности за I<дубину> може учинити шифрер врло спорим са неким датотекама.  Избегавајте постављање I<дубине> преко 1000 осим ако нисте спремни да прекинете запакивање у случају да потраје превише дуго."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decoding raw streams (B<--format=raw>), LZMA2 needs only the dictionary I<size>.  LZMA1 needs also I<lc>, I<lp>, and I<pb>."
msgstr "Приликом дешифровања сирових токова (B<--format=raw>), LZMA2 треба само I<величина> речника.  LZMA1 треба такође I<lc>, I<lp> и I<pb>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--x86>[B<=>I<options>]"
msgstr "B<--x86>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm>[B<=>I<options>]"
msgstr "B<--arm>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--armthumb>[B<=>I<options>]"
msgstr "B<--armthumb>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--arm64>[B<=>I<options>]"
msgstr "B<--arm64>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--powerpc>[B<=>I<options>]"
msgstr "B<--powerpc>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--ia64>[B<=>I<options>]"
msgstr "B<--ia64>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--sparc>[B<=>I<options>]"
msgstr "B<--sparc>[B<=>I<опције>]"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--riscv>[B<=>I<options>]"
msgstr "B<--riscv>[B<=>I<опције>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add a branch/call/jump (BCJ) filter to the filter chain.  These filters can be used only as a non-last filter in the filter chain."
msgstr "Додаје „branch/call/jump“ (BCJ) филтер ланцу филтера.  Ови филтери могу се користити само ако нису последњи филтер у ланцу филтера."

#. type: Plain text
#: ../src/xz/xz.1
msgid "A BCJ filter converts relative addresses in the machine code to their absolute counterparts.  This doesn't change the size of the data but it increases redundancy, which can help LZMA2 to produce 0\\(en15\\ % smaller B<.xz> file.  The BCJ filters are always reversible, so using a BCJ filter for wrong type of data doesn't cause any data loss, although it may make the compression ratio slightly worse.  The BCJ filters are very fast and use an insignificant amount of memory."
msgstr "BCJ филтер претвара релативне адресе у машинском коду на њихове апсолутне двојнике. Ово не мења величину података, али повећава сувишност, што може помоћи LZMA2 да произведе 0\\(en15\\% мању B<.xz> датотеку. BCJ филтери су увек реверзибилни, стога коришћење BCJ филтера за погрешну врсту података не доводи ни до каквог губитка података, мада може да учини степен сажимања нешто лошијим. BCJ филтери су врло брзи и користе безначајну количину меморије."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These BCJ filters have known problems related to the compression ratio:"
msgstr "Ови BCJ филтери имају познате проблеме везане за стопу запакивања:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Some types of files containing executable code (for example, object files, static libraries, and Linux kernel modules)  have the addresses in the instructions filled with filler values.  These BCJ filters will still do the address conversion, which will make the compression worse with these files."
msgstr "Неке врсте датотека које садрже извршни код (на пример, објектне датотеке, статичке библиотеке и модули Линукс кернела) имају адресе у упутствима испуњеним вредностима попуњавача. Ови BCJ филтери и даље ће радити претварање адресе, што ће запакивање учинити горим са овим датотекама."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If a BCJ filter is applied on an archive, it is possible that it makes the compression ratio worse than not using a BCJ filter.  For example, if there are similar or even identical executables then filtering will likely make the files less similar and thus compression is worse.  The contents of non-executable files in the same archive can matter too.  In practice one has to try with and without a BCJ filter to see which is better in each situation."
msgstr "Ако се BCJ филтер примени на архиву, могуће је да ће учинити стопу запакивања лошијим него кад се не би користио.  На пример, ако постоје сличне или чак истоветне извршне тада ће филтрирање вероватно учинити датотеке мање сличним и стога је запакивање лошије.  Садржај не-извршних датотека у истој архиви је такође важан.  У пракси се мора покушати са и без BCJ филтера да би се видело шта је боље у којој прилици."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Different instruction sets have different alignment: the executable file must be aligned to a multiple of this value in the input data to make the filter work."
msgstr "Различити скупови упутстава имају различита поравнања: извршна датотека мора бити поравната на множилац ове вредности у улазним подацима како би филтер радио."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Filter"
msgstr "Филтер"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Alignment"
msgstr "Поравнање"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Notes"
msgstr "Напомене"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "x86"
msgstr "x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "32-bit or 64-bit x86"
msgstr "32-бита или 64-бита x86"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM"
msgstr "ARM"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM-Thumb"
msgstr "ARM-Thumb"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "ARM64"
msgstr "ARM64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "4096-byte alignment is best"
msgstr "4096-бита поравнање је најбоље"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "PowerPC"
msgstr "PowerPC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Big endian only"
msgstr "Само велика крајност"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "IA-64"
msgstr "IA-64"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "16"
msgstr "16"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Itanium"
msgstr "Itanium"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "SPARC"
msgstr "SPARC"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "RISC-V"
msgstr "RISC-V"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since the BCJ-filtered data is usually compressed with LZMA2, the compression ratio may be improved slightly if the LZMA2 options are set to match the alignment of the selected BCJ filter.  Examples:"
msgstr "Како су BCJ-филтрирани подаци обично запаковани са LZMA2, стопа запакивања може бити незнатно побољшана ако су LZMA2 опције постављене да поклопе поравнање изабраног BCJ филтера.  Примери:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "IA-64 filter has 16-byte alignment so B<pb=4,lp=4,lc=0> is good with LZMA2 (2^4=16)."
msgstr "IA-64 филтер има поравнање 16-бајта тако да је B<pb=4,lp=4,lc=0> добро са LZMA2 (2^4=16)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "RISC-V code has 2-byte or 4-byte alignment depending on whether the file contains 16-bit compressed instructions (the C extension).  When 16-bit instructions are used, B<pb=2,lp=1,lc=3> or B<pb=1,lp=1,lc=3> is good.  When 16-bit instructions aren't present, B<pb=2,lp=2,lc=2> is the best.  B<readelf -h> can be used to check if \"RVC\" appears on the \"Flags\" line."
msgstr "RISC-V код има поравнање од 2 или 4 бајта у зависности од тога да ли датотека садржи запаковане инструкције од 16 бита (проширење C).  Када се користе инструкције од 16 бита, B<pb=2,lp=1,lc=3> или B<pb=1,lp=1,lc=3> је добро.  Када нема инструкција од 16 бита, B<pb=2,lp=2,lc=2> је најбоље.  B<readelf -h> се може користити да се провери да ли се „RVC“ јавља у реду „Заставице“."

#. type: Plain text
#: ../src/xz/xz.1
msgid "ARM64 is always 4-byte aligned so B<pb=2,lp=2,lc=2> is the best."
msgstr "ARM64 је увек 4-бајта поравнат тако да је B<pb=2,lp=2,lc=2> најбоље."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The x86 filter is an exception.  It's usually good to stick to LZMA2's defaults (B<pb=2,lp=0,lc=3>)  when compressing x86 executables."
msgstr "Филтер x86 је изузетак.  Обично је добро придржавати се LZMA2 основности (B<pb=2,lp=0,lc=3>) приликом запакивања x86 извршних."

#. type: Plain text
#: ../src/xz/xz.1
msgid "All BCJ filters support the same I<options>:"
msgstr "Сви BCJ филтери подржавају исте I<опције>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<start=>I<offset>"
msgstr "B<start=>I<померај>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the start I<offset> that is used when converting between relative and absolute addresses.  The I<offset> must be a multiple of the alignment of the filter (see the table above).  The default is zero.  In practice, the default is good; specifying a custom I<offset> is almost never useful."
msgstr "Наводи I<померај> почетка који се користи приликом претварања између релативних и апсолутних адреса.  I<Померај> мора бити производ поравнања филтера (видите табелу изнад).  Основно је нула.  У пракси, основност је добра; навођење произвољног I<помераја> скоро никада није од користи."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--delta>[B<=>I<options>]"
msgstr "B<--delta>[B<=>I<опције>]"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Add the Delta filter to the filter chain.  The Delta filter can be only used as a non-last filter in the filter chain."
msgstr "Додаје Delta филтер у ланац филтера.  Делта филтер може се користити само ако није последњи филтер у ланцу филтера."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Currently only simple byte-wise delta calculation is supported.  It can be useful when compressing, for example, uncompressed bitmap images or uncompressed PCM audio.  However, special purpose algorithms may give significantly better results than Delta + LZMA2.  This is true especially with audio, which compresses faster and better, for example, with B<flac>(1)."
msgstr "Тренутно је подржан само једноставан делта прорачун једног по једног бита.  Може бити користан приликом запакивања, на пример, незапакованих битмап слика или незапакованог PCM звука.  Међутим, алгоритми посебне намене могу дати знатно боље резултате него Delta + LZMA2. Ово је тачно, посебно са звуком, који се запакује брже и боље, на пример, са B<flac>(1)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Supported I<options>:"
msgstr "Подржане I<опције>:"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<dist=>I<distance>"
msgstr "B<dist=>I<растојање>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Specify the I<distance> of the delta calculation in bytes.  I<distance> must be 1\\(en256.  The default is 1."
msgstr "Наводи I<растојање> делта прорачуна у бајтима.  I<Растојање> мора бити 1\\(en256.  Основно је 1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "For example, with B<dist=2> and eight-byte input A1 B1 A2 B3 A3 B5 A4 B7, the output will be A1 B1 01 02 01 02 01 02."
msgstr "На пример, са B<dist=2> и осмобајтним улазом A1 B1 A2 B3 A3 B5 A4 B7, излаз ће бити A1 B1 01 02 01 02 01 02."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Other options"
msgstr "Остале опције"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-q>, B<--quiet>"
msgstr "B<-q>, B<--quiet>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Suppress warnings and notices.  Specify this twice to suppress errors too.  This option has no effect on the exit status.  That is, even if a warning was suppressed, the exit status to indicate a warning is still used."
msgstr "Потискује упозорења и обавештења.  Наведите ово два пута да потиснете и грешке.  Ова опција нема утицаја на стање излаза.  Тако је, чак и ако је упозорење потиснуто, стање излаза које указује на упозорење се и даље користи."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-v>, B<--verbose>"
msgstr "B<-v>, B<--verbose>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Be verbose.  If standard error is connected to a terminal, B<xz> will display a progress indicator.  Specifying B<--verbose> twice will give even more verbose output."
msgstr "Бива опширан.  Ако је стандардна грешка повезана са терминалом, B<xz> ће приказати указивач напретка.  Навођењем B<--verbose> два пута добија се још опширнији излаз."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The progress indicator shows the following information:"
msgstr "Указивач напредовања показује следеће информације:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Completion percentage is shown if the size of the input file is known.  That is, the percentage cannot be shown in pipes."
msgstr "Проценат довршености се показује ако је величина улазне датотеке позната.  Тако је, проценат се не може приказати у спојкама."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of compressed data produced (compressing)  or consumed (decompressing)."
msgstr "Количина запакованих података проистеклих (запакивање) или утрошених (распакивање)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of uncompressed data consumed (compressing)  or produced (decompressing)."
msgstr "Количина распакованих података утрошених (запакивање) или проистеклих (распакивање)."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, which is calculated by dividing the amount of compressed data processed so far by the amount of uncompressed data processed so far."
msgstr "Размера паковања, која се израчунава дељењем количине запакованих података обрађених до сада количином незапакованих података до сада обрађених."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression or decompression speed.  This is measured as the amount of uncompressed data consumed (compression) or produced (decompression) per second.  It is shown after a few seconds have passed since B<xz> started processing the file."
msgstr "Брзина запакивања или распакивања.  Ово се мери као количина незапакованих података утрошених (запакивање) или добијених (распакивање) у секунди.  Приказује се након неколико секунде након што B<xz> започне обраду датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Elapsed time in the format M:SS or H:MM:SS."
msgstr "Протекло време у формату М:СС или Ч:ММ:СС."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Estimated remaining time is shown only when the size of the input file is known and a couple of seconds have already passed since B<xz> started processing the file.  The time is shown in a less precise format which never has any colons, for example, 2 min 30 s."
msgstr "Процењено преостало време се приказује само када је величина улазне датотеке позната и неколико секунди је већ протекло од кад је B<xz> започео обраду датотеке.  Време се приказује у мање тачном формату које никада нема двотачке, на пример, 2 мин 30 сек."

#. type: Plain text
#: ../src/xz/xz.1
msgid "When standard error is not a terminal, B<--verbose> will make B<xz> print the filename, compressed size, uncompressed size, compression ratio, and possibly also the speed and elapsed time on a single line to standard error after compressing or decompressing the file.  The speed and elapsed time are included only when the operation took at least a few seconds.  If the operation didn't finish, for example, due to user interruption, also the completion percentage is printed if the size of the input file is known."
msgstr "Када стандардна грешка није терминал, B<--verbose> ће учинити да B<xz> испише назив датотеке, запаковану величину, незапаковану величину, стопу запакивања и по могућству такође брзину и протекло време на једном реду на стандардну грешку након запакивања или распакивања датотеке.  Брзина и протекло време су укључени само када радња потраје барем неколико секунде.  Ако се радња не заврши, на пример, услед што корисник прекине, такође се исписује проценат завршености ако је величина улазне датотеке позната."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-Q>, B<--no-warn>"
msgstr "B<-Q>, B<--no-warn>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Don't set the exit status to 2 even if a condition worth a warning was detected.  This option doesn't affect the verbosity level, thus both B<--quiet> and B<--no-warn> have to be used to not display warnings and to not alter the exit status."
msgstr "Не поставља стање излаза на 2 чак и ако је услов вредан упозорења откривен.  Ова опција не утиче на ниво опширности, стога и B<--quiet> и B<--no-warn> треба да се користе да се не приказују упозорења и да се не измени стање излаза."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--robot>"
msgstr "B<--robot>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Print messages in a machine-parsable format.  This is intended to ease writing frontends that want to use B<xz> instead of liblzma, which may be the case with various scripts.  The output with this option enabled is meant to be stable across B<xz> releases.  See the section B<ROBOT MODE> for details."
msgstr "Исписује поруке у формату обрадивом машинама.  Ово је замишљено да олакша писање челника који желе да користе B<xz> уместо „liblzma“, што може бити случај са разним скриптама.  Излаз са овом опцијом укљученом је замишљен да буде стабилан кроз B<xz> издања.  Видите одељак B<РЕЖИМ РОБОТА> за више о томе."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<--info-memory>"
msgstr "B<--info-memory>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display, in human-readable format, how much physical memory (RAM)  and how many processor threads B<xz> thinks the system has and the memory usage limits for compression and decompression, and exit successfully."
msgstr "Приказује, у формату читљивом људима, колико физичке меморије (RAM) и колико нити процесора B<xz> мисли да систем има и ограничења коришћења меморије за запакивање и распакивање и излази успешно."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-h>, B<--help>"
msgstr "B<-h>, B<--help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing the most commonly used options, and exit successfully."
msgstr "Приказује поруку помоћи описујући најчешће коришћених опција, и успешно излази."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<-H>, B<--long-help>"
msgstr "B<-H>, B<--long-help>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display a help message describing all features of B<xz>, and exit successfully"
msgstr "Приказује поруку помоћи описујући све функције B<xz>, и успешно излази"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "B<-V>, B<--version>"
msgstr "B<-V>, B<--version>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Display the version number of B<xz> and liblzma in human readable format.  To get machine-parsable output, specify B<--robot> before B<--version>."
msgstr "Приказује број издања за B<xz> и „liblzma“ у формату читљивом људима.  Да добијете излаз машинама обрадив, наведите B<--robot> пре B<--version>."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "ROBOT MODE"
msgstr "РЕЖИМ РОБОТА"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The robot mode is activated with the B<--robot> option.  It makes the output of B<xz> easier to parse by other programs.  Currently B<--robot> is supported only together with B<--list>, B<--filters-help>, B<--info-memory>, and B<--version>.  It will be supported for compression and decompression in the future."
msgstr "Режим робота се покреће опцијом B<--robot>.  Чини да излаз B<xz> буде лакши за обраду другим програмима.  Тренутно B<--robot> је подржано само заједно са B<--list>, B<--filters-help>, B<--info-memory>, и B<--version>.  Биће подржан за запакивање и распакивање у будућности."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "List mode"
msgstr "Режим списка"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --list> uses tab-separated output.  The first column of every line has a string that indicates the type of the information found on that line:"
msgstr "B<xz --robot --list> користи излаз раздвојен табулатором.  Прва колона сваког реда садржи ниску која указује на врсту информације која се налази у том реду:"

#.  TRANSLATORS: The bold strings B<name>, B<file>, B<stream>, B<block>,
#.  B<summary>, and B<totals> are produced by the xz tool for scripts to
#.  parse, thus the untranslated strings must be included in the translated
#.  man page. It may be useful to provide a translated string in parenthesis
#.  without bold, for example: "B<name> (nimi)"
#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<name>"
msgstr "B<name> (назив)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is always the first line when starting to list a file.  The second column on the line is the filename."
msgstr "Ово је увек први ред приликом почетка листања датотеке.  Друга колона у реду је назив датотеке."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<file>"
msgstr "B<file> (датотека)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line contains overall information about the B<.xz> file.  This line is always printed after the B<name> line."
msgstr "Овај ред садржи свеукупне информације о B<.xz> датотеци.  Овај ред се увек исписује после реда B<назив>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<stream>"
msgstr "B<stream> (ток)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<stream> lines as there are streams in the B<.xz> file."
msgstr "Ова врста реда се користи само када је B<--verbose> наведено.  Има толико редова B<тока> колико има токова у B<.xz> даатотеци."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<block>"
msgstr "B<block> (блок)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified.  There are as many B<block> lines as there are blocks in the B<.xz> file.  The B<block> lines are shown after all the B<stream> lines; different line types are not interleaved."
msgstr "Ова врста реда се користи само када је B<--verbose> наведено.  Има онолико редова B<блока> колико има блокова у B<.xz> датотеци.  Редови B<блока> се приказују након свих редова B<тока>; различите врсте реда се не преплићу."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<summary>"
msgstr "B<summary> (сажетак)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line type is used only when B<--verbose> was specified twice.  This line is printed after all B<block> lines.  Like the B<file> line, the B<summary> line contains overall information about the B<.xz> file."
msgstr "Ова врста реда се користи само када је B<--verbose> наведено два пута.  Овај ред се исписује након свих редова B<блока>.  Као ред B<датотека>, ред B<сажетка> садржи преглед информација о B<.xz> датотеци."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<totals>"
msgstr "B<totals> (укупност)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This line is always the very last line of the list output.  It shows the total counts and sizes."
msgstr "Овај ред је увек последњи ред на списку излаза.  Показује укупне збирове и величине."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<file> lines:"
msgstr "Колоне редова B<датотеке>:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "2."
msgstr "2."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams in the file"
msgstr "Број токова у датотеци"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "3."
msgstr "3."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total number of blocks in the stream(s)"
msgstr "Укупан број блокова у току(овима)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "4."
msgstr "4."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size of the file"
msgstr "Величина запаковане датотеке"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "5."
msgstr "5."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size of the file"
msgstr "Величина незапаковане датотеке"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "6."
msgstr "6."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio, for example, B<0.123>.  If ratio is over 9.999, three dashes (B<--->)  are displayed instead of the ratio."
msgstr "Размера паковања, на пример, B<0.123>.  Ако је размера преко 9.999, три цртице (B<--->)  се приказују уместо размере."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "7."
msgstr "7."

#.  TRANSLATORS: Don't translate the bold strings B<None>, B<CRC32>,
#.  B<CRC64>, B<SHA-256>, or B<Unknown-> here. In robot mode, xz produces
#.  them in untranslated form for scripts to parse.
#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names.  The following strings are used for the known check types: B<None>, B<CRC32>, B<CRC64>, and B<SHA-256>.  For unknown check types, B<Unknown->I<N> is used, where I<N> is the Check ID as a decimal number (one or two digits)."
msgstr "Зарезом раздвојен списак назива провера целовитости.  Следеће ниске се користе за познате врсте провера: B<None>, B<CRC32>, B<CRC64> и B<SHA-256>.  За непознате врсте провера, користи се B<Unknown->I<N>, где је I<N> ИД провере као децимални број (једна или две цифре)."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "8."
msgstr "8."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total size of stream padding in the file"
msgstr "Укупна величина попуњавања тока у датотеци"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<stream> lines:"
msgstr "Колоне редова B<тока>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream number (the first stream is 1)"
msgstr "Број тока (први ток је 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks in the stream"
msgstr "Број блокова у току"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset"
msgstr "Померај почетка запакованог"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset"
msgstr "Померај почетка незапаковане"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size (does not include stream padding)"
msgstr "Величина запакованог (не укључује попуну тока)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed size"
msgstr "Величина незапаковане"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compression ratio"
msgstr "Размера паковања"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "9."
msgstr "9."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the integrity check"
msgstr "Назив провере целовитости"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "10."
msgstr "10."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of stream padding"
msgstr "Величина попуне тока"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<block> lines:"
msgstr "Колоне редова B<блока>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of the stream containing this block"
msgstr "Број тока који садржи овај блок"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the stream (the first block is 1)"
msgstr "Број блока релативан на почетак тока (први блок је 1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block number relative to the beginning of the file"
msgstr "Број блока релативан на почетак датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed start offset relative to the beginning of the file"
msgstr "Померај почетка запакованог односан на почетак датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Uncompressed start offset relative to the beginning of the file"
msgstr "Померај почетка незапаковане односан на почетак датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total compressed size of the block (includes headers)"
msgstr "Укупна запакована величина блока (заједно са заглављима)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<block> lines.  These are not displayed with a single B<--verbose>, because getting this information requires many seeks and can thus be slow:"
msgstr "Ако је B<--verbose> наведено два пута, додатне колоне су укључене у редовима B<блока>.  Они се не приказују са једним B<--verbose>, јер добијање ове информације захтева много тражења и стога може бити споро:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "11."
msgstr "11."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Value of the integrity check in hexadecimal"
msgstr "Вредност провере целовитости у хексадецималном облику"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "12."
msgstr "12."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block header size"
msgstr "Величина заглавља блока"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "13."
msgstr "13."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Block flags: B<c> indicates that compressed size is present, and B<u> indicates that uncompressed size is present.  If the flag is not set, a dash (B<->)  is shown instead to keep the string length fixed.  New flags may be added to the end of the string in the future."
msgstr "Заставице блока: B<c> указује да је величина запакованог присутна, а B<u> указује да је величина нераспакованог присутна.  Ако заставица није постављена, цртица (B<->) се приказује уместо да се настави са поправком дужине ниске.  У будућности нове заставице могу бити додате на крај ниске."

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "14."
msgstr "14."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Size of the actual compressed data in the block (this excludes the block header, block padding, and check fields)"
msgstr "Величина стварних запакованих података у блоку (ово искључује заглавље блока, попуњавање блока и поља провере)"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "15."
msgstr "15."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this block with this B<xz> version"
msgstr "Количина меморије (у бајтовима) потребна за распакивање овог блока са овим B<xz> издањем"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "16."
msgstr "16."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Filter chain.  Note that most of the options used at compression time cannot be known, because only the options that are needed for decompression are stored in the B<.xz> headers."
msgstr "Ланац филтера.  Знајте да већина опција коришћених за време запакивања не може бити познато, јер се само опције које су потребне за распакивање чувају у B<.xz> заглављима."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<summary> lines:"
msgstr "Колоне редова B<сажетка>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Amount of memory (in bytes) required to decompress this file with this B<xz> version"
msgstr "Количина меморије (у бајтовима) потребна за распакивање ове датотеке са овим B<xz> издањем"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<yes> or B<no> indicating if all block headers have both compressed size and uncompressed size stored in them"
msgstr "B<yes> или B<no> указује да ли сва заглавља блока имају и величину запакованог и величину незапакованог сачуване у њима"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<Since> B<xz> I<5.1.2alpha:>"
msgstr "I<Од> B<xz> I<5.1.2alpha:>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minimum B<xz> version required to decompress the file"
msgstr "Потребно је најмање B<xz> издање за распакивање датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The columns of the B<totals> line:"
msgstr "Колоне редова B<укупности>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of streams"
msgstr "Број токова"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of blocks"
msgstr "Број блокова"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compressed size"
msgstr "Величина запакованог"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Average compression ratio"
msgstr "Просечан степен запакивања"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Comma-separated list of integrity check names that were present in the files"
msgstr "Списак зарезом раздвојених назива провере целовитости која су била присутна у датотекама"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stream padding size"
msgstr "Величина попуњавања тока"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Number of files.  This is here to keep the order of the earlier columns the same as on B<file> lines."
msgstr "Број датотека. Ту се држи поредак ранијих колона исто као у редовима B<датотеке>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If B<--verbose> was specified twice, additional columns are included on the B<totals> line:"
msgstr "Ако је B<--verbose> наведено два пута, додатне колоне су укључене у реду B<укупности>:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Maximum amount of memory (in bytes) required to decompress the files with this B<xz> version"
msgstr "Највећа количина меморије (у бајтима) потребна за распакивање датотеке са овим B<xz> издањем"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Future versions may add new line types and new columns can be added to the existing line types, but the existing columns won't be changed."
msgstr "Будућа издања могу додати нове врсте реда и нове колоне се могу додати постојећим врстама реда, али постојеће колоне се неће променити."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Filters help"
msgstr "Помоћ филтера"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --filters-help> prints the supported filters in the following format:"
msgstr "B<xz --robot --filters-help> исписује подржане филтере у следећем формату:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<filter>B<:>I<option>B<=E<lt>>I<value>B<E<gt>,>I<option>B<=E<lt>>I<value>B<E<gt>>..."
msgstr "I<филтер>B<:>I<опција>B<=E<lt>>I<вредност>B<E<gt>,>I<опција>B<=E<lt>>I<вредност>B<E<gt>>..."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<filter>"
msgstr "I<филтер>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of the filter"
msgstr "Назив филтера"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<option>"
msgstr "I<опција>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Name of a filter specific option"
msgstr "Назив филтера специфичне опције"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<value>"
msgstr "I<вредност>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Numeric I<value> ranges appear as B<E<lt>>I<min>B<->I<max>B<E<gt>>.  String I<value> choices are shown within B<E<lt> E<gt>> and separated by a B<|> character."
msgstr "Опсези бројевних I<вредности> се јављају као B<E<lt>>I<min>B<->I<max>B<E<gt>>.  I<Вредност> ниске избора се приказују унутар B<E<lt> E<gt>> и раздвојене су знаком B<|>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Each filter is printed on its own line."
msgstr "Сваки филтер се исписује на свом сопственом реду."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Memory limit information"
msgstr "Информације о ограничењу меморије"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --info-memory> prints a single line with multiple tab-separated columns:"
msgstr "B<xz --robot --info-memory> исписује један ред са више колона раздвојених табулатором:"

#. type: IP
#: ../src/xz/xz.1
#, no-wrap
msgid "1."
msgstr "1."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Total amount of physical memory (RAM) in bytes."
msgstr "Укупна количина физичке меморије (RAM) у бајтима."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for compression in bytes (B<--memlimit-compress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Ограничење коришћења меморије за запакивање у бајтима (B<--memlimit-compress>).  Специјална вредност B<0> означава основно подешавање које је за режим једне нити исто као без ограничења."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Memory usage limit for decompression in bytes (B<--memlimit-decompress>).  A special value of B<0> indicates the default setting which for single-threaded mode is the same as no limit."
msgstr "Ограничење коришћења меморије за распакивање у бајтима (B<--memlimit-decompress>).  Специјална вредност B<0> означава основно подешавање које је за режим једне нити исто као без ограничења."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Memory usage for multi-threaded decompression in bytes (B<--memlimit-mt-decompress>).  This is never zero because a system-specific default value shown in the column 5 is used if no limit has been specified explicitly.  This is also never greater than the value in the column 3 even if a larger value has been specified with B<--memlimit-mt-decompress>."
msgstr "Од B<xz> 5.3.4alpha: Коришћење меморије за распакивање са више нити у бајтима (B<--memlimit-mt-decompress>).  Ово никада није нула јер систему специфична основна вредност приказана у колони 5 се користи ако ограничење није изричито наведено.  Такође никада није веће од вредности у колони 3 чак и ако је наведена већа вредност са B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: A system-specific default memory usage limit that is used to limit the number of threads when compressing with an automatic number of threads (B<--threads=0>)  and no memory usage limit has been specified (B<--memlimit-compress>).  This is also used as the default value for B<--memlimit-mt-decompress>."
msgstr "Од B<xz> 5.3.4alpha: Систему специфично основно ограничење коришћења меморије које се користи за ограничавање броја нити приликом запакивања са аутоматским бројем нити (B<--threads=0>) и без наведеног ограничења коришћења меморије (B<--memlimit-compress>).  Ово се такође користи као основна вредност за B<--memlimit-mt-decompress>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Since B<xz> 5.3.4alpha: Number of available processor threads."
msgstr "Од B<xz> 5.3.4alpha: Број доступних нити обрађивача."

#. type: Plain text
#: ../src/xz/xz.1
msgid "In the future, the output of B<xz --robot --info-memory> may have more columns, but never more than a single line."
msgstr "У будуће, излаз B<xz --robot --info-memory> може имати више колона, али никада више од једног реда."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Version"
msgstr "Издање"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz --robot --version> prints the version number of B<xz> and liblzma in the following format:"
msgstr "B<xz --robot --version> исписује број издања за B<xz> и „liblzma“ у следећем формату:"

#.  TRANSLATORS: Don't translate the uppercase XZ_VERSION or LIBLZMA_VERSION.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<XZ_VERSION=>I<XYYYZZZS>"
msgstr "B<XZ_VERSION=>I<XYYYZZZS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<LIBLZMA_VERSION=>I<XYYYZZZS>"
msgstr "B<LIBLZMA_VERSION=>I<XYYYZZZS>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<X>"
msgstr "I<X>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Major version."
msgstr "Главно издање."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<YYY>"
msgstr "I<YYY>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Minor version.  Even numbers are stable.  Odd numbers are alpha or beta versions."
msgstr "Споредно издање. Парни бројеви су стабилна а непарни су алфа или бета издања."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<ZZZ>"
msgstr "I<ZZZ>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Patch level for stable releases or just a counter for development releases."
msgstr "Ниво закрпе за стабилна издања или само бројач за развојна издања."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "I<S>"
msgstr "I<S>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Stability.  0 is alpha, 1 is beta, and 2 is stable.  I<S> should be always 2 when I<YYY> is even."
msgstr "Стабилност.  0 је алфа, 1 је бета, а 2 је стабилно.  I<S> треба увек да буде 2 када је I<YYY> парно."

#. type: Plain text
#: ../src/xz/xz.1
msgid "I<XYYYZZZS> are the same on both lines if B<xz> and liblzma are from the same XZ Utils release."
msgstr "I<XYYYZZZS> су исти у оба реда ако су B<xz> и „liblzma“ из истог издања XZ Utils-а."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Examples: 4.999.9beta is B<49990091> and 5.0.0 is B<50000002>."
msgstr "Примери: 4.999.9beta је B<49990091> и 5.0.0 је B<50000002>."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1
#, no-wrap
msgid "EXIT STATUS"
msgstr "СТАЊЕ ИЗЛАЗА"

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<0>"
msgstr "B<0>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/lzmainfo/lzmainfo.1
msgid "All is good."
msgstr "Све је у реду."

#. type: TP
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "B<1>"
msgstr "B<1>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "An error occurred."
msgstr "Дошло је до грешке."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<2>"
msgstr "B<2>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Something worth a warning occurred, but no actual errors occurred."
msgstr "Десило се нешто вредно упозорења, али није дошло до стварних грешака."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Notices (not warnings or errors) printed on standard error don't affect the exit status."
msgstr "Обавештења (без упозорења или грешака) исписано на стандардној грешци не утичу на стање излаза."

#. type: SH
#: ../src/xz/xz.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "ENVIRONMENT"
msgstr "ОКРУЖЕЊЕ"

#.  TRANSLATORS: Don't translate the uppercase XZ_DEFAULTS or XZ_OPT.
#.  They are names of environment variables.
#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> parses space-separated lists of options from the environment variables B<XZ_DEFAULTS> and B<XZ_OPT>, in this order, before parsing the options from the command line.  Note that only options are parsed from the environment variables; all non-options are silently ignored.  Parsing is done with B<getopt_long>(3)  which is used also for the command line arguments."
msgstr "B<xz> обрађује размаком одвојени списак опција из променљивих окружења B<XZ_DEFAULTS> и B<XZ_OPT>, тим редом, пре обраде опција са линије наредби.  Знајте да се обрађују само опције из променљивих окружења; све што нису опције се тихо занемарује.  Обрада се ради са B<getopt_long>(3) која се користи такође за аргументе линије наредби."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<Warning:> By setting these environment variables, one is effectively modifying programs and scripts that run B<xz>.  Most of the time it is safe to set memory usage limits, number of threads, and compression options via the environment variables.  However, some options can break scripts.  An obvious example is B<--help> which makes B<xz> show the help text instead of compressing or decompressing a file.  More subtle examples are B<--quiet> and B<--verbose>.  In many cases it works well to enable the progress indicator using B<--verbose>, but in some situations the extra messages create problems.  The verbosity level also affects the behavior of B<--list>."
msgstr "B<Упозорење:> Постављањем ових променљивих окружења, ефективно мењате програме и скрипте које покреће B<xz>.  У већини случајева је безбедно поставити ограничење коришћења, број нити и опција паковања путем променљивих окружења.  Међутим, неке опције могу да уруше скрипте.  Очигледан пример је B<--help> која чини да B<xz> прикаже текст помоћи уместо да запакује или распакује датотеку.  Суптилнији примери су B<--quiet> и B<--verbose>.  У многим случајевима добро функционише омогућавање указивача напредовања коришћењем B<--verbose>, али у неким ситуацијама додатне поруке стварају проблеме. Ниво опширности такође утиче на понашање B<--list>"

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_DEFAULTS>"
msgstr "B<XZ_DEFAULTS>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "User-specific or system-wide default options.  Typically this is set in a shell initialization script to enable B<xz>'s memory usage limiter by default or set the default number of threads.  Excluding shell initialization scripts and similar special cases, scripts should never set or unset B<XZ_DEFAULTS>."
msgstr "Кориснику специфичне или свеопште системске основне опције.  Обично је ово постављено у скрипти покретања конзоле за укључивање B<xz> ограничавача коришћења меморије по основи или за постављање основног броја нити.  Искључивање скрипти покретања конзоле и сличних специјалних случајева, скрипте не смеју никада да поставе или пониште B<XZ_DEFAULTS>."

#. type: TP
#: ../src/xz/xz.1
#, no-wrap
msgid "B<XZ_OPT>"
msgstr "B<XZ_OPT>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "This is for passing options to B<xz> when it is not possible to set the options directly on the B<xz> command line.  This is the case when B<xz> is run by a script or tool, for example, GNU B<tar>(1):"
msgstr "Ово је за прослеђивање опција ка B<xz> када није могуће поставити опције директно на B<xz> линији наредби.  Ово је случај када је B<xz> покренут скриптом или алатом, на пример, са Гну B<tar>(1):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"
msgstr "\\f(CRXZ_OPT=-2v tar caf foo.tar.xz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Scripts may use B<XZ_OPT>, for example, to set script-specific default compression options.  It is still recommended to allow users to override B<XZ_OPT> if that is reasonable.  For example, in B<sh>(1)  scripts one may use something like this:"
msgstr "Скрипте могу да користе B<XZ_OPT>, на пример, за постављање основних опција запакивања специфичних скрипти.  Још увек се препоручује омогућавање корисницима да пишу преко B<XZ_OPT> ако је то разумљиво.  На пример, у B<sh>(1) скриптама неко може користити нешто као ово:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"
msgstr ""
"\\f(CRXZ_OPT=${XZ_OPT-\"-7e\"}\n"
"export XZ_OPT\\fR\n"

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA UTILS COMPATIBILITY"
msgstr "LZMA UTILS САДЕЈСТВЕНОСТ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The command line syntax of B<xz> is practically a superset of B<lzma>, B<unlzma>, and B<lzcat> as found from LZMA Utils 4.32.x.  In most cases, it is possible to replace LZMA Utils with XZ Utils without breaking existing scripts.  There are some incompatibilities though, which may sometimes cause problems."
msgstr "Синтакса линије наредби B<xz> је практично супер скуп B<lzma>, B<unlzma> и B<lzcat> као што се налази у LZMA Utils 4.32.x.  У већини случајева, могуће је заменити LZMA Utils са XZ Utils без оштећења постојећих скрипти.  Има неких несагласности такође, које понекад могу довести до проблема."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compression preset levels"
msgstr "Нивои предподешавања запакивања"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The numbering of the compression level presets is not identical in B<xz> and LZMA Utils.  The most important difference is how dictionary sizes are mapped to different presets.  Dictionary size is roughly equal to the decompressor memory usage."
msgstr "Набрајање предподешености нивоа запакивања није исто у B<xz> и LZMA Utils.  Најважнија разлика је у томе како се величине речника мапирају у различитим предподешеностима.  Величина речника је отприлике иста као и коришћење меморије распакивача."

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "Level"
msgstr "Ниво"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "xz"
msgstr "xz"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils"
msgstr "LZMA Utils"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "N/A"
msgstr "Н/Д"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "64 KiB"
msgstr "64 KiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "512 KiB"
msgstr "512 KiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The dictionary size differences affect the compressor memory usage too, but there are some other differences between LZMA Utils and XZ Utils, which make the difference even bigger:"
msgstr "Разлике величине речника делују такође на коришћење меморије запакивање, али постоје још неке разлике између LZMA Utils и XZ Utils, што чини разлике још већим:"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "LZMA Utils 4.32.x"
msgstr "LZMA Utils 4.32.x"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "12 MiB"
msgstr "12 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "26 MiB"
msgstr "26 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "45 MiB"
msgstr "45 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "83 MiB"
msgstr "83 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "159 MiB"
msgstr "159 MiB"

#. type: tbl table
#: ../src/xz/xz.1
#, no-wrap
msgid "311 MiB"
msgstr "311 MiB"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The default preset level in LZMA Utils is B<-7> while in XZ Utils it is B<-6>, so both use an 8 MiB dictionary by default."
msgstr "Основни ниво предподешености у LZMA Utils је B<-7> док у XZ Utils је B<-6>, тако да оба користе 8 MiB речник по основи."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Streamed vs. non-streamed .lzma files"
msgstr "Токовне наспрам не-токовних „.lzma“ датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The uncompressed size of the file can be stored in the B<.lzma> header.  LZMA Utils does that when compressing regular files.  The alternative is to mark that uncompressed size is unknown and use end-of-payload marker to indicate where the decompressor should stop.  LZMA Utils uses this method when uncompressed size isn't known, which is the case, for example, in pipes."
msgstr "Незапакована величина датотеке се може чувати у B<.lzma> заглављу.  LZMA Utils то ради приликом запакивања обичних датотека.  Алтернатива је да се означи та незапакована величина као непозната и да се користи означавач краја утовара да назначи где распакивач треба да стане.  LZMA Utils користи ову методу када незапакована величина није позната, што је случај, на пример, у спојкама."

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xz> supports decompressing B<.lzma> files with or without end-of-payload marker, but all B<.lzma> files created by B<xz> will use end-of-payload marker and have uncompressed size marked as unknown in the B<.lzma> header.  This may be a problem in some uncommon situations.  For example, a B<.lzma> decompressor in an embedded device might work only with files that have known uncompressed size.  If you hit this problem, you need to use LZMA Utils or LZMA SDK to create B<.lzma> files with known uncompressed size."
msgstr "B<xz> подржава распакивање B<.lzma> датотека са или без означавача краја утовара, али ће све B<.lzma> датотеке направљене са B<xz> користити означавач краја утовара и означити незапаковану величину непознатом у B<.lzma> заглављу.  Ово може бити проблем у неким неуобичајеним ситуацијама.  На пример, B<.lzma> распакивач у угнежденом уређају може радити само са датотекама које имају незапаковану величину.  Ако наиђете на тај проблем, треба да користите LZMA Utils или LZMA SDK да направите B<.lzma> датотеке са познатом незапакованом величином."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Unsupported .lzma files"
msgstr "Неподржане „.lzma“ датотеке"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<.lzma> format allows I<lc> values up to 8, and I<lp> values up to 4.  LZMA Utils can decompress files with any I<lc> and I<lp>, but always creates files with B<lc=3> and B<lp=0>.  Creating files with other I<lc> and I<lp> is possible with B<xz> and with LZMA SDK."
msgstr "Формат B<.lzma> омогућава I<lc> вредности све до 8, а I<lp> вредности све до 4.  LZMA Utils може да распакује датотеке са било којим I<lc> и I<lp>, али увек прави датотеке са B<lc=3> и B<lp=0>.  Прављење датотека са другим I<lc> и I<lp> је мгуће са B<xz> и са LZMA SDK."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The implementation of the LZMA1 filter in liblzma requires that the sum of I<lc> and I<lp> must not exceed 4.  Thus, B<.lzma> files, which exceed this limitation, cannot be decompressed with B<xz>."
msgstr "Примена филтера LZMA1 у „liblzma“ захтева да збир I<lc> и I<lp> не сме да премаши 4.  Стога, B<.lzma> датотеке, које премаше ово ограничење, не могу да се распакују са B<xz>."

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA Utils creates only B<.lzma> files which have a dictionary size of 2^I<n> (a power of 2) but accepts files with any dictionary size.  liblzma accepts only B<.lzma> files which have a dictionary size of 2^I<n> or 2^I<n> + 2^(I<n>-1).  This is to decrease false positives when detecting B<.lzma> files."
msgstr "LZMA Utils прави само B<.lzma> датотеке које имају величину речника од 2^I<n> (степен 2) али прихвата датотеке са било којом величином речника.  „liblzma“ прихвата само B<.lzma> датотеке које имају величину речника од 2^I<n> или 2^I<n> + 2^(I<n>-1).  Ово је да се смање лажни позитивни резултати приликом откривања B<.lzma> датотека."

#. type: Plain text
#: ../src/xz/xz.1
msgid "These limitations shouldn't be a problem in practice, since practically all B<.lzma> files have been compressed with settings that liblzma will accept."
msgstr "Ова ограничења не би требало да буду проблем у пракси, јер су практично све B<.lzma> датотеке запаковане са поставкама које ће „liblzma“ прихватити."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Trailing garbage"
msgstr "Пратеће смеће"

#. type: Plain text
#: ../src/xz/xz.1
msgid "When decompressing, LZMA Utils silently ignore everything after the first B<.lzma> stream.  In most situations, this is a bug.  This also means that LZMA Utils don't support decompressing concatenated B<.lzma> files."
msgstr "Приликом распакивања, LZMA Utils тихо занемарује све после првог B<.lzma> тока.  У већини прилика, ово је грешка.  Ово такође значи да LZMA Utils не подржава распакивање надовезаних B<.lzma> датотека."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If there is data left after the first B<.lzma> stream, B<xz> considers the file to be corrupt unless B<--single-stream> was used.  This may break obscure scripts which have assumed that trailing garbage is ignored."
msgstr "Ако су остали подаци након првог B<.lzma> тока, B<xz> сматра да је датотека оштећена осим ако је коришћено B<--single-stream>.  Ово може прекинути нејасне скрипте које су претпоставиле да је пратеће смеће занемарено."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
#, no-wrap
msgid "NOTES"
msgstr "НАПОМЕНЕ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Compressed output may vary"
msgstr "Излаз запакованог може бити променљив"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The exact compressed output produced from the same uncompressed input file may vary between XZ Utils versions even if compression options are identical.  This is because the encoder can be improved (faster or better compression)  without affecting the file format.  The output can vary even between different builds of the same XZ Utils version, if different build options are used."
msgstr "Тачан запаковани излаз добијен из исте незапаковане улазне датотеке може се разликовати између XZ Utils издања чак и ако су опције запакивања истоветне.  Ово је због тога што шифрер може бити побољшан (брже и боље запакивање) а да не утиче на формат датотеке.  Излаз се може разликовати између различитих изградњи истог XZ Utils издања, ако су коришћене другачије опције изградње."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The above means that once B<--rsyncable> has been implemented, the resulting files won't necessarily be rsyncable unless both old and new files have been compressed with the same xz version.  This problem can be fixed if a part of the encoder implementation is frozen to keep rsyncable output stable across xz versions."
msgstr "Ово изнад значи да када се B<--rsyncable> једном примени, резултирајућа датотека неће неопходно бити r-ускладива осим ако и стара и нова датотека нису запаковане истим xz издањем.  Овај проблем може бити поправљен ако је део примене шифрера замрзнут да држи r-ускладив излаз стабилним кроз xz издања."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Embedded .xz decompressors"
msgstr "Угнеждени „.xz“ распакивачи"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Embedded B<.xz> decompressor implementations like XZ Embedded don't necessarily support files created with integrity I<check> types other than B<none> and B<crc32>.  Since the default is B<--check=crc64>, you must use B<--check=none> or B<--check=crc32> when creating files for embedded systems."
msgstr "Примена угнежденог B<.xz> распакивача као што је XZ Embedded неопходно не подржава датотеке направљене врстама I<провере> целовитости осим B<none> и B<crc32>.  Како је основно B<--check=crc64>, морате да користите B<--check=none> или B<--check=crc32> када правите датотеке за угнеждене системе."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Outside embedded systems, all B<.xz> format decompressors support all the I<check> types, or at least are able to decompress the file without verifying the integrity check if the particular I<check> is not supported."
msgstr "Споља уграђени системи, сви распакивачи B<.xz> формата подржавају све врсте I<провере>, или барем могу да распакују датотеку без испитивања провере целовитости ако одређена I<провера> није подржана."

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Embedded supports BCJ filters, but only with the default start offset."
msgstr "XZ Embedded подржава BCJ филтере, али само са основним померајем почетка."

#. type: SH
#: ../src/xz/xz.1
#, no-wrap
msgid "EXAMPLES"
msgstr "ПРИМЕРИ"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Basics"
msgstr "Основно"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Compress the file I<foo> into I<foo.xz> using the default compression level (B<-6>), and remove I<foo> if compression is successful:"
msgstr "Запакује датотеку I<foo> у I<foo.xz> користећи основни ниво запакивања (B<-6>), и уклања I<foo> ако је запакивање успешно:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz foo\\fR\n"
msgstr "\\f(CRxz foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Decompress I<bar.xz> into I<bar> and don't remove I<bar.xz> even if decompression is successful:"
msgstr "Распакује I<bar.xz> у I<bar> и не уклања I<bar.xz> чак и када је распакивање успешно:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dk bar.xz\\fR\n"
msgstr "\\f(CRxz -dk bar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Create I<baz.tar.xz> with the preset B<-4e> (B<-4 --extreme>), which is slower than the default B<-6>, but needs less memory for compression and decompression (48\\ MiB and 5\\ MiB, respectively):"
msgstr "Ствара I<baz.tar.xz> са предподешавањем B<-4e> (B<-4 --extreme>), које је спорије него основно B<-6>, али захтева мање меморије за запакивање и распакивање (48\\ MiB и 5\\ MiB):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"
msgstr "\\f(CRtar cf - baz | xz -4e E<gt> baz.tar.xz\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A mix of compressed and uncompressed files can be decompressed to standard output with a single command:"
msgstr "Мешавина сажетих и несажетих датотека се може распаковати на стандардни излаз једном наредбом:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"
msgstr "\\f(CRxz -dcf a.txt b.txt.xz c.txt d.txt.lzma E<gt> abcd.txt\\fR\n"

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Parallel compression of many files"
msgstr "Паралелно запакивање више датотека"

#. type: Plain text
#: ../src/xz/xz.1
msgid "On GNU and *BSD, B<find>(1)  and B<xargs>(1)  can be used to parallelize compression of many files:"
msgstr "На GNU и *BSD, B<find>(1) и B<xargs>(1) се могу користити за паралелно запакивање више датотека:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"
msgstr ""
"\\f(CRfind . -type f \\e! -name '*.xz' -print0 \\e\n"
"    | xargs -0r -P4 -n16 xz -T1\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The B<-P> option to B<xargs>(1)  sets the number of parallel B<xz> processes.  The best value for the B<-n> option depends on how many files there are to be compressed.  If there are only a couple of files, the value should probably be 1; with tens of thousands of files, 100 or even more may be appropriate to reduce the number of B<xz> processes that B<xargs>(1)  will eventually create."
msgstr "Опција B<-P> за B<xargs>(1) поставља број паралелних B<xz> процеса.  Најбоља вредност за опцију B<-n> зависи од тога колико датотека има за запакивање.  Ако има само неколико датотека, вредност би  вероватно требала да буде 1; са више хиљада датотека, 100 или чак и више може бити одговарајуће за смањење броја B<xz> процеса које би B<xargs>(1) евентуално направио."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The option B<-T1> for B<xz> is there to force it to single-threaded mode, because B<xargs>(1)  is used to control the amount of parallelization."
msgstr "Опција B<-T1> за B<xz> је ту да га примора на једнонитни режим, јер се B<xargs>(1) користи за контролу количине паралелизације."

#. type: SS
#: ../src/xz/xz.1
#, no-wrap
msgid "Robot mode"
msgstr "Режим робота"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Calculate how many bytes have been saved in total after compressing multiple files:"
msgstr "Израчунава колико је бајтова укупно сачувано након запакивања више датотека:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"
msgstr "\\f(CRxz --robot --list *.xz | awk '/^totals/{print $5-$4}'\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "A script may want to know that it is using new enough B<xz>.  The following B<sh>(1)  script checks that the version number of the B<xz> tool is at least 5.0.0.  This method is compatible with old beta versions, which didn't support the B<--robot> option:"
msgstr "Скрипта може желети да зна да користи најновији B<xz>.  Следећа скрипта B<sh>(1)  проверава да ли је број издања B<xz> алата барем 5.0.0.  Ова метода је сагласна са старим бета издањима која не подржавају B<--robot> опцију:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Your xz is too old.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"
msgstr ""
"\\f(CRif ! eval \"$(xz --robot --version 2E<gt> /dev/null)\" ||\n"
"        [ \"$XZ_VERSION\" -lt 50000002 ]; then\n"
"    echo \"Ваш „xz“ је превише стар.\"\n"
"fi\n"
"unset XZ_VERSION LIBLZMA_VERSION\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Set a memory usage limit for decompression using B<XZ_OPT>, but if a limit has already been set, don't increase it:"
msgstr "Поставља ограничење коришћења меморије за распакивање коришћењем B<XZ_OPT>, али ако је ограничење већ постављено, не повећава је:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"
msgstr ""
"\\f(CRNEWLIM=$((123 E<lt>E<lt> 20))\\ \\ # 123 MiB\n"
"OLDLIM=$(xz --robot --info-memory | cut -f3)\n"
"if [ $OLDLIM -eq 0 -o $OLDLIM -gt $NEWLIM ]; then\n"
"    XZ_OPT=\"$XZ_OPT --memlimit-decompress=$NEWLIM\"\n"
"    export XZ_OPT\n"
"fi\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "The simplest use for custom filter chains is customizing a LZMA2 preset.  This can be useful, because the presets cover only a subset of the potentially useful combinations of compression settings."
msgstr "Најједноставнија употреба за произвољне ланце филтера прилагођава LZMA2 предподешеност.  Ово може бити корисно, јер предподешености покривају само подскуп потенцијално корисних комбинација поставки запакивања."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The CompCPU columns of the tables from the descriptions of the options B<-0> ... B<-9> and B<--extreme> are useful when customizing LZMA2 presets.  Here are the relevant parts collected from those two tables:"
msgstr "Колоне ЗапакЦПЈ у табелама из описа опција B<-0> ... B<-9> и B<--extreme> су корисне приликом прилагођавања LZMA2 предподешености.  Овде су значајни делови прикупљени из те две табеле:"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you know that a file requires somewhat big dictionary (for example, 32\\ MiB) to compress well, but you want to compress it quicker than B<xz -8> would do, a preset with a low CompCPU value (for example, 1)  can be modified to use a bigger dictionary:"
msgstr "Ако знате да датотека захтева нешто велики речник (на пример, 32 \\ MiB) за добро запакивање, али желите да је запакујете брже него што би то B<xz -8> урадио, предподешеност са ниском вредношћу ЗапакМем (на пример, 1) може се изменити да користи већи речник:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=1,dict=32MiB foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "With certain files, the above command may be faster than B<xz -6> while compressing significantly better.  However, it must be emphasized that only some files benefit from a big dictionary while keeping the CompCPU value low.  The most obvious situation, where a big dictionary can help a lot, is an archive containing very similar files of at least a few megabytes each.  The dictionary size has to be significantly bigger than any individual file to allow LZMA2 to take full advantage of the similarities between consecutive files."
msgstr "Са одређеним датотекама, горња наредба може бити бржа од B<xz -6> а запакивање значајно боље.  Међутим, мора бити наглашено да само неке датотеке имају корист од великог речника док одржавају вредност ЗапакЦПЈ ниском.  Најочигледнија ситуација, у којој велики речник може доста помоћи, јесте архива која садржи врло сличне датотеке где свака има најмање неколико мегабајта.  Величина речника треба да буде значајно већа од сваке засебне датотеке да би се омогућило да LZMA2 има потпуну предност од сличности између узастопних датотека."

#. type: Plain text
#: ../src/xz/xz.1
msgid "If very high compressor and decompressor memory usage is fine, and the file being compressed is at least several hundred megabytes, it may be useful to use an even bigger dictionary than the 64 MiB that B<xz -9> would use:"
msgstr "Ако је веома велико коришћење меморије запакивача и распакивача у реду, а датотека која се запакује је барем неколико стотина мегабајта, може бити корисно користити још већи речник од 64 MiB који ће B<xz -9> користити:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"
msgstr "\\f(CRxz -vv --lzma2=dict=192MiB big_foo.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using B<-vv> (B<--verbose --verbose>)  like in the above example can be useful to see the memory requirements of the compressor and decompressor.  Remember that using a dictionary bigger than the size of the uncompressed file is waste of memory, so the above command isn't useful for small files."
msgstr "Коришћење B<-vv> (B<--verbose --verbose>) као у горњем примеру може бити корисно да се виде захтеви за меморијом запакивача и распакивача.  Знајте да је коришћење речника већег од величине незапаковане датотеке губитак меморије, тако да горња наредба није корисна за мале датотеке."

#. type: Plain text
#: ../src/xz/xz.1
msgid "Sometimes the compression time doesn't matter, but the decompressor memory usage has to be kept low, for example, to make it possible to decompress the file on an embedded system.  The following command uses B<-6e> (B<-6 --extreme>)  as a base and sets the dictionary to only 64\\ KiB.  The resulting file can be decompressed with XZ Embedded (that's why there is B<--check=crc32>)  using about 100\\ KiB of memory."
msgstr "Понекад време запакивања није важно, али коришћење меморије распакивача треба да се одржава ниским, на пример, да би се учинило могућим распакивање датотеке на угнежденом систему,  Следеће наредбе користе B<-6e> (B<-6 --extreme>) као основу и постављају речник на само 64\\ KiB.  Резултујућа датотека се може распаковати са XZ Embedded (због тога је ту B<--check=crc32>) користећи око 100\\ KiB меморије."

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"
msgstr "\\f(CRxz --check=crc32 --lzma2=preset=6e,dict=64KiB foo\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If you want to squeeze out as many bytes as possible, adjusting the number of literal context bits (I<lc>)  and number of position bits (I<pb>)  can sometimes help.  Adjusting the number of literal position bits (I<lp>)  might help too, but usually I<lc> and I<pb> are more important.  For example, a source code archive contains mostly US-ASCII text, so something like the following might give slightly (like 0.1\\ %) smaller file than B<xz -6e> (try also without B<lc=4>):"
msgstr "Ако желите да исцедите што је више могуће бајтова, дотеривање броја битова контекста литерала (I<lc>) и броја битова положаја (I<pb>) може понекад да помогне.  Дотеривање броја битова положаја литерала (I<lp>) може такође да помогне, али обично I<lc> и I<pb> су важнији.  На пример, архива извора кода садржи углавном US-ASCII текст, тако да нешто као следеће може да да незнатно (око 0.1\\ %) мању датотеку него B<xz -6e> (пробајте такође без B<lc=4>):"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"
msgstr "\\f(CRxz --lzma2=preset=6e,pb=0,lc=4 source_code.tar\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Using another filter together with LZMA2 can improve compression with certain file types.  For example, to compress a x86-32 or x86-64 shared library using the x86 BCJ filter:"
msgstr "Коришћење још једног филтера заједно са LZMA2 може побољшати запакивање са одређеним врстама датотека.  На пример, за запакивање x86-32 или x86-64 дељене библиотеке коришћењем x86 BCJ филтера:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"
msgstr "\\f(CRxz --x86 --lzma2 libfoo.so\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "Note that the order of the filter options is significant.  If B<--x86> is specified after B<--lzma2>, B<xz> will give an error, because there cannot be any filter after LZMA2, and also because the x86 BCJ filter cannot be used as the last filter in the chain."
msgstr "Знајте да поредак опција филтера има значај.  Ако је B<--x86> наведено након B<--lzma2>, B<xz> ће дати грешку, јер не сме бити никаквог филтера после LZMA2, и такође зато што се x86 BCJ филтер не може користити као последњи филтер у ланцу."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The Delta filter together with LZMA2 can give good results with bitmap images.  It should usually beat PNG, which has a few more advanced filters than simple delta but uses Deflate for the actual compression."
msgstr "Delta филтер заједно са LZMA2 може дати добре резултате са битмап сликама.  Требало би обично да надјача PNG, који има нешто више напредних филтера него једноставни делта али користи Deflate за стварно запакивање."

#. type: Plain text
#: ../src/xz/xz.1
msgid "The image has to be saved in uncompressed format, for example, as uncompressed TIFF.  The distance parameter of the Delta filter is set to match the number of bytes per pixel in the image.  For example, 24-bit RGB bitmap needs B<dist=3>, and it is also good to pass B<pb=0> to LZMA2 to accommodate the three-byte alignment:"
msgstr "Слика треба да се сачува у незапакованом формату, на пример, као незапакована TIFF.  Параметар растојања Delta филтера је постављен да се подудари са бројем бајтова по пикселу у слици.  На пример, RGB битмапа од 24 бита захтева B<dist=3>, а такође је добро проследити B<pb=0> за LZMA2 да се прилагоди поравнање од три бајта:"

#. type: Plain text
#: ../src/xz/xz.1
#, no-wrap
msgid "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"
msgstr "\\f(CRxz --delta=dist=3 --lzma2=pb=0 foo.tiff\\fR\n"

#. type: Plain text
#: ../src/xz/xz.1
msgid "If multiple images have been put into a single archive (for example, B<.tar>), the Delta filter will work on that too as long as all images have the same number of bytes per pixel."
msgstr "Ако је више слика стављено у једну архиву (на пример, B<.tar>), Delta филтер ће радити на томе такође све док све слике не буду имале исти број бајтова по пикселу."

#. type: SH
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "SEE ALSO"
msgstr "ВИДИТЕ ТАКОЂЕ"

#. type: Plain text
#: ../src/xz/xz.1
msgid "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"
msgstr "B<xzdec>(1), B<xzdiff>(1), B<xzgrep>(1), B<xzless>(1), B<xzmore>(1), B<gzip>(1), B<bzip2>(1), B<7z>(1)"

#. type: Plain text
#: ../src/xz/xz.1
msgid "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"
msgstr "XZ Utils: E<lt>https://tukaani.org/xz/E<gt>"

#. type: Plain text
#: ../src/xz/xz.1 ../src/xzdec/xzdec.1
msgid "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"
msgstr "XZ Embedded: E<lt>https://tukaani.org/xz/embedded.htmlE<gt>"

#. type: Plain text
#: ../src/xz/xz.1
msgid "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"
msgstr "LZMA SDK: E<lt>https://7-zip.org/sdk.htmlE<gt>"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "XZDEC"
msgstr "XZDEC"

#. type: TH
#: ../src/xzdec/xzdec.1
#, no-wrap
msgid "2024-04-08"
msgstr "08.04.2024."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "xzdec, lzmadec - Small .xz and .lzma decompressors"
msgstr "xzdec, lzmadec – Мали „.xz“ и „.lzma“ распакивачи"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> [I<option...>] [I<file...>]"
msgstr "B<xzdec> [I<опција...>] [I<датотека...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<lzmadec> [I<option...>] [I<file...>]"
msgstr "B<lzmadec> [I<опција...>] [I<датотека...>]"

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> is a liblzma-based decompression-only tool for B<.xz> (and only B<.xz>)  files.  B<xzdec> is intended to work as a drop-in replacement for B<xz>(1)  in the most common situations where a script has been written to use B<xz --decompress --stdout> (and possibly a few other commonly used options) to decompress B<.xz> files.  B<lzmadec> is identical to B<xzdec> except that B<lzmadec> supports B<.lzma> files instead of B<.xz> files."
msgstr "B<xzdec> је алат само за распакивање заснован на „liblzma“ за B<.xz> (и само B<.xz>) датотеке.  B<xzdec> је намењен да ради као тренутна замена за B<xz>(1) у најопштијим ситуацијама где је скрипта написана да користи B<xz --decompress --stdout> (и по могућству још неколико других обично коришћених опција) за распакивање B<.xz> датотека.  B<lzmadec> је истоветна са B<xzdec> осим што B<lzmadec> подржава B<.lzma> датотеке уместо of B<.xz> датотека."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "To reduce the size of the executable, B<xzdec> doesn't support multithreading or localization, and doesn't read options from B<XZ_DEFAULTS> and B<XZ_OPT> environment variables.  B<xzdec> doesn't support displaying intermediate progress information: sending B<SIGINFO> to B<xzdec> does nothing, but sending B<SIGUSR1> terminates the process instead of displaying progress information."
msgstr "За смањење величине извршне, B<xzdec> не подржава више нити или локализацију, и не чита опције из променљивих окружења B<XZ_DEFAULTS> и B<XZ_OPT>.  B<xzdec> не подржава приказивање посредничких информација напредовања: слање B<SIGINFO> ка B<xzdec> не ради ништа, али слање B<SIGUSR1> окончава процес уместо да прикаже информације о напредовању."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> supports only decompression."
msgstr "Занемарено зарад B<xz>(1) сагласности.  B<xzdec> подржава само распакивање."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never creates or removes any files."
msgstr "Занемарено зарад B<xz>(1) сагласности.  B<xzdec> никада не прави или уклања датотеке."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> always writes the decompressed data to standard output."
msgstr "Занемарено зарад B<xz>(1) сагласности.  B<xzdec> увек пише распаковане податке на стандардни излаз."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Specifying this once does nothing since B<xzdec> never displays any warnings or notices.  Specify this twice to suppress errors."
msgstr "Навођењем овога једном не ради ништа јер B<xzdec> никада не приказује упозорења или обавештења.  Наведите ово два пута да потиснете грешке."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Ignored for B<xz>(1)  compatibility.  B<xzdec> never uses the exit status 2."
msgstr "Занемарено зарад B<xz>(1) сагласности.  B<xzdec> никада не користи излазно стање 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display a help message and exit successfully."
msgstr "Приказује поруку помоћи и излази с успехом."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Display the version number of B<xzdec> and liblzma."
msgstr "Приказује број издања за B<xzdec> и „liblzma“."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "All was good."
msgstr "Све беше у реду."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> doesn't have any warning messages like B<xz>(1)  has, thus the exit status 2 is not used by B<xzdec>."
msgstr "B<xzdec> нема поруке упозорења као што има B<xz>(1), стога B<xzdec> не користи излазно стање 2."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "Use B<xz>(1)  instead of B<xzdec> or B<lzmadec> for normal everyday use.  B<xzdec> or B<lzmadec> are meant only for situations where it is important to have a smaller decompressor than the full-featured B<xz>(1)."
msgstr "Користите B<xz>(1) уместо B<xzdec> или B<lzmadec> за обично свакодневно коришћење.  B<xzdec> или B<lzmadec> су замишљени само ситуације у којима је важно имати мањи распакивач неко потпуно функционалан B<xz>(1)."

#. type: Plain text
#: ../src/xzdec/xzdec.1
msgid "B<xzdec> and B<lzmadec> are not really that small.  The size can be reduced further by dropping features from liblzma at compile time, but that shouldn't usually be done for executables distributed in typical non-embedded operating system distributions.  If you need a truly small B<.xz> decompressor, consider using XZ Embedded."
msgstr "B<xzdec> и B<lzmadec> нису баш тако мали.  Величина може бити још смањена одбацивањем функција из „liblzma“ за време превођења, али то не треба обично да се ради за извршне достављене у типичним не-угнежденим дистрибуцијама оперативног система.  Ако вам треба стварно мали B<.xz> распакивач, размотрите коришћење XZ Embedded-а."

#. type: Plain text
#: ../src/xzdec/xzdec.1 ../src/lzmainfo/lzmainfo.1
msgid "B<xz>(1)"
msgstr "B<xz>(1)"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "LZMAINFO"
msgstr "LZMAINFO"

#. type: TH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "2013-06-30"
msgstr "30.06.2013."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "lzmainfo - show information stored in the .lzma file header"
msgstr "lzmainfo – приказује информације смештене у заглављу .lzma датотеке"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> [B<--help>] [B<--version>] [I<file...>]"
msgstr "B<lzmainfo> [B<--help>] [B<--version>] [I<датотека...>]"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> shows information stored in the B<.lzma> file header.  It reads the first 13 bytes from the specified I<file>, decodes the header, and prints it to standard output in human readable format.  If no I<files> are given or I<file> is B<->, standard input is read."
msgstr "B<lzmainfo> приказује информације које се налазе у заглављу B<.lzma> датотеке.  Чита првих 13 бајтова из наведене I<датотеке>, дешифрује заглавље и исписује га на стандардни излаз у облику читљивом људима.  Ако ни једна I<датотека> није дата или је I<датотека> B<->, чита се стандардни улаз."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "Usually the most interesting information is the uncompressed size and the dictionary size.  Uncompressed size can be shown only if the file is in the non-streamed B<.lzma> format variant.  The amount of memory required to decompress the file is a few dozen kilobytes plus the dictionary size."
msgstr "Обично најзанимљивије информације су незапакована величина и величина речника.  Незапакована величина може се приказати само ако је датотека у варијанти формата B<.lzma> која није ток.  Количина меморије потребне за распакивање датотеке је неколико десетина килобајта плус величина речника."

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> is included in XZ Utils primarily for backward compatibility with LZMA Utils."
msgstr "B<lzmainfo> је садржано у XZ Utils-у првенствено због повратне сагласности са  LZMA Utils-ом."

#. type: SH
#: ../src/lzmainfo/lzmainfo.1
#, no-wrap
msgid "BUGS"
msgstr "ГРЕШКЕ"

#. type: Plain text
#: ../src/lzmainfo/lzmainfo.1
msgid "B<lzmainfo> uses B<MB> while the correct suffix would be B<MiB> (2^20 bytes).  This is to keep the output compatible with LZMA Utils."
msgstr "B<lzmainfo> користи B<MB> док тачан суфикс треба да буде B<MiB> (2^20 бајтова). Ово је зато да би се задржала сагласност излаза са LZMA Utils-ом."

#. type: TH
#: ../src/scripts/xzdiff.1
#, no-wrap
msgid "XZDIFF"
msgstr "XZDIFF"

#. type: TH
#: ../src/scripts/xzdiff.1 ../src/scripts/xzgrep.1 ../src/scripts/xzless.1
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "2025-03-06"
msgstr "06.03.2025."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "xzcmp, xzdiff, lzcmp, lzdiff - compare compressed files"
msgstr "xzcmp, xzdiff, lzcmp, lzdiff – упоређује запаковане датотеке"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> [I<option...>] I<file1> [I<file2>]"
msgstr "B<xzcmp> [I<опција...>] I<датотека1> [I<датотека2>]"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzdiff> \\&..."
msgstr "B<xzdiff> \\&..."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzcmp> \\&...  (DEPRECATED)"
msgstr "B<lzcmp> \\&...  (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<lzdiff> \\&...  (DEPRECATED)"
msgstr "B<lzdiff> \\&...  (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<xzcmp> and B<xzdiff> compare uncompressed contents of two files.  Uncompressed data and options are passed to B<cmp>(1)  or B<diff>(1)  unless B<--help> or B<--version> is specified."
msgstr "B<xzcmp> и B<xzdiff> пореди незапаковане садржаје две датотеке.  Незапаковани подаци и опције се прослеђују ка B<cmp>(1) или B<diff>(1) осим ако није наведено B<--help> or B<--version>."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If both I<file1> and I<file2> are specified, they can be uncompressed files or files in formats that B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  can decompress.  The required decompression commands are determined from the filename suffixes of I<file1> and I<file2>.  A file with an unknown suffix is assumed to be either uncompressed or in a format that B<xz>(1)  can decompress."
msgstr "Ако су наведене и I<датотека1> и I<датотека2>, оне могу бити незапаковане датотеке или датотеке у форматима које B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) или B<lz4>(1) могу да распакују.  Захтеване наредбе распакивања се одређују из суфикса назива I<датотеке1> и I<датотеке2>.  Датотека са непознатим суфиксом се претпоставља да је незапакована или да је у формату који B<xz>(1) може да распакује."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If only one filename is provided, I<file1> must have a suffix of a supported compression format and the name for I<file2> is assumed to be I<file1> with the compression format suffix removed."
msgstr "Ако је достављен само један назив датотеке, I<датотека1> мора да има суфикс подржаних формата запакивања а назив за I<датотеку2> се подразумева да је I<датотека1> са уклоњеним суфиксом формата запакивања."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "The commands B<lzcmp> and B<lzdiff> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Наредбе B<lzcmp> и B<lzdiff> се достављају зарад назадне сагласности са LZMA Utils. Застареле су и биће уклоњене у будућем издању."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "If a decompression error occurs, the exit status is B<2>.  Otherwise the exit status of B<cmp>(1)  or B<diff>(1)  is used."
msgstr "Ако дође до грешке распакивања,излазно стање је B<2>.  У супротном излазно стање од B<cmp>(1)  или B<diff>(1)  се користи."

#. type: Plain text
#: ../src/scripts/xzdiff.1
msgid "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"
msgstr "B<cmp>(1), B<diff>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1)"

#. type: TH
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "XZGREP"
msgstr "XZGREP"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "xzgrep - search possibly-compressed files for patterns"
msgstr "xzgrep – претражује могуће запаковане датотеке за шаблонима"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> [I<option...>] [I<pattern_list>] [I<file...>]"
msgstr "B<xzgrep> [I<опција...>] [I<списак_шаблона>] [I<датотека...>]"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> \\&..."
msgstr "B<xzegrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzfgrep> \\&..."
msgstr "B<xzfgrep> \\&..."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzgrep> \\&...  (DEPRECATED)"
msgstr "B<lzgrep> \\&...  (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzegrep> \\&...  (DEPRECATED)"
msgstr "B<lzegrep> \\&...  (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<lzfgrep> \\&...  (DEPRECATED)"
msgstr "B<lzfgrep> \\&...  (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzgrep> invokes B<grep>(1)  on uncompressed contents of files.  The formats of the I<files> are determined from the filename suffixes.  Any I<file> with a suffix supported by B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), or B<lz4>(1)  will be decompressed; all other files are assumed to be uncompressed."
msgstr "B<xzgrep> призива B<grep>(1) на незапакованим садржајима датотека.  Формати I<датотека> се одређују из суфикса назива датотека.  Било која I<датотека> са суфиксом подржаним од стране B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1) или B<lz4>(1) биће распакована; све остале датотеке се подразумевају да нису запаковане."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If no I<files> are specified or I<file> is B<-> then standard input is read.  When reading from standard input, only files supported by B<xz>(1)  are decompressed.  Other files are assumed to be in uncompressed form already."
msgstr "Ако I<датотеке> нису наведене или I<датотека> је B<-> тада се чита стандардни улаз.  Приликом читања са стандардног улаза, само датотеке подржане са B<xz>(1) се распакују.  Друге датотеке се подразумева да су већ у незапакованом облику."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "Most I<options> of B<grep>(1)  are supported.  However, the following options are not supported:"
msgstr "Већина I<опција> B<grep>(1) је подржана. Међутим, следеће опције нису подржане:"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-r>, B<--recursive>"
msgstr "B<-r>, B<--recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-R>, B<--dereference-recursive>"
msgstr "B<-R>, B<--dereference-recursive>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-d>, B<--directories=>I<action>"
msgstr "B<-d>, B<--directories=>I<радња>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-Z>, B<--null>"
msgstr "B<-Z>, B<--null>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<-z>, B<--null-data>"
msgstr "B<-z>, B<--null-data>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--include=>I<glob>"
msgstr "B<--include=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude=>I<glob>"
msgstr "B<--exclude=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-from=>I<file>"
msgstr "B<--exclude-from=>I<датотека>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<--exclude-dir=>I<glob>"
msgstr "B<--exclude-dir=>I<glob>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<xzegrep> is an alias for B<xzgrep -E>.  B<xzfgrep> is an alias for B<xzgrep -F>."
msgstr "B<xzegrep> је алијас за B<xzgrep -E>.  B<xzfgrep> је алијас за B<xzgrep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "The commands B<lzgrep>, B<lzegrep>, and B<lzfgrep> are provided for backward compatibility with LZMA Utils.  They are deprecated and will be removed in a future version."
msgstr "Наредбе B<lzgrep>, B<lzegrep> и B<lzfgrep> се достављају зарад назадне сагласности са LZMA Utils. Застареле су и биће уклоњене у будућем издању."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "At least one match was found from at least one of the input files.  No errors occurred."
msgstr "Најмање једно поклапање је пронађено из барем једне од улазних датотека.  Није било грешака."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "No matches were found from any of the input files.  No errors occurred."
msgstr "Никаква поклапања нису пронађена ни из једне од улазних датотека. Није дошло до грешака."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "E<gt>1"
msgstr "E<gt>1"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "One or more errors occurred.  It is unknown if matches were found."
msgstr "Дошло је до једне или више грешака.  Није познато да ли су поклапања нађена."

#. type: TP
#: ../src/scripts/xzgrep.1
#, no-wrap
msgid "B<GREP>"
msgstr "B<GREP>"

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "If B<GREP> is set to a non-empty value, it is used instead of B<grep>, B<grep -E>, or B<grep -F>."
msgstr "Ако је B<GREP> постављено на не-празну вредност, користи се уместо B<grep>, B<grep -E> или B<grep -F>."

#. type: Plain text
#: ../src/scripts/xzgrep.1
msgid "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"
msgstr "B<grep>(1), B<xz>(1), B<gzip>(1), B<bzip2>(1), B<lzop>(1), B<zstd>(1), B<lz4>(1), B<zgrep>(1)"

#. type: TH
#: ../src/scripts/xzless.1
#, no-wrap
msgid "XZLESS"
msgstr "XZLESS"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "xzless, lzless - view xz or lzma compressed (text) files"
msgstr "xzless, lzless – приказује „xz“ или „lzma“ запаковане (текстуалне) датотеке"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> [I<file>...]"
msgstr "B<xzless> [I<датотека>...]"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<lzless> [I<file>...] (DEPRECATED)"
msgstr "B<lzless> [I<датотека>...] (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> is a filter that displays text from compressed files to a terminal.  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzless> reads from standard input."
msgstr "B<xzless> је филтер који приказује текст из запакованих датотека у терминалу.  Датотеке које подржава B<xz>(1) се распакују; остале датотеке се већ подразумева да су у незапакованом облику.  Ако није дата ни једна I<датотека>, B<xzless> чита са стандардног улаза."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<xzless> uses B<less>(1)  to present its output.  Unlike B<xzmore>, its choice of pager cannot be altered by setting an environment variable.  Commands are based on both B<more>(1)  and B<vi>(1)  and allow back and forth movement and searching.  See the B<less>(1)  manual for more information."
msgstr "B<xzless> користи B<less>(1) да представи свој излаз.  За разлику од B<xzmore>, његов избор страничара се не може изменити постављањем променљиве окружења.  Наредбе се заснивају и на B<more>(1) и на B<vi>(1) и дозвољавају померање и претрагу назад и напред.  Видите упутство B<less>(1) за више информација."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "The command named B<lzless> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Наредба B<lzless> се доставља зарад назадне сагласности са LZMA Utils. Застарела је и биће уклоњена у будућем издању."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSMETACHARS>"
msgstr "B<LESSMETACHARS>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "A list of characters special to the shell.  Set by B<xzless> unless it is already set in the environment."
msgstr "Списак знакова нарочитих за шкољку.  Поставите са B<xzless> осим ако већ није постављено у окружењу."

#. type: TP
#: ../src/scripts/xzless.1
#, no-wrap
msgid "B<LESSOPEN>"
msgstr "B<LESSOPEN>"

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "Set to a command line to invoke the B<xz>(1)  decompressor for preprocessing the input files to B<less>(1)."
msgstr "Поставља да линија наредби призове B<xz>(1) распакивач за предобраду улазних датотека на B<less>(1)."

#. type: Plain text
#: ../src/scripts/xzless.1
msgid "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"
msgstr "B<less>(1), B<xz>(1), B<xzmore>(1), B<zless>(1)"

#. type: TH
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "XZMORE"
msgstr "XZMORE"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "xzmore, lzmore - view xz or lzma compressed (text) files"
msgstr "xzmore, lzmore – приказује „xz“ или „lzma“ запаковане (текстуалне) датотеке"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> [I<file>...]"
msgstr "B<xzmore> [I<датотека>...]"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<lzmore> [I<file>...] (DEPRECATED)"
msgstr "B<lzmore> [I<датотека>...] (ЗАСТАРЕЛО)"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<xzmore> displays text from compressed files to a terminal using B<more>(1).  Files supported by B<xz>(1)  are decompressed; other files are assumed to be in uncompressed form already.  If no I<files> are given, B<xzmore> reads from standard input.  See the B<more>(1)  manual for the keyboard commands."
msgstr "B<xzmore> приказује текст из запакованих датотека у терминалу користећи B<more>(1).  Датотеке које подржава B<xz>(1) се распакују; остале датотеке се подразумева да су већ у незапакованом облику.  Ако није дата ни једна I<датотека>, B<xzmore> чита са стандардног улаза.  Видите упутство B<more>(1) за више информација."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "Note that scrolling backwards might not be possible depending on the implementation of B<more>(1).  This is because B<xzmore> uses a pipe to pass the decompressed data to B<more>(1).  B<xzless>(1)  uses B<less>(1)  which provides more advanced features."
msgstr "Знајте да клизање уназад можда неће бити могуће у зависности од примене B<more>(1).  Ово је зато што B<xzmore> користи спојку за прослеђивање распакованих података ка B<more>(1).  B<xzless>(1) користи B<less>(1) које обезбеђује напреднијим функцијама."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "The command B<lzmore> is provided for backward compatibility with LZMA Utils.  It is deprecated and will be removed in a future version."
msgstr "Наредба B<lzmore> се доставља зарад назадне сагласности са LZMA Utils. Застарела је и биће уклоњена у будућем издању."

#.  TRANSLATORS: Don't translate the uppercase PAGER.
#.  It is a name of an environment variable.
#. type: TP
#: ../src/scripts/xzmore.1
#, no-wrap
msgid "B<PAGER>"
msgstr "B<PAGER>"

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "If B<PAGER> is set, its value is used as the pager instead of B<more>(1)."
msgstr "Ако је B<PAGER> постављено, његова вредност се користи као страничар уместо B<more>(1)."

#. type: Plain text
#: ../src/scripts/xzmore.1
msgid "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
msgstr "B<more>(1), B<xz>(1), B<xzless>(1), B<zmore>(1)"
