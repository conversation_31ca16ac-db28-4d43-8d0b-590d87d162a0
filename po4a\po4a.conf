# SPDX-License-Identifier: 0BSD

# To add a new language, add it to po4a_langs and run "update-po"
# to get a new .po file. After translating the .po file, run
# "update-po" again to generate the translated man pages.

[po4a_langs] de fr it ko pt_BR ro sr uk
[po4a_paths] xz-man.pot $lang:$lang.po

[type: man] ../src/xz/xz.1              $lang:man/$lang/xz.1        add_$lang:?$lang.po.authors
[type: man] ../src/xzdec/xzdec.1        $lang:man/$lang/xzdec.1     add_$lang:?$lang.po.authors
[type: man] ../src/lzmainfo/lzmainfo.1  $lang:man/$lang/lzmainfo.1  add_$lang:?$lang.po.authors
[type: man] ../src/scripts/xzdiff.1     $lang:man/$lang/xzdiff.1    add_$lang:?$lang.po.authors
[type: man] ../src/scripts/xzgrep.1     $lang:man/$lang/xzgrep.1    add_$lang:?$lang.po.authors
[type: man] ../src/scripts/xzless.1     $lang:man/$lang/xzless.1    add_$lang:?$lang.po.authors
[type: man] ../src/scripts/xzmore.1     $lang:man/$lang/xzmore.1    add_$lang:?$lang.po.authors
