# SPDX-License-Identifier: 0BSD
#
# Esperanto translations for xz package.
# This file is published under the BSD Zero Clause License.
# <PERSON> <<EMAIL>>, 2019, 2023–2024.
#
msgid ""
msgstr ""
"Project-Id-Version: xz 5.6.0-pre2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2024-02-24 00:16-0500\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Esperanto <<EMAIL>>\n"
"Language: eo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Nevalida parametro por --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Tro da argumentoj por --block-list"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "En --block-list, la blokgrando mankas post numero de la filtrila ĉeno '%c:'"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 povas nur esti uzata kiel la lasta elemento en --block-list"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Nekonata dosierformata tipo"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Nekomprenata tipo de integra kontrolo"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Nur unu dosiero estas specifebla per '--files' aŭ '--files0'."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "La medivariablo %s enhavas troajn argumentojn"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Rego de kunpremado estas malaktivigita dum muntotempo"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Rego de malkunpremado estas malaktivigita dum muntotempo"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Ne povas kunpremi lzip-dosierojn (.lz)"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "--block-list estas ignorata se ne kunpremas al la formato .xz"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Kun --format=raw, --suffix=.SUF estas postulata se ne skribi al la ĉefeligujo"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Maksimuma nombra da filtriloj estas kvar"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Erora en la --filters%s=FILTRILOJ elekto:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Memoruzada limigo estas tro malgranda por la donita filtrila elekto."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "filtrila ĉeno %u uzata de --block-list sed ne specifita per --filters%u="

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Uzi aprioraĵon en kruda reĝimo estas malkonsilinda."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "La ĝustaj elektoj de la aprioraĵoj povas varii inter programoj eldonoj."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "La .lzma-formato komprenas sole la filtrilon LZMA1"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "LZMA ne estas uzebla por la .xz-formato"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "La filtrila ĉeno %u estas nekongrua kun --flush-timeout"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Ŝanĝas al unufadena reĝimo pro --flush-timeout"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Nekomprenataj elektoj en filtrila ĉeno %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Uzas ĝis %<PRIu32> fadenoj"

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Nekomprenata filtrila ĉeno aŭ filtrilaj elektoj"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "Malkunpremado postulos %s megabajtojn da memoro."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Malpliigis la nombron da fadenoj de %s ĝis %s por ne superi la memoruzadan limigo de %s megabajtoj"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Malpliigis la nombron da fadenoj de %s ĝis unu. La aŭtomata memoruzada limigo de %s megabajtoj ankoraŭ estas superata. %s megabajtoj da memoro estas postulata. Senkonsidere daŭrigas."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Ŝanĝas al unufadena reĝimo por ne superi la memoruzadan limigon de %s megabajtoj"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Alĝŭstigis vortara grando de LZMA%c de %s megabajtoj ĝis %s megabajtoj por ne superi la memoruzadan limigon de %s megabajtoj"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Alĝustigis vortara grando de LZMA%c por --filters%u de %s megabajtoj ĝis %s megabajtoj por ne superi la memoruzadan limigon de %s megabajtoj"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Eraro dum ŝanĝiĝo al filtrila ĉeno %u: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Eraro dum krei dukton: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: poll() malsukcesis: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Dosiero ŝajne estis movita, ne forigos"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Ne eblas forigi: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Ne eblas agordi la dosieran estron: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Ne eblas agordi la dosieran grupon: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Ne eblas agordi la dosierajn atingopermesojn: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Fermo de la dosiero malsukcesis: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Fermo de la dosiero malsukcesis: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Eraro dum atingi la dosierstatajn flagojn de ĉefenigujon: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Estas simbola ligilo, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Estas dosierujo, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Ne regula dosiero, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Dosiero havas setuid- aŭ setgid-biton, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Dosiero havas glueman biton, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Enmeta dosiero havas pli ol rektan ligilon, preterpasas"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Malplena dosiero, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Eraro dum restarigi la statajn flagojn de la ĉefenigujo: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Eraro dum atingi la dosierstatajn flagojn el la ĉefenigujo: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Closing the file failed: %s"
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Fermo de la dosiero malsukcesis: %s"

#: src/xz/file_io.c
#, fuzzy, c-format
#| msgid "%s: Not a regular file, skipping"
msgid "%s: Destination is not a regular file"
msgstr "%s: Ne regula dosiero, preterpasas"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Eraro dum restarigi la flagon O_APPEND de la ĉefenigujo: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Fermo de la dosiero malsukcesis: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Serĉado malsukcesis dum provi krei maldensan dosieron: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Legeraro: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Eraro dum serĉi la dosieron: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Neatendita dosierfino"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Skriberaro: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Malaktiva"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Kiomo da efektiva memoro (ĉefmemoro)"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Nombro da procesoraj fadenoj:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Kunpremo:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Malkunmpreno:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Plurfadena malkunpremado:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Aprioraĵo por -T0:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "Aparataro-informoj:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Memoruzada limigoj:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Fluoj:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Blokoj:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Kunpremita grando:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Nekunpremita grando:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Proporcio:"

#: src/xz/list.c
msgid "Check:"
msgstr "Kontrolo:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Fluo-remburo:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Memoro postulata:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Grandoj en ĉapoj:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Nombro da dosieroj:"

#: src/xz/list.c
msgid "Stream"
msgstr "Fluo"

#: src/xz/list.c
msgid "Block"
msgstr "Bloko"

#: src/xz/list.c
msgid "Blocks"
msgstr "Blokoj"

#: src/xz/list.c
msgid "CompOffset"
msgstr "KunpMsam"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "MKunMSam"

#: src/xz/list.c
msgid "CompSize"
msgstr "Kunpgrando"

#: src/xz/list.c
msgid "UncompSize"
msgstr "Mkunpgrando"

#: src/xz/list.c
msgid "TotalSize"
msgstr "KiomGrando"

#: src/xz/list.c
msgid "Ratio"
msgstr "Proporcio"

#: src/xz/list.c
msgid "Check"
msgstr "Kontrolo"

#: src/xz/list.c
msgid "CheckVal"
msgstr "KontVal"

#: src/xz/list.c
msgid "Padding"
msgstr "Remburo"

#: src/xz/list.c
msgid "Header"
msgstr "Ĉapo"

#: src/xz/list.c
msgid "Flags"
msgstr "Flago"

#: src/xz/list.c
msgid "MemUsage"
msgstr "Memuzado"

#: src/xz/list.c
msgid "Filters"
msgstr "Filtriloj"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Nenio"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Nekonata-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Nekonata-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Nekonata-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Nekonata-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Nekonata-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Nekonata-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Nekonata-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Nekonata-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Nekonata-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Nekonata-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Nekonata-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Nekonata-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Dosiero malplenas"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Tro malgranda por esti valida .xz-dosiero"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Fluoj  Blokoj   Kunpremita Nekunpremita Propor  Kontrol Dosiernomo"

#: src/xz/list.c
msgid "Yes"
msgstr "Jes"

#: src/xz/list.c
msgid "No"
msgstr "Ne"

#: src/xz/list.c
#, fuzzy
#| msgid "  Minimum XZ Utils version: %s\n"
msgid "Minimum XZ Utils version:"
msgstr "  Minimuma eldono de XZ Utils: %s\n"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s dosiero\n"
msgstr[1] "%s dosieroj\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Sumoj:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "--list funkcias nur por .xz-dosierojn (--format=xz aŭ --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Provi la programon 'lzmainfo' por .lzma-dosieroj."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "--list ne regas legadon el la ĉefenigujo"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Eraro dum legi dosiernomojn: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Neatendita fino de enigo dum legi dosiernomojn"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Nula signo trovita dum legi dosiernomojn; eble vi celis uzi la parametron '--files0' anstataŭ '--files'"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Kunpremo kaj malkunmpremo per --robot ankoraŭ ne estas regataj."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Ne eblas legi datumojn el la ĉefenigujo dum legi dosiernomojn el la ĉefenigujo"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Interna programeraro"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Ne eblas establi signalajn traktilojn"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Neniu integra kontrolo; ne certigos dosieran integron"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Nekomprenata tipo de integra kontrolo; ne certigos dosieran integron"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Memoruzada limigo atingita"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Dosierformato ne rekonata"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Nekomprenataj elektoj"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Kunpremitaj datumoj estas koruptaj"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Neatendita fino de enigo"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s megabajtoj da memoro estas postulataj. La limigilo estas malaktiva."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s megabajtoj da memoro estas postulata. La limigo estas %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Filtrila ĉeno: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "'%s --help' por pliaj informaj."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr ""

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "Usage: %s [OPTION]... [FILE]...\n"
#| "Compress or decompress FILEs in the .xz format.\n"
#| "\n"
msgid "Compress or decompress FILEs in the .xz format."
msgstr ""
"Uzmaniero: %s [ELEKTO].. [DOSIERO]...\n"
"Kunpremi aŭ malkunpremi DOSIEROjN laŭ la .xz-formato.\n"
"\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Mandatory arguments to long options are mandatory for short options too.\n"
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr ""
"Devigitaj parametroj por longaj elektoj estas ankaŭ devigitaj por\n"
"mallongaj elektoj.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid " Operation mode:\n"
msgid "Operation mode:"
msgstr " Operacia reĝimo:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force compression"
msgstr "Malkunmpreno:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "force decompression"
msgstr "Malkunmpreno:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Operation modifiers:\n"
msgid "Operation modifiers:"
msgstr ""
"\n"
" Operacia modifiloj:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Writing to standard output failed"
msgid "write to standard output and don't delete input files"
msgstr "Skribi al la ĉefeligujo malsukcesis"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --single-stream decompress only the first stream, and silently\n"
#| "                      ignore possible remaining input data"
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr ""
"      --single-stream\n"
"                      malkunpremi nur la unuan fluon kaj silente\n"
"                      ignori eventualajn ceterajn enigajn datumojn"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr ""

#: src/xz/message.c
msgid ".SUF"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr ""

#: src/xz/message.c
msgid "FILE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Basic file format and compression options:\n"
msgid "Basic file format and compression options:"
msgstr ""
"\n"
" Bazaj dosierformataj kaj kunpremaj elektoj:\n"

#: src/xz/message.c
msgid "FORMAT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr ""

#: src/xz/message.c
msgid "NAME"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --ignore-check  don't verify the integrity check when decompressing"
msgid "don't verify the integrity check when decompressing"
msgstr "      --ignore-check  ne certigi la integran kontrolon dum malkunpremo"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -0 ... -9           compression preset; default is 6; take compressor *and*\n"
#| "                      decompressor memory usage into account before using 7-9!"
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr ""
"  -0 ... -9           kunpremnivelo; apriore 6; pripensu memoruzadon antaŭ ol\n"
"                      uzi la nivelojn 7-9!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -e, --extreme       try to improve compression ratio by using more CPU time;\n"
#| "                      does not affect decompressor memory requirements"
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr ""
"  -e, --extreme       provi plibonigi kunpreman proporcion per uzado de\n"
"                      ĉefprocesoran tempon; ne influas la memorajn postulojn\n"
"                      de malkunpremo"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  -T, --threads=NUM   use at most NUM threads; the default is 0 which uses\n"
#| "                      as many threads as there are processor cores"
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr ""
"  -T, --threads=NOMBRO\n"
"                      uzi maksimume NOMBRO da fadenoj; apriore 0, kiu\n"
"                      uzas fadenojn samnombrajn kiel procesorajn kernojn"

#: src/xz/message.c
msgid "SIZE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-size=SIZE\n"
#| "                      start a new .xz block after every SIZE bytes of input;\n"
#| "                      use this to set the block size for threaded compression"
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr ""
"      --block-size=GRANDO\n"
"                      komenci novan .xz-blokon post ĉiu GRANDO bajtoj da enigo;\n"
"                      uzi por agordi la blokan grandon por kunfadena kunpremo"

#: src/xz/message.c
msgid "BLOCKS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --block-list=BLOCKS\n"
#| "                      start a new .xz block after the given comma-separated\n"
#| "                      intervals of uncompressed data; optionally, specify a\n"
#| "                      filter chain number (0-9) followed by a ':' before the\n"
#| "                      uncompressed data size"
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr ""
"      --block-list=BLOKOJ\n"
"                      komenci novan .xz-blokon post la donitajn intertempojn de\n"
"                      nekunpremitaj datumoj, apartigataj de komoj. Laŭvole\n"
"                      specifi numero (0-9) de filtrila ĉeno kaj poste ':' antaŭ\n"
"                      la malkunpremita datuma grando"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --flush-timeout=TIMEOUT\n"
#| "                      when compressing, if more than TIMEOUT milliseconds has\n"
#| "                      passed since the previous flush and reading more input\n"
#| "                      would block, all pending data is flushed out"
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr ""
"      --flush-timeout=TEMPOLIMO\n"
"                      dum kunpremo se pli ol TEMPOLIMO milisekundoj\n"
"                      okazis post la antaŭan elbufrigo kaj legi pliajn enigojn\n"
"                      paŭzigus, ĉiuj atendataj datumoj estas elbufrigataj"

#: src/xz/message.c
msgid "LIMIT"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --no-adjust     if compression settings exceed the memory usage limit,\n"
#| "                      give an error instead of adjusting the settings downwards"
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr ""
"      --no-adjust     se kunprema agordo superas la memoruzadan limigon\n"
"                      montri eraron anstataŭ malgrandigi la agordaĵon"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Custom filter chain for compression (alternative for using presets):"
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr ""
"\n"
" Propra filtrila ĉeno por kunpremo (alternativaj por uzi antaŭagordaĵon):"

#: src/xz/message.c
msgid "FILTERS"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| "  --filters=FILTERS   set the filter chain using the liblzma filter string\n"
#| "                      syntax; use --filters-help for more information"
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr ""
"\n"
"  --filters=FILTRILOJ   agordi la filtrilan ĉenan uzanta la sintakso de la\n"
"                      lzma-filtrila ĉeno.  --filters-help or pliaj informoj"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters1=FILTERS ... --filters9=FILTERS\n"
#| "                      set additional filter chains using the liblzma filter\n"
#| "                      string syntax to use with --block-list"
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr ""
"  --filters=FILTRILOJ ... --filters9=FILTRILOJ\n"
"                      agordi aldonajn filtrilajn ĉenojn por uzi kun\n"
"                      --block-list per la sintakso de liblzma-filtrila ĉeno"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "  --filters-help      display more information about the liblzma filter string\n"
#| "                      syntax and exit."
msgid "display more information about the liblzma filter string syntax and exit"
msgstr ""
"  --filters-help      montri pliajn informojn pri la sintakso de la\n"
"                      liblzma-filtrila ĉeno kaj poste eliri."

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr ""

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr ""

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr ""

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Number of processor threads:"
msgid "number of position bits"
msgstr "Nombro da procesoraj fadenoj:"

#: src/xz/message.c
msgid "MODE"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "Decompression:"
msgid "compression mode"
msgstr "Malkunmpreno:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "\n"
#| " Other options:\n"
msgid "Other options:"
msgstr ""
"\n"
" Aliaj elektoj:\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -Q, --no-warn       make warnings not affect the exit status"
msgid "make warnings not affect the exit status"
msgstr "  -Q, --no-warn       avertoj ne influu la eliran staton"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "      --robot         use machine-parsable messages (useful for scripts)"
msgid "use machine-parsable messages (useful for scripts)"
msgstr "      --robot         uzi mesaĝojn facile analizeblaj per skriptoj"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid ""
#| "      --info-memory   display the total amount of RAM and the currently active\n"
#| "                      memory usage limits, and exit"
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr ""
"      --info-memory   montri la totalan kiomon de la ĉefmemoro kaj la nune\n"
"                      aktivaj memoruzadaj limigoj, kaj finiĝi"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr ""

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy
#| msgid "  -V, --version       display the version number and exit"
msgid "display the version number and exit"
msgstr "  -V, --version       montri la eldonan numeron kaj finiĝi"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "\n"
#| "With no FILE, or when FILE is -, read standard input.\n"
msgid "With no FILE, or when FILE is -, read standard input."
msgstr ""
"\n"
"Kun neniu DOSIERO aŭ kiam DOSIERO estas -, legi el la ĉefenigujo.\n"

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "Report bugs to <%s> (in English or Finnish).\n"
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "Raporti programerarojn al <%s> (en la angla aŭ la suoma).\n"

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid "%s home page: <%s>\n"
msgid "%s home page: <%s>"
msgstr "%s ĉefpaĝo: <%s>\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "ĈI TIU ESTAS DISVOLVA REDAKCIO, NE CELATA POR ĈIUTAGA UZADO."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, fuzzy, c-format
#| msgid ""
#| "Filter chains are set using the --filters=FILTERS or\n"
#| "--filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain\n"
#| "can be separated by spaces or '--'. Alternatively a preset <0-9>[e] can be\n"
#| "specified instead of a filter chain.\n"
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr ""
"Filtrilaj ĉenoj estas agorditaj per la elektoj --filters=FIILTRILOJ aŭ\n"
"--filters1=FILTRILOJ ... --filters9=FILTRILOJ. Ĉiu filtrilo en la ĉeno\n"
"estas apartigebla per spacetoj aŭ '--'. Alternative antaŭargordo <0-9>[e]\n"
"anstataŭ filtrila ĉeno estas specifebla.\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "La komprenataj filtriloj aŭ filtrilaj elektoj estas:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Options must be 'name=value' pairs separated with commas"
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "%s: Elektoj devas esti paroj de 'name=value', apartigitaj per komoj"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Nevalida elekto-nomo"

#: src/xz/options.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option value"
msgid "Invalid option value"
msgstr "%s: Nevalida elekto-valoro"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "Nevalida LZMA1/LZMA2 antaŭagordaĵo: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "La sumo de lc kaj lp devas ne esti pli ol 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Dosiernomo havas nekonatan sufikson, preterpasas"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Dosiero jam havas la sufikson '%s', preterpasas"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Nevalida dosiernoma sufikso"

#: src/xz/util.c src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Value is not a non-negative decimal integer"
msgid "Value is not a non-negative decimal integer"
msgstr "%s: Valoro ne estas nenegativa dekuma entjero"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Nevalida multiplika sufikso"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Validaj sufiksoj estas 'KiB' (2^10), 'MiB' (2^20) kaj 'GiB' (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Valoro de la elekto '%s' devas esti inkluzive inter %<PRIu64> kaj %<PRIu64>"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Kunpremitaj datumoj ne povas esti ligataj de terminalo"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Kunpremitaj datumoj ne povas esti skribataj al terminalo"

#: src/lzmainfo/lzmainfo.c
#, fuzzy, c-format
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr ""
"Uzmaniero: %s [--help] [--version] [DOSIERO]...\n"
"Montri informojn konservitaj en la .lzma-dosiera ĉapo"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
#, fuzzy
#| msgid ""
#| "Usage: %s [--help] [--version] [FILE]...\n"
#| "Show information stored in the .lzma file header"
msgid "Show information stored in the .lzma file header."
msgstr ""
"Uzmaniero: %s [--help] [--version] [DOSIERO]...\n"
"Montri informojn konservitaj en la .lzma-dosiera ĉapo"

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "Dosiero malgrandas por esti .lzma-dosiero"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Ne .lzma-dosiero"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Skribi al la ĉefeligujo malsukcesis"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Nekonata eraro"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported options"
msgid "Unsupported preset"
msgstr "Nekomprenataj elektoj"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Unsupported filter chain or filter options"
msgid "Unsupported flag in the preset"
msgstr "Nekomprenata filtrila ĉeno aŭ filtrilaj elektoj"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid option name"
msgid "Unknown option name"
msgstr "%s: Nevalida elekto-nomo"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr ""

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Invalid multiplier suffix"
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "%s: Nevalida multiplika sufikso"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "%s: Unknown file format type"
msgid "Unknown filter name"
msgstr "%s: Nekonata dosierformata tipo"

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "LZMA1 cannot be used with the .xz format"
msgid "This filter cannot be used in the .xz format"
msgstr "LZMA ne estas uzebla por la .xz-formato"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr ""

#: src/liblzma/common/string_conversion.c
#, fuzzy
#| msgid "Maximum number of filters is four"
msgid "The maximum number of filters is four"
msgstr "Maksimuma nombra da filtriloj estas kvar"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr ""

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr ""

#~ msgid ""
#~ "  -z, --compress      force compression\n"
#~ "  -d, --decompress    force decompression\n"
#~ "  -t, --test          test compressed file integrity\n"
#~ "  -l, --list          list information about .xz files"
#~ msgstr ""
#~ "  -z, --compress      eldevigi kunpremon\n"
#~ "  -d, --decompress    eldevigi malkunpremon\n"
#~ "  -t, --test          certigi la integron de kunpremitan dosieron\n"
#~ "  -l, --list          listigi informojn pri .xz-dosierojn"

#~ msgid ""
#~ "  -k, --keep          keep (don't delete) input files\n"
#~ "  -f, --force         force overwrite of output file and (de)compress links\n"
#~ "  -c, --stdout        write to standard output and don't delete input files"
#~ msgstr ""
#~ "  -k, --keep          ne forigi enigajn dosierojn\n"
#~ "  -f, --force         eldevigi anstataŭigi eligajn dosierojn kaj\n"
#~ "                      (mal)kunpmremajn ligilojn \n"
#~ "  -c, --stdout        skribi al la ĉefeligujo kaj ne forigi enigajn dosierojn"

#~ msgid ""
#~ "      --no-sparse     do not create sparse files when decompressing\n"
#~ "  -S, --suffix=.SUF   use the suffix '.SUF' on compressed files\n"
#~ "      --files[=FILE]  read filenames to process from FILE; if FILE is\n"
#~ "                      omitted, filenames are read from the standard input;\n"
#~ "                      filenames must be terminated with the newline character\n"
#~ "      --files0[=FILE] like --files but use the null character as terminator"
#~ msgstr ""
#~ "      --no-sparse     ne krei maldensajn dosierojn dum malkunpremiĝo\n"
#~ "  -S, --suffix=.SUF   uzi la sufikson '.SUF' ĉe kunpremataj dosieroj\n"
#~ "      --files[=DOSIERO]\n"
#~ "                      legi dosiernomojn traktotajn el DOSIERO; se DOSIERO estas\n"
#~ "                      forlasita, dosiernomoj estas legataj el la ĉefenigujo;\n"
#~ "                      dosiernomojn devas finigi novlinia signo\n"
#~ "      --files0[=DOSIERO]\n"
#~ "                      kiel --files sed uzi la nulan signon por finigi"

#~ msgid ""
#~ "  -F, --format=FMT    file format to encode or decode; possible values are\n"
#~ "                      'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'\n"
#~ "  -C, --check=CHECK   integrity check type: 'none' (use with caution),\n"
#~ "                      'crc32', 'crc64' (default), or 'sha256'"
#~ msgstr ""
#~ "h  -F, --format=FMT    dosierformato kodota aŭ malkodata; validaj valoroj estas\n"
#~ "                      'auto' (apriora), 'xz', 'lzma', 'lzip' kaj 'raw'\n"
#~ "  -C, --check=KONT    tipo de integra kontrolo: 'none' (estu atentema),\n"
#~ "                      'crc32', 'crc64' (apriora) aŭ 'sha256'"

#, no-c-format
#~ msgid ""
#~ "      --memlimit-compress=LIMIT\n"
#~ "      --memlimit-decompress=LIMIT\n"
#~ "      --memlimit-mt-decompress=LIMIT\n"
#~ "  -M, --memlimit=LIMIT\n"
#~ "                      set memory usage limit for compression, decompression,\n"
#~ "                      threaded decompression, or all of these; LIMIT is in\n"
#~ "                      bytes, % of RAM, or 0 for defaults"
#~ msgstr ""
#~ "      --memlimit-compress=LIMIGO\n"
#~ "      --memlimit-decompress=LIMIGO\n"
#~ "      --memlimit-mt-decompress=LIMIGO\n"
#~ "  -M, --memlimit=LIMO\n"
#~ "                      agordi memoruzadon por kunpremo, malkunpremo,\n"
#~ "                      kunfadena kunpmero aŭ ĉiuj el tiuj; LIMIGO estas\n"
#~ "                      laŭ bajtoj, % da ĉefmemoro, aŭ 0 por aprioraĵoj"

#~ msgid ""
#~ "\n"
#~ "  --lzma1[=OPTS]      LZMA1 or LZMA2; OPTS is a comma-separated list of zero or\n"
#~ "  --lzma2[=OPTS]      more of the following options (valid values; default):\n"
#~ "                        preset=PRE reset options to a preset (0-9[e])\n"
#~ "                        dict=NUM   dictionary size (4KiB - 1536MiB; 8MiB)\n"
#~ "                        lc=NUM     number of literal context bits (0-4; 3)\n"
#~ "                        lp=NUM     number of literal position bits (0-4; 0)\n"
#~ "                        pb=NUM     number of position bits (0-4; 2)\n"
#~ "                        mode=MODE  compression mode (fast, normal; normal)\n"
#~ "                        nice=NUM   nice length of a match (2-273; 64)\n"
#~ "                        mf=NAME    match finder (hc3, hc4, bt2, bt3, bt4; bt4)\n"
#~ "                        depth=NUM  maximum search depth; 0=automatic (default)"
#~ msgstr ""
#~ "\n"
#~ "  --lzma1[=ELEKTOJ]   LZMA1 aŭ LZMA2; OPTS estas listo de nul aŭ pliaj\n"
#~ "  --lzma2[=ELEKTOJ]   de la jenaj elektoj (validaj valoroj; apriora),\n"
#~ "                      apartigataj de komoj:\n"
#~ "                        preset=ANT restarigi agordon al antaŭagordaĵon (0-9[e])\n"
#~ "                        dict=NOM   vortara grando (4 kilobajtoj - 1536\n"
#~ "                                   megabajtoj; 8 megabajtoj)\n"
#~ "                        lc=NOM     nombro da laŭvortaj kuntekstaj bitoj\n"
#~ "                                   (0-4; 3)\n"
#~ "                        lp=NOM     nombro da laŭvortaj poziciaj bitoj (0-4; 0)\n"
#~ "                        pb=NOM     nombro da poziciaj bitoj (0-4; 2)\n"
#~ "                        mode=REĜI  kunprema reĝimo (fast, normal; normal)\n"
#~ "                        nice=NOM   bona longo de kongruaĵo (2-273; 64)\n"
#~ "                        mf=NOMO    kongruaĵa trovilo (hc3, hc4, bt2, bt3, bt4;\n"
#~ "                                   bt4)\n"
#~ "                        depth=NUM  maksimuma profundo de serĉo; 0=aŭtomata\n"
#~ "                                   (apriore)"

#~ msgid ""
#~ "\n"
#~ "  --x86[=OPTS]        x86 BCJ filter (32-bit and 64-bit)\n"
#~ "  --arm[=OPTS]        ARM BCJ filter\n"
#~ "  --armthumb[=OPTS]   ARM-Thumb BCJ filter\n"
#~ "  --arm64[=OPTS]      ARM64 BCJ filter\n"
#~ "  --powerpc[=OPTS]    PowerPC BCJ filter (big endian only)\n"
#~ "  --ia64[=OPTS]       IA-64 (Itanium) BCJ filter\n"
#~ "  --sparc[=OPTS]      SPARC BCJ filter\n"
#~ "  --riscv[=OPTS]      RISC-V BCJ filter\n"
#~ "                      Valid OPTS for all BCJ filters:\n"
#~ "                        start=NUM  start offset for conversions (default=0)"
#~ msgstr ""
#~ "\n"
#~ "  --x86[=ELEKTOJ]     x86-BCJ-filtrilo (32-bita kaj 64-bita)\n"
#~ "  --arm[=ELEKTOJ]     ARM-BCJ-filtrilo\n"
#~ "  --armthumb[=ELEKTOJ]\n"
#~ "                      ARM-Thumb-BCJ-filtrilo\n"
#~ "  --arm64[=OPTS]      ARM64-BCJ-filtrilo\n"
#~ "  --powerpc[=ELEKTOJ] PowerPC-BCJ-filtrilo (nur pezkomenca)\n"
#~ "  --ia64[=ELEKTOJ]    IA-64 (Itanium)-BCJ-filtrilo\n"
#~ "  --sparc[=ELEKTOJ]   SPARC-BCJ-filtrilo\n"
#~ "  --riscv[=ELEKTOJ]   RISC-V_BCJ-filtrilo\n"
#~ "                      Validaj ELEKTOJ por ĉiuj BCJ-filters:\n"
#~ "                        start=NOMBRO  komenca deŝovo por konvertoj (apriore 0)"

#~ msgid ""
#~ "\n"
#~ "  --delta[=OPTS]      Delta filter; valid OPTS (valid values; default):\n"
#~ "                        dist=NUM   distance between bytes being subtracted\n"
#~ "                                   from each other (1-256; 1)"
#~ msgstr ""
#~ "\n"
#~ "  --delta[=ELEKTOJ]   Delta filtriloj; validaj valoroj:\n"
#~ "                        dist=NOMBRO  distanco inter bajtoj subtrahataj de unu\n"
#~ "                                     la alia (1-256; 1)"

#~ msgid ""
#~ "  -q, --quiet         suppress warnings; specify twice to suppress errors too\n"
#~ "  -v, --verbose       be verbose; specify twice for even more verbose"
#~ msgstr ""
#~ "  -q, --quiet         silentigi avertojn; uzu dufoje por ankaŭ silentigi erarojn\n"
#~ "  -v, --verbose       eligi superfluajn informojn; uzu dufoje por pliigi la\n"
#~ "                      superfluecon"

#~ msgid ""
#~ "  -h, --help          display the short help (lists only the basic options)\n"
#~ "  -H, --long-help     display this long help and exit"
#~ msgstr ""
#~ "  -h, --help          montri la mallongan helpon (listigas nur la bazajn\n"
#~ "                      elektojn)\n"
#~ "  -H, --long-help     montri la longan helpon kaj finiĝi"

#~ msgid ""
#~ "  -h, --help          display this short help and exit\n"
#~ "  -H, --long-help     display the long help (lists also the advanced options)"
#~ msgstr ""
#~ "  -h, --help          montri ĉi tiun mallongan helpon kaj finiĝi\n"
#~ "  -H, --long-help     montri la longan helpon (listigas ankaŭ la altnivelajn\n"
#~ "                      elektojn)"

#~ msgid "Failed to enable the sandbox"
#~ msgstr "Malsukcesis aktivigi la sablujon"
