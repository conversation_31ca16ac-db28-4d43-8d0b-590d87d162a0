# SPDX-License-Identifier: 0BSD
#
# Serbian translation of xz.
# This file is published under the BSD Zero Clause License.
# <AUTHOR> <EMAIL>, 2020-2025.
msgid ""
msgstr ""
"Project-Id-Version: xz 5.7.1-dev1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-01-29 20:59+0200\n"
"PO-Revision-Date: 2025-02-03 07:28+0100\n"
"Last-Translator: Мирослав Николић <<EMAIL>>\n"
"Language-Team: Serbian <(nothing)>\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Bugs: Report translation errors to the Language-Team address.\n"
"X-Generator: Poedit 3.5\n"

#: src/xz/args.c
#, c-format
msgid "%s: Invalid argument to --block-list"
msgstr "%s: Неисправан аргумент за „--block-list“"

#: src/xz/args.c
#, c-format
msgid "%s: Too many arguments to --block-list"
msgstr "%s: Превише аргумената за „--block-list“"

#: src/xz/args.c
#, c-format
msgid "In --block-list, block size is missing after filter chain number '%c:'"
msgstr "У „--block-list“, величина блока недостаје након ланца филтера број „%c:“"

#: src/xz/args.c
msgid "0 can only be used as the last element in --block-list"
msgstr "0 се може користити само као последњи елемент у „--block-list“-у"

#: src/xz/args.c
#, c-format
msgid "%s: Unknown file format type"
msgstr "%s: Непозната врста формата датотеке"

#: src/xz/args.c
#, c-format
msgid "%s: Unsupported integrity check type"
msgstr "%s: Неподржана врста провере целовитости"

#: src/xz/args.c
msgid "Only one file can be specified with '--files' or '--files0'."
msgstr "Само једну датотеку можете навести са „--files“ или „--files0“."

#. TRANSLATORS: This is a translatable
#. string because French needs a space
#. before the colon ("%s : %s").
#: src/xz/args.c src/xz/coder.c src/xz/file_io.c src/xz/list.c src/xz/options.c
#: src/xz/util.c
#, c-format
msgid "%s: %s"
msgstr "%s: %s"

#: src/xz/args.c
#, c-format
msgid "The environment variable %s contains too many arguments"
msgstr "Променљива окружења „%s“ садржи превише аргумената"

#: src/xz/args.c
msgid "Compression support was disabled at build time"
msgstr "Подршка запакивања је искључена у време изградње"

#: src/xz/args.c
msgid "Decompression support was disabled at build time"
msgstr "Подршка распакивања је искључена у време изградње"

#: src/xz/args.c
msgid "Compression of lzip files (.lz) is not supported"
msgstr "Запакивање „lzip“ датотека (.lz) није подржано"

#: src/xz/args.c
msgid "--block-list is ignored unless compressing to the .xz format"
msgstr "„--block-list“ се занемарује осим ако се пакује у „.xz“ формат"

#: src/xz/args.c
msgid "With --format=raw, --suffix=.SUF is required unless writing to stdout"
msgstr "Са „--format=raw“, „--suffix=.SUF“ је потребно осим ако се пише на стандардни излаз"

#: src/xz/coder.c
msgid "Maximum number of filters is four"
msgstr "Највећи број филтера је четири"

#: src/xz/coder.c
#, c-format
msgid "Error in --filters%s=FILTERS option:"
msgstr "Грешка у опцији „--filters%s=ФИЛТЕРИ“:"

#: src/xz/coder.c
msgid "Memory usage limit is too low for the given filter setup."
msgstr "Ограничење коришћења меморије је премало за дато подешавање филтера."

#: src/xz/coder.c
#, c-format
msgid "filter chain %u used by --block-list but not specified with --filters%u="
msgstr "ланац филтера „%u“ је коришћен од „--block-list“ али није наведен са „--filters%u=“"

#: src/xz/coder.c
msgid "Using a preset in raw mode is discouraged."
msgstr "Коришћење претподешавања у сировом режиму је обесхрабрујуће."

#: src/xz/coder.c
msgid "The exact options of the presets may vary between software versions."
msgstr "Тачне опције претподешавања се могу разликовати од издања до издања софтвера."

#: src/xz/coder.c
msgid "The .lzma format supports only the LZMA1 filter"
msgstr "Формат „.lzma“ подржава само „LZMA1“ филтер"

#: src/xz/coder.c
msgid "LZMA1 cannot be used with the .xz format"
msgstr "Не можете користити „LZMA1“ са „.xz“ форматом"

#: src/xz/coder.c
#, c-format
msgid "Filter chain %u is incompatible with --flush-timeout"
msgstr "Ланац филтера „%u“ није сагласан са „--flush-timeout“"

#: src/xz/coder.c
msgid "Switching to single-threaded mode due to --flush-timeout"
msgstr "Пребацујем се на режим једне нити због „--flush-timeout“"

#: src/xz/coder.c
#, c-format
msgid "Unsupported options in filter chain %u"
msgstr "Неподржане опције у ланцу филтера %u"

#: src/xz/coder.c
#, c-format
msgid "Using up to %<PRIu32> threads."
msgstr "Користим до %<PRIu32> нити."

#: src/xz/coder.c
msgid "Unsupported filter chain or filter options"
msgstr "Неподржан ланац филтера или опције филтера"

#: src/xz/coder.c
#, c-format
msgid "Decompression will need %s MiB of memory."
msgstr "За распакивање ће бити потребно %s MiB меморије."

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to %s to not exceed the memory usage limit of %s MiB"
msgstr "Број нити је смањен са %s на %s да се не би прекорачило ограничење коришћења меморије од %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Reduced the number of threads from %s to one. The automatic memory usage limit of %s MiB is still being exceeded. %s MiB of memory is required. Continuing anyway."
msgstr "Број нити је смањен са %s на једну. Аутоматско ограничење коришћења меморије од %s MiB је ипак премашено. %s MiB меморије је потребно. Ипак настављам."

#: src/xz/coder.c
#, c-format
msgid "Switching to single-threaded mode to not exceed the memory usage limit of %s MiB"
msgstr "Прелазим на режим једне нити да се не би прекорачило ограничење коришћења меморије од %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Величина „LZMA%c“ речника је промењена са %s на %s да се неби прекорачило ограничење коришћења меморије од %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Adjusted LZMA%c dictionary size for --filters%u from %s MiB to %s MiB to not exceed the memory usage limit of %s MiB"
msgstr "Величина „LZMA%c“ речника је промењена за „--filters%u“ са %s MiB на %s MiB да се не прекорачи ограничење коришћења меморије од %s MiB"

#: src/xz/coder.c
#, c-format
msgid "Error changing to filter chain %u: %s"
msgstr "Грешка промене на ланац филтера „%u“: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error creating a pipe: %s"
msgstr "Грешка стварања спојке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: poll() failed: %s"
msgstr "%s: „poll()“ није успело: %s"

#. TRANSLATORS: When compression or decompression finishes,
#. and xz is going to remove the source file, xz first checks
#. if the source file still exists, and if it does, does its
#. device and inode numbers match what xz saw when it opened
#. the source file. If these checks fail, this message is
#. shown, %s being the filename, and the file is not deleted.
#. The check for device and inode numbers is there, because
#. it is possible that the user has put a new file in place
#. of the original file, and in that case it obviously
#. shouldn't be removed.
#: src/xz/file_io.c
#, c-format
msgid "%s: File seems to have been moved, not removing"
msgstr "%s: Изгледа да је датотека премештена, не уклањам"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot remove: %s"
msgstr "%s: Не могу да уклоним: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file owner: %s"
msgstr "%s: Не могу да поставим власника датотеке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file group: %s"
msgstr "%s: Не могу да поставим групу датотеке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Cannot set the file permissions: %s"
msgstr "%s: Не могу да поставим овлашћења датотеке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the file failed: %s"
msgstr "%s: Усклађивање датотеке није успело: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Synchronizing the directory of the file failed: %s"
msgstr "%s: Усклађивање директоријума датотеке није успело: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard input: %s"
msgstr "Грешка добављања заставица стања датотеке са стандардног улаза: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a symbolic link, skipping"
msgstr "%s: Јесте симболичка веза прескачем"

#: src/xz/file_io.c
#, c-format
msgid "%s: Is a directory, skipping"
msgstr "%s: Јесте директоријум, прескачем"

#: src/xz/file_io.c
#, c-format
msgid "%s: Not a regular file, skipping"
msgstr "%s: Није обична датотека, прескачем"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has setuid or setgid bit set, skipping"
msgstr "%s: Датотека има постављен „setuid“ или „setgid“ бит, прескачем"

#: src/xz/file_io.c
#, c-format
msgid "%s: File has sticky bit set, skipping"
msgstr "%s: Датотека има постављен лепљиви бит, прескачем"

#: src/xz/file_io.c
#, c-format
msgid "%s: Input file has more than one hard link, skipping"
msgstr "%s: Улазна датотека има више од једне чврсте везе, прескачем"

#: src/xz/file_io.c
msgid "Empty filename, skipping"
msgstr "Празан назив датотеке, прескачем"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the status flags to standard input: %s"
msgstr "Грешка повраћаја заставица стања на стандардни улаз: %s"

#: src/xz/file_io.c
#, c-format
msgid "Error getting the file status flags from standard output: %s"
msgstr "Грешка добављања заставица стања датотеке са стандардног излаза: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Opening the directory failed: %s"
msgstr "%s: Отварање директоријума није успело: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Destination is not a regular file"
msgstr "%s: Одредиште није обична датотека"

#: src/xz/file_io.c
#, c-format
msgid "Error restoring the O_APPEND flag to standard output: %s"
msgstr "Грешка повраћаја заставице „O_APPEND“ на стандардни излаз: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Closing the file failed: %s"
msgstr "%s: Затварање датотеке није успело: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Seeking failed when trying to create a sparse file: %s"
msgstr "%s: Премотавање није успело приликом покушаја прављења оскудне датотеке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Read error: %s"
msgstr "%s: Грешка читања: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Error seeking the file: %s"
msgstr "%s: Грешка приликом претраге датотеке: %s"

#: src/xz/file_io.c
#, c-format
msgid "%s: Unexpected end of file"
msgstr "%s: Неочекиван крај датотеке"

#: src/xz/file_io.c
#, c-format
msgid "%s: Write error: %s"
msgstr "%s: Грешка писања: %s"

#: src/xz/hardware.c
msgid "Disabled"
msgstr "Искључено"

#: src/xz/hardware.c
msgid "Amount of physical memory (RAM):"
msgstr "Укупна количина физичке меморије (RAM):"

#: src/xz/hardware.c
msgid "Number of processor threads:"
msgstr "Број нити процесора:"

#: src/xz/hardware.c
msgid "Compression:"
msgstr "Запакивање:"

#: src/xz/hardware.c
msgid "Decompression:"
msgstr "Распакивање:"

#: src/xz/hardware.c
msgid "Multi-threaded decompression:"
msgstr "Вишенитно распакивање:"

#: src/xz/hardware.c
msgid "Default for -T0:"
msgstr "Основно за „-T0“:"

#: src/xz/hardware.c
msgid "Hardware information:"
msgstr "О хардверу:"

#: src/xz/hardware.c
msgid "Memory usage limits:"
msgstr "Ограничење коришћења меморије:"

#: src/xz/list.c
msgid "Streams:"
msgstr "Токови:"

#: src/xz/list.c
msgid "Blocks:"
msgstr "Блокови:"

#: src/xz/list.c
msgid "Compressed size:"
msgstr "Величина сажетог:"

#: src/xz/list.c
msgid "Uncompressed size:"
msgstr "Величина несажетог:"

#: src/xz/list.c
msgid "Ratio:"
msgstr "Однос:"

#: src/xz/list.c
msgid "Check:"
msgstr "Провери:"

#: src/xz/list.c
msgid "Stream Padding:"
msgstr "Попуна тока:"

#: src/xz/list.c
msgid "Memory needed:"
msgstr "Потребна меморија:"

#: src/xz/list.c
msgid "Sizes in headers:"
msgstr "Величине у заглављима:"

#: src/xz/list.c
msgid "Number of files:"
msgstr "Број датотека:"

#: src/xz/list.c
msgid "Stream"
msgstr "Ток"

#: src/xz/list.c
msgid "Block"
msgstr "Блок"

#: src/xz/list.c
msgid "Blocks"
msgstr "Блокови"

#: src/xz/list.c
msgid "CompOffset"
msgstr "ПомерЗапак"

#: src/xz/list.c
msgid "UncompOffset"
msgstr "ПомерНсжтог"

#: src/xz/list.c
msgid "CompSize"
msgstr "ВлчнаЗапак"

#: src/xz/list.c
msgid "UncompSize"
msgstr "ВлчнаНсжтог"

#: src/xz/list.c
msgid "TotalSize"
msgstr "УкупнаВлчна"

#: src/xz/list.c
msgid "Ratio"
msgstr "Однос"

#: src/xz/list.c
msgid "Check"
msgstr "Провери"

#: src/xz/list.c
msgid "CheckVal"
msgstr "ПровериВредн"

#: src/xz/list.c
msgid "Padding"
msgstr "Попуна"

#: src/xz/list.c
msgid "Header"
msgstr "Заглавље"

#: src/xz/list.c
msgid "Flags"
msgstr "Заставице"

#: src/xz/list.c
msgid "MemUsage"
msgstr "КоришћМемор"

#: src/xz/list.c
msgid "Filters"
msgstr "Филтери"

#. TRANSLATORS: Indicates that there is no integrity check.
#. This string is used in tables. In older xz version this
#. string was limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "None"
msgstr "Ништа"

#. TRANSLATORS: Indicates that integrity check name is not known,
#. but the Check ID is known (here 2). In older xz version these
#. strings were limited to ten columns in a fixed-width font, but
#. nowadays there is no strict length restriction anymore.
#: src/xz/list.c
msgid "Unknown-2"
msgstr "Незнано-2"

#: src/xz/list.c
msgid "Unknown-3"
msgstr "Незнано-3"

#: src/xz/list.c
msgid "Unknown-5"
msgstr "Незнано-5"

#: src/xz/list.c
msgid "Unknown-6"
msgstr "Незнано-6"

#: src/xz/list.c
msgid "Unknown-7"
msgstr "Незнано-7"

#: src/xz/list.c
msgid "Unknown-8"
msgstr "Незнано-8"

#: src/xz/list.c
msgid "Unknown-9"
msgstr "Незнано-9"

#: src/xz/list.c
msgid "Unknown-11"
msgstr "Незнано-11"

#: src/xz/list.c
msgid "Unknown-12"
msgstr "Незнано-12"

#: src/xz/list.c
msgid "Unknown-13"
msgstr "Незнано-13"

#: src/xz/list.c
msgid "Unknown-14"
msgstr "Незнано-14"

#: src/xz/list.c
msgid "Unknown-15"
msgstr "Незнано-15"

#: src/xz/list.c
#, c-format
msgid "%s: File is empty"
msgstr "%s: Датотека је празна"

#: src/xz/list.c
#, c-format
msgid "%s: Too small to be a valid .xz file"
msgstr "%s: Премало је да би било исправна „.xz“ датотека"

#. TRANSLATORS: These are column headings. From Strms (Streams)
#. to Ratio, the columns are right aligned. Check and Filename
#. are left aligned. If you need longer words, it's OK to
#. use two lines here. Test with "xz -l foo.xz".
#: src/xz/list.c
msgid "Strms  Blocks   Compressed Uncompressed  Ratio  Check   Filename"
msgstr "Токови Блокови  Запаковано Распаковано   Однос  Провера  Датотека"

#: src/xz/list.c
msgid "Yes"
msgstr "Да"

#: src/xz/list.c
msgid "No"
msgstr "Не"

#: src/xz/list.c
msgid "Minimum XZ Utils version:"
msgstr "Најмање издање XZ помагала:"

#. TRANSLATORS: %s is an integer. Only the plural form of this
#. message is used (e.g. "2 files"). Test with "xz -l foo.xz bar.xz".
#: src/xz/list.c
#, c-format
msgid "%s file\n"
msgid_plural "%s files\n"
msgstr[0] "%s датотека\n"
msgstr[1] "%s датотеке\n"
msgstr[2] "%s датотека\n"

#: src/xz/list.c
msgid "Totals:"
msgstr "Укупно:"

#: src/xz/list.c
msgid "--list works only on .xz files (--format=xz or --format=auto)"
msgstr "„--list“ ради само над „.xz“ датотекама (--format=xz или --format=auto)"

#: src/xz/list.c
msgid "Try 'lzmainfo' with .lzma files."
msgstr "Пробајте „lzmainfo“ са „.lzma“ датотекама."

#: src/xz/list.c
msgid "--list does not support reading from standard input"
msgstr "„--list“ не подржава читање са стандардног улаза"

#: src/xz/main.c
#, c-format
msgid "%s: Error reading filenames: %s"
msgstr "%s: Грешка читања назива датотека: %s"

#: src/xz/main.c
#, c-format
msgid "%s: Unexpected end of input when reading filenames"
msgstr "%s: Неочекивани крај улаза приликом читања назива датотека"

#: src/xz/main.c
#, c-format
msgid "%s: Null character found when reading filenames; maybe you meant to use '--files0' instead of '--files'?"
msgstr "%s: Нађох ништаван знак приликом читања назива датотека; можда сте хтели да користите „--files0“ уместо „--files“?"

#: src/xz/main.c
msgid "Compression and decompression with --robot are not supported yet."
msgstr "Запакивање и распакивање са „--robot“ није још подржано."

#: src/xz/main.c
msgid "Cannot read data from standard input when reading filenames from standard input"
msgstr "Не могу да читам податке са стандардног улаза приликом читања назива датотека са стандардног улаза"

#. TRANSLATORS: This is the program name in the beginning
#. of the line in messages. Usually it becomes "xz: ".
#. This is a translatable string because French needs
#. a space before a colon.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s: "
msgstr "%s: "

#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "Internal error (bug)"
msgstr "Унутрашња грешка (бубица)"

#: src/xz/message.c
msgid "Cannot establish signal handlers"
msgstr "Не могу да успоставим руковаоце сигналом"

#: src/xz/message.c
msgid "No integrity check; not verifying file integrity"
msgstr "Нема провере целовитости; не проверавам целовитост датотеке"

#: src/xz/message.c
msgid "Unsupported type of integrity check; not verifying file integrity"
msgstr "Неподржана врста провере целовитости; не проверавам целовитост датотеке"

#: src/xz/message.c
msgid "Memory usage limit reached"
msgstr "Ограничење коришћења меморије је достигнуто"

#: src/xz/message.c
msgid "File format not recognized"
msgstr "Није препознат формат датотеке"

#: src/xz/message.c
msgid "Unsupported options"
msgstr "Неподржане опције"

#: src/xz/message.c
msgid "Compressed data is corrupt"
msgstr "Запаковани подаци су оштећени"

#: src/xz/message.c
msgid "Unexpected end of input"
msgstr "Неочекиван крај улаза"

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limiter is disabled."
msgstr "%s MiB меморије је потребно. Ограничавач је онемогућен."

#: src/xz/message.c
#, c-format
msgid "%s MiB of memory is required. The limit is %s."
msgstr "%s MiB меморије је потребно. Ограничење је %s."

#: src/xz/message.c
#, c-format
msgid "%s: Filter chain: %s\n"
msgstr "%s: Ланац филтера: %s\n"

#: src/xz/message.c
#, c-format
msgid "Try '%s --help' for more information."
msgstr "Пробајте „%s --help“ за више података."

#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Error printing the help text (error code %d)"
msgstr "Грешка исписивања текста помоћи (код грешке %d)"

#: src/xz/message.c
#, c-format
msgid "Usage: %s [OPTION]... [FILE]...\n"
msgstr "Коришћење: %s [ОПЦИЈА]... [ДАТОТЕКА]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Compress or decompress FILEs in the .xz format."
msgstr "Пакује или распакује ДАТОТЕКЕ у „.xz“ формату."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Mandatory arguments to long options are mandatory for short options too."
msgstr "Обавезни аргументи за дуге опције су такође обавезни и за кратке опције."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation mode:"
msgstr "Режим рада:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force compression"
msgstr "приморава запакивање"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force decompression"
msgstr "приморава распакивање"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "test compressed file integrity"
msgstr "испробава целовитост запаковане датотеке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "list information about .xz files"
msgstr "исписује информације о „.xz“ даттотекама"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Operation modifiers:"
msgstr "Измењивачи рада:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "keep (don't delete) input files"
msgstr "задржава (не брише) улазне датотеке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "force overwrite of output file and (de)compress links"
msgstr "приморава преписивање излазне датотеке и (рас)пакује везе"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "write to standard output and don't delete input files"
msgstr "пише на стандардни излаз и не брише улазне датотеке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't synchronize the output file to the storage device before removing the input file"
msgstr "не усклађује излазну датотеку са смештајним уређајем пре уклањања улазне датотеке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "decompress only the first stream, and silently ignore possible remaining input data"
msgstr "распакује само први ток, и тихо занемарује могуће преостале улазне податке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "do not create sparse files when decompressing"
msgstr "не ствара привидне датотеке када распакује"

#: src/xz/message.c
msgid ".SUF"
msgstr ".SUF"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use the suffix '.SUF' on compressed files"
msgstr "користи суфикс „.SUF“ на запакованим датотекама"

#: src/xz/message.c
msgid "FILE"
msgstr "ДАТОТЕКА"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "read filenames to process from FILE; if FILE is omitted, filenames are read from the standard input; filenames must be terminated with the newline character"
msgstr "чита називе датотека процесу из ДАТОТЕКА; ако је ДАТОТЕКА изостављено, називи датотека се читају са стандардног улаза; називи датотека се морају завршавати знаком новог реда"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "like --files but use the null character as terminator"
msgstr "исто као „--files“ али користи знак ништице као окончавач"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Basic file format and compression options:"
msgstr "Основне опције формата датотеке и запакивања:"

#: src/xz/message.c
msgid "FORMAT"
msgstr "ФОРМАТ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "file format to encode or decode; possible values are 'auto' (default), 'xz', 'lzma', 'lzip', and 'raw'"
msgstr "формат датотеке за шифровање или дешифровање; могуће вредности су „auto“ (основно), xz, lzma, lzip, и raw"

#: src/xz/message.c
msgid "NAME"
msgstr "НАЗИВ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "integrity check type: 'none' (use with caution), 'crc32', 'crc64' (default), or 'sha256'"
msgstr "врста провере целовитости: „none“ (користите уз опрез), „crc32“, „crc64“ (основно), или „sha256“"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "don't verify the integrity check when decompressing"
msgstr "не потврђује проверу целовитости приликом распакивања"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression preset; default is 6; take compressor *and* decompressor memory usage into account before using 7-9!"
msgstr "претподешавање запакивања; основно је 6; узмите у обзир коришћење меморије запакивања *и* распакивања пре него ли употребите 7-9!"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "try to improve compression ratio by using more CPU time; does not affect decompressor memory requirements"
msgstr "покушава да побољша однос запакивања користећи више времена процесора; не утиче на потребе меморије распакивача"

#. TRANSLATORS: Short for NUMBER. A longer string is fine but
#. wider than 5 columns makes --long-help a few lines longer.
#: src/xz/message.c
msgid "NUM"
msgstr "БРОЈ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use at most NUM threads; the default is 0 which uses as many threads as there are processor cores"
msgstr "користи највише БРОЈ нити; основно је 0 за коришћење онолико нити колико има језгара процесора"

#: src/xz/message.c
msgid "SIZE"
msgstr "ВЕЛИЧИНА"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after every SIZE bytes of input; use this to set the block size for threaded compression"
msgstr "започиње нови „.xz“ блок након свака ВЕЛИЧИНА бајта улаза; користите ово да поставите величину блока за нитирано запакивање"

#: src/xz/message.c
msgid "BLOCKS"
msgstr "БЛОКОВИ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start a new .xz block after the given comma-separated intervals of uncompressed data; optionally, specify a filter chain number (0-9) followed by a ':' before the uncompressed data size"
msgstr "започиње нови „.xz“ блок након датих зарезом одвојених интервала несажетих података; изборно, наводи број ланца филтера (0-9) за којим следе „:“ пре величине несажетих података"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "when compressing, if more than NUM milliseconds has passed since the previous flush and reading more input would block, all pending data is flushed out"
msgstr "приликом запакивања, ако је прошло више од ВРЕМЕ_ИСТЕКА милисекунди до претходног убацивања и читања још улаза блокираће, сви подаци на чекању се истискују ван"

#: src/xz/message.c
msgid "LIMIT"
msgstr "ОГРАНИЧЕЊЕ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, no-c-format
msgid "set memory usage limit for compression, decompression, threaded decompression, or all of these; LIMIT is in bytes, % of RAM, or 0 for defaults"
msgstr "поставља ограничење коришћења меморије за запакивање, распакивање, распакивање нити или за све ово; ОГРАНИЧЕЊЕ је у бајтовима, % РАМ-а или 0 за основно"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "if compression settings exceed the memory usage limit, give an error instead of adjusting the settings downwards"
msgstr "ако подешавања запакивања пређу ограничење коришћења меморије, даје грешку уместо дотеривања подешавања"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Custom filter chain for compression (an alternative to using presets):"
msgstr "Произвољни ланац филтера за запакивање (алтернатива за коришћење предподешавања):"

#: src/xz/message.c
msgid "FILTERS"
msgstr "ФИЛТЕРИ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set the filter chain using the liblzma filter string syntax; use --filters-help for more information"
msgstr "поставља ланац филтера користећи синтаксу ниске „liblzma“ филтера; користите „--filters-help“ за више информација"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "set additional filter chains using the liblzma filter string syntax to use with --block-list"
msgstr "поставља додатне ланце филтера користећи синтаксу ниске „liblzma“ филтера за коришћење са „--block-list“"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display more information about the liblzma filter string syntax and exit"
msgstr "приказује више информација о синтакси ниске „liblzma“ филтера и излази"

#. TRANSLATORS: Short for OPTIONS.
#: src/xz/message.c
msgid "OPTS"
msgstr "ОПЦИЈЕ"

#. TRANSLATORS: Use semicolon (or its fullwidth form)
#. in "(valid values; default)" even if it is weird in
#. your language. There are non-translatable strings
#. that look like "(foo, bar, baz; foo)" which list
#. the supported values and the default value.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "LZMA1 or LZMA2; OPTS is a comma-separated list of zero or more of the following options (valid values; default):"
msgstr "LZMA1 или LZMA2; ОПЦИЈЕ је зарезом раздвојен списак нула или више следећих опција (исправне вредности; основно):"

#. TRANSLATORS: Short for PRESET. A longer string is
#. fine but wider than 4 columns makes --long-help
#. one line longer.
#: src/xz/message.c
msgid "PRE"
msgstr "ПРЕ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "reset options to a preset"
msgstr "враћа опције на предподешеност"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "dictionary size"
msgstr "величина речника"

#. TRANSLATORS: The word "literal" in "literal context
#. bits" means how many "context bits" to use when
#. encoding literals. A literal is a single 8-bit
#. byte. It doesn't mean "literally" here.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal context bits"
msgstr "број битова контекста литерала"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of literal position bits"
msgstr "број битова положаја литерала"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "number of position bits"
msgstr "број битова положаја"

#: src/xz/message.c
msgid "MODE"
msgstr "РЕЖИМ"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "compression mode"
msgstr "режим запакивања"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "nice length of a match"
msgstr "фина дужина поклапања"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "match finder"
msgstr "поклапа налазача"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "maximum search depth; 0=automatic (default)"
msgstr "највећа дубина претраге; 0=аутоматски (основно)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "x86 BCJ filter (32-bit and 64-bit)"
msgstr "x86 BCJ филтер (32-бита и 64-бита)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM BCJ filter"
msgstr "ARM BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM-Thumb BCJ filter"
msgstr "ARM-Thumb BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "ARM64 BCJ filter"
msgstr "ARM64 BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "PowerPC BCJ filter (big endian only)"
msgstr "PowerPC BCJ филтер (само велика крајност)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "IA-64 (Itanium) BCJ filter"
msgstr "IA-64 (Itanium) BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "SPARC BCJ filter"
msgstr "SPARC BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "RISC-V BCJ filter"
msgstr "RISC-V BCJ филтер"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Valid OPTS for all BCJ filters:"
msgstr "Исправне ОПЦИЈЕ за све BCJ филтере:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "start offset for conversions (default=0)"
msgstr "померај почетка за претварање (основно=0)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Delta filter; valid OPTS (valid values; default):"
msgstr "Делта филтер; исправне ОПЦИЈЕ (исправне вредности; основно):"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "distance between bytes being subtracted from each other"
msgstr "растојање између бајтова који су одузети један од другог"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "Other options:"
msgstr "Остале опције:"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "suppress warnings; specify twice to suppress errors too"
msgstr "потискује упозорења; наведите два пута да би потискивао и грешке"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "be verbose; specify twice for even more verbose"
msgstr "бива опширан; наведите два пута за још више опширности"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "make warnings not affect the exit status"
msgstr "чини да упозорења не делују на стање излаза"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "use machine-parsable messages (useful for scripts)"
msgstr "користи поруке обрадиве рачунаром (корисно за скрипте)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the total amount of RAM and the currently active memory usage limits, and exit"
msgstr "приказује укупан износ РАМ-а и тренутно ограничење коришћења активне меморије, и излази"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the short help (lists only the basic options)"
msgstr "приказује кратку помоћ (исписује само основне опције)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this long help and exit"
msgstr "приказује ову опширну помоћ и излази"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display this short help and exit"
msgstr "приказује ову кратку помоћ и излази"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the long help (lists also the advanced options)"
msgstr "приказује дугу помоћ (исписује такође и напредне опције)"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "display the version number and exit"
msgstr "приказује број издања и излази"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
msgid "With no FILE, or when FILE is -, read standard input."
msgstr "Без ДАТОТЕКЕ, или када је ДАТОТЕКА -, чита стандардни улаз."

#. TRANSLATORS: This message indicates the bug reporting
#. address for this package. Please add another line saying
#. "\nReport translation bugs to <...>." with the email or WWW
#. address for translation bugs. Thanks!
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "Report bugs to <%s> (in English or Finnish)."
msgstr "Грешке пријавите на <%s> (на енглеском или финском)."

#. TRANSLATORS: The first %s is the name of this software.
#. The second <%s> is an URL.
#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c src/lzmainfo/lzmainfo.c
#, c-format
msgid "%s home page: <%s>"
msgstr "„%s“ матична страница: <%s>"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "THIS IS A DEVELOPMENT VERSION NOT INTENDED FOR PRODUCTION USE."
msgstr "ОВО ЈЕ РАЗВОЈНО ИЗДАЊЕ И НИЈЕ НАМЕЊЕНО ЗА ПРОФЕСИОНАЛНУ УПОТРЕБУ."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
#, c-format
msgid "Filter chains are set using the --filters=FILTERS or --filters1=FILTERS ... --filters9=FILTERS options. Each filter in the chain can be separated by spaces or '--'. Alternatively a preset %s can be specified instead of a filter chain."
msgstr "Ланци филтера су постављени са опцијама „--filters=ФИЛТЕРИ“ или „--filters1=ФИЛТЕРИ“ ... „--filters9=ФИЛТЕРИ“. Сваки филтер у ланцу се може одвојити размацима или „--“. Или предподешеност „%s“ се може навести уместо ланца филтера."

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/xz/message.c
msgid "The supported filters and their options are:"
msgstr "Подржани филтери и њихове опције су:"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Options must be 'name=value' pairs separated with commas"
msgstr "Опције морају бити парови „назив=вредност“ раздвојени зарезима"

#: src/xz/options.c
#, c-format
msgid "%s: Invalid option name"
msgstr "%s: Неисправан назив опције"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "Invalid option value"
msgstr "Неисправна вредност опције"

#: src/xz/options.c
#, c-format
msgid "Unsupported LZMA1/LZMA2 preset: %s"
msgstr "Неподржано претподешавање „LZMA1/LZMA2“: %s"

#: src/xz/options.c src/liblzma/common/string_conversion.c
msgid "The sum of lc and lp must not exceed 4"
msgstr "Збир „lc“ и „lp“ не сме премашити 4"

#: src/xz/suffix.c
#, c-format
msgid "%s: Filename has an unknown suffix, skipping"
msgstr "%s: Назив датотеке има непознат суфикс, прескачем"

#: src/xz/suffix.c
#, c-format
msgid "%s: File already has '%s' suffix, skipping"
msgstr "%s: Датотека већ има суфикс „%s“, прескачем"

#: src/xz/suffix.c
#, c-format
msgid "%s: Invalid filename suffix"
msgstr "%s: Неисправан суфикс назива датотеке"

#: src/xz/util.c src/liblzma/common/string_conversion.c
msgid "Value is not a non-negative decimal integer"
msgstr "Вредност није не-негативан децимални цео број"

#: src/xz/util.c
#, c-format
msgid "%s: Invalid multiplier suffix"
msgstr "%s: Неисправан суфикс умножавача"

#: src/xz/util.c
msgid "Valid suffixes are 'KiB' (2^10), 'MiB' (2^20), and 'GiB' (2^30)."
msgstr "Исправни суфикси су KiB (2^10), MiB (2^20), и GiB (2^30)."

#: src/xz/util.c
#, c-format
msgid "Value of the option '%s' must be in the range [%<PRIu64>, %<PRIu64>]"
msgstr "Вредност опције „%s“ мора бити у опсегу [%<PRIu64>, %<PRIu64>]"

#: src/xz/util.c
msgid "Compressed data cannot be read from a terminal"
msgstr "Запаковани подаци се не могу читати из терминала"

#: src/xz/util.c
msgid "Compressed data cannot be written to a terminal"
msgstr "Запаковани подаци се не могу писати на терминал"

#: src/lzmainfo/lzmainfo.c
#, c-format
msgid "Usage: %s [--help] [--version] [FILE]...\n"
msgstr "Коришћење: %s [--help] [--version] [ДАТОТЕКА]...\n"

#. This is word wrapped at spaces. The Unicode character U+00A0 works as a non-breaking space. Tab (\t) is interpret as a zero-width space (the tab itself is not displayed); U+200B is NOT supported. Manual word wrapping with \n is supported but requires care.
#: src/lzmainfo/lzmainfo.c
msgid "Show information stored in the .lzma file header."
msgstr "Приказује информације смештене у заглављу „.lzma“ датотеке."

#: src/lzmainfo/lzmainfo.c
msgid "File is too small to be a .lzma file"
msgstr "Датотека је премала да би била „.lzma“ датотека"

#: src/lzmainfo/lzmainfo.c
msgid "Not a .lzma file"
msgstr "Није „.lzma“ датотека"

#: src/common/tuklib_exit.c
msgid "Writing to standard output failed"
msgstr "Писање на стандардни излаз није успело"

#: src/common/tuklib_exit.c
msgid "Unknown error"
msgstr "Непозната грешка"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported preset"
msgstr "Неподржано предподешавање"

#: src/liblzma/common/string_conversion.c
msgid "Unsupported flag in the preset"
msgstr "Неподржана заставица у предподешавању"

#: src/liblzma/common/string_conversion.c
msgid "Unknown option name"
msgstr "Непознат назив опције"

#: src/liblzma/common/string_conversion.c
msgid "Option value cannot be empty"
msgstr "Вредност опције не може бити празна"

#: src/liblzma/common/string_conversion.c
msgid "Value out of range"
msgstr "Вредност је ван опсега"

#: src/liblzma/common/string_conversion.c
msgid "This option does not support any multiplier suffixes"
msgstr "Ова опција не подржава никакве суфиксе умножавача"

#. TRANSLATORS: Don't translate the
#. suffixes "KiB", "MiB", or "GiB"
#. because a user can only specify
#. untranslated suffixes.
#: src/liblzma/common/string_conversion.c
msgid "Invalid multiplier suffix (KiB, MiB, or GiB)"
msgstr "Неисправан суфикс умножавача (KiB, MiB, или GiB)"

#: src/liblzma/common/string_conversion.c
msgid "Unknown filter name"
msgstr "Непознат назив филтера"

#: src/liblzma/common/string_conversion.c
msgid "This filter cannot be used in the .xz format"
msgstr "Филтер се не може користити у „.xz“ формату"

#: src/liblzma/common/string_conversion.c
msgid "Memory allocation failed"
msgstr "Додела меморије није успела"

#: src/liblzma/common/string_conversion.c
msgid "Empty string is not allowed, try '6' if a default value is needed"
msgstr "Празна ниска није дозвољена, пробајте 6 ако је основна вредност потребна"

#: src/liblzma/common/string_conversion.c
msgid "The maximum number of filters is four"
msgstr "Највећи број филтера је четири"

#: src/liblzma/common/string_conversion.c
msgid "Filter name is missing"
msgstr "Недостаје назив филтера"

#: src/liblzma/common/string_conversion.c
msgid "Invalid filter chain ('lzma2' missing at the end?)"
msgstr "Неисправан ланац филтера („lzma2“ недостаје на крају?)"
